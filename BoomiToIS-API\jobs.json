{"42d896d7-7c6b-4b99-9df0-accbba5b7cd5": {"id": "42d896d7-7c6b-4b99-9df0-accbba5b7cd5", "status": "completed", "created": "295f750d-34c7-11f0-81d5-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\42d896d7-7c6b-4b99-9df0-accbba5b7cd5\\IFlow_86564cda.zip", "debug": {"final_iflow_IFlow_86564cda.xml": "genai_debug\\final_iflow_IFlow_86564cda.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_86564cda", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_42d896d7", "package_id": "WithRequestReply", "iflow_name": "GeneratedIFlow_42d896d7"}}, "87939f41-9c18-4fe3-9282-63ef954901b3": {"id": "87939f41-9c18-4fe3-9282-63ef954901b3", "status": "completed", "created": "ea028ead-37e4-11f0-8c56-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\87939f41-9c18-4fe3-9282-63ef954901b3\\IFlow_be99636e.zip", "debug": {"final_iflow_IFlow_be99636e.xml": "genai_debug\\final_iflow_IFlow_be99636e.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_be99636e", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_87939f41", "package_id": "WithRequestReply", "iflow_name": "GeneratedIFlow_87939f41"}}, "a23b7ac1-ccda-41e5-944a-e1cf180b2b95": {"id": "a23b7ac1-ccda-41e5-944a-e1cf180b2b95", "status": "completed", "created": "fc405960-37e8-11f0-8653-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\a23b7ac1-ccda-41e5-944a-e1cf180b2b95\\IFlow_588fedd0.zip", "debug": {"final_iflow_IFlow_588fedd0.xml": "genai_debug\\final_iflow_IFlow_588fedd0.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_588fedd0", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_a23b7ac1", "package_id": "WithRequestReply", "iflow_name": "GeneratedIFlow_a23b7ac1"}}, "ab928466-fa24-4e70-bb23-e9770f9daf5f": {"id": "ab928466-fa24-4e70-bb23-e9770f9daf5f", "original_job_id": "cf3168dc-4d1a-4e5b-8b07-41931e18e48d", "status": "completed", "created": "aa6d9dce-46f3-11f0-88b0-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\ab928466-fa24-4e70-bb23-e9770f9daf5f\\IFlow_cf3168dc.zip", "debug": {"final_iflow_IFlow_cf3168dc.xml": "genai_debug\\final_iflow_IFlow_cf3168dc.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_cf3168dc"}, "a944399d-f46e-4cab-823f-9388deff5a40": {"id": "a944399d-f46e-4cab-823f-9388deff5a40", "status": "completed", "created": "e424d194-4b41-11f0-9feb-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\a944399d-f46e-4cab-823f-9388deff5a40\\IFlow_0b35c86c.zip", "debug": {"final_iflow_IFlow_0b35c86c.xml": "genai_debug\\final_iflow_IFlow_0b35c86c.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_0b35c86c"}, "4667ac11-be2c-47d8-ad82-e9857893977b": {"id": "4667ac11-be2c-47d8-ad82-e9857893977b", "status": "completed", "created": "b68e5d9d-4b4c-11f0-b7c0-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\4667ac11-be2c-47d8-ad82-e9857893977b\\IFlow_4be854cb.zip", "debug": {"final_iflow_IFlow_4be854cb.xml": "genai_debug\\final_iflow_IFlow_4be854cb.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_4be854cb", "deployment_status": "deploying", "deployment_message": "Deploying to SAP Integration Suite using direct deployment..."}, "4d464a0b-d794-49e8-9b9e-61068673c014": {"id": "4d464a0b-d794-49e8-9b9e-61068673c014", "status": "completed", "created": "950aaedc-4b4d-11f0-8634-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\4d464a0b-d794-49e8-9b9e-61068673c014\\IFlow_e70f7fc5.zip", "debug": {"final_iflow_IFlow_e70f7fc5.xml": "genai_debug\\final_iflow_IFlow_e70f7fc5.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_e70f7fc5", "deployment_status": "failed", "deployment_message": "Deployment failed: Failed to deploy iFlow. Response: Unauthorized", "deployment_details": {"status": "error", "message": "Failed to deploy iFlow. Response: Unauthorized"}}, "c8c471bf-2960-4ada-8c1e-95218cb693fb": {"id": "c8c471bf-2960-4ada-8c1e-95218cb693fb", "status": "completed", "created": "f6588e94-4b50-11f0-81f3-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\c8c471bf-2960-4ada-8c1e-95218cb693fb\\IFlow_063df8a9.zip", "debug": {"final_iflow_IFlow_063df8a9.xml": "genai_debug\\final_iflow_IFlow_063df8a9.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_063df8a9", "deployment_status": "failed", "deployment_message": "Deployment failed: Failed to deploy iFlow. Response: Unauthorized", "deployment_details": {"status": "error", "message": "Failed to deploy iFlow. Response: Unauthorized"}}, "bffaec08-99ae-4043-b30e-9fe91b879e32": {"id": "bffaec08-99ae-4043-b30e-9fe91b879e32", "status": "completed", "created": "82d5deb9-4b5c-11f0-93f1-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\bffaec08-99ae-4043-b30e-9fe91b879e32\\IFlow_b3962ca7.zip", "debug": {"final_iflow_IFlow_b3962ca7.xml": "genai_debug\\final_iflow_IFlow_b3962ca7.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_b3962ca7", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_bffaec08", "package_id": "ConversionPackages", "iflow_name": "GeneratedIFlow_bffaec08", "response_code": 201, "method": "Bearer + <PERSON><PERSON>ers"}}, "01af1604-b029-42fc-b7cf-a4a75191e1d5": {"id": "01af1604-b029-42fc-b7cf-a4a75191e1d5", "status": "processing", "created": "5f73a6db-4b99-11f0-83a2-743af40c1803", "message": "Analyzing markdown and generating iFlow..."}, "64d3079f-21f3-423c-b645-424692e8da80": {"id": "64d3079f-21f3-423c-b645-424692e8da80", "status": "processing", "created": "d56e66dd-4b99-11f0-83bc-743af40c1803", "message": "Analyzing markdown and generating iFlow..."}, "0d1bb2b6-d01a-485b-8947-f259ba7f3dd1": {"id": "0d1bb2b6-d01a-485b-8947-f259ba7f3dd1", "status": "completed", "created": "50c5815f-4b9a-11f0-8a16-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\0d1bb2b6-d01a-485b-8947-f259ba7f3dd1\\IFlow_5a2a2efa.zip", "debug": {"final_iflow_IFlow_5a2a2efa.xml": "genai_debug\\final_iflow_IFlow_5a2a2efa.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_5a2a2efa", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_0d1bb2b6", "package_id": "ConversionPackages", "iflow_name": "GeneratedIFlow_0d1bb2b6", "response_code": 201, "method": "Bearer + <PERSON><PERSON>ers"}}, "abfe64c9-591b-43f7-ad91-353bcf76bba2": {"id": "abfe64c9-591b-43f7-ad91-353bcf76bba2", "status": "completed", "created": "d6c795cc-4c32-11f0-a08a-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\abfe64c9-591b-43f7-ad91-353bcf76bba2\\IFlow_1bf097b1.zip", "debug": {"final_iflow_IFlow_1bf097b1.xml": "genai_debug\\final_iflow_IFlow_1bf097b1.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_1bf097b1"}, "1d1c7581-3daa-47f1-876c-5cf301034846": {"id": "1d1c7581-3daa-47f1-876c-5cf301034846", "status": "completed", "created": "0eb758a7-4c43-11f0-a3ff-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\1d1c7581-3daa-47f1-876c-5cf301034846\\IFlow_b677f872.zip", "debug": {"final_iflow_IFlow_b677f872.xml": "genai_debug\\final_iflow_IFlow_b677f872.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_b677f872", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_1d1c7581", "package_id": "ConversionPackages", "iflow_name": "GeneratedIFlow_1d1c7581", "response_code": 201, "method": "Bearer + <PERSON><PERSON>ers"}}, "2c5e0912-ef89-400f-8768-e70aaac0c594": {"id": "2c5e0912-ef89-400f-8768-e70aaac0c594", "status": "completed", "created": "7ab78823-4cf2-11f0-a065-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\2c5e0912-ef89-400f-8768-e70aaac0c594\\IFlow_e6d0c653.zip", "debug": {"final_iflow_IFlow_e6d0c653.xml": "genai_debug\\final_iflow_IFlow_e6d0c653.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt"}}, "iflow_name": "IFlow_e6d0c653", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_2c5e0912", "package_id": "ConversionPackages", "iflow_name": "GeneratedIFlow_2c5e0912", "response_code": 201, "method": "Bearer + <PERSON><PERSON>ers"}}, "e6ed5dd5-fb7c-460d-bd09-afd72b0917f0": {"id": "e6ed5dd5-fb7c-460d-bd09-afd72b0917f0", "status": "completed", "created": "c069a3eb-4d12-11f0-910e-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\e6ed5dd5-fb7c-460d-bd09-afd72b0917f0\\IFlow_c5e48c6e.zip", "debug": {"final_iflow_IFlow_c5e48c6e.xml": "genai_debug\\final_iflow_IFlow_c5e48c6e.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt", "raw_analysis_response_attempt1.txt": "genai_debug\\raw_analysis_response_attempt1.txt", "raw_analysis_response_attempt2.txt": "genai_debug\\raw_analysis_response_attempt2.txt"}}, "iflow_name": "IFlow_c5e48c6e", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_e6ed5dd5", "package_id": "ConversionPackages", "iflow_name": "GeneratedIFlow_e6ed5dd5", "response_code": 201, "method": "Bearer + <PERSON><PERSON>ers"}}, "4f19ccbe-24c5-4008-8734-e19433959a8e": {"id": "4f19ccbe-24c5-4008-8734-e19433959a8e", "status": "failed", "created": "0fe934b8-4d16-11f0-975b-743af40c1803", "message": "Error generating iFlow: 'EnhancedIFlowTemplates' object has no attribute 'successfactors_receiver_participant_template'"}, "f1e2c6df-fd8e-4d63-be4a-f8161ae89de4": {"id": "f1e2c6df-fd8e-4d63-be4a-f8161ae89de4", "status": "failed", "created": "7bb90dc2-4d16-11f0-81e9-743af40c1803", "message": "Error generating iFlow: 'EnhancedIFlowTemplates' object has no attribute 'successfactors_receiver_participant_template'"}, "941c3752-3026-4d0b-a60e-b2afaf54eee9": {"id": "941c3752-3026-4d0b-a60e-b2afaf54eee9", "status": "failed", "created": "a243640c-4d18-11f0-90ce-743af40c1803", "message": "Error generating iFlow: 'str' object has no attribute 'get'"}, "17e3def8-556f-4aa1-893d-1ef12d9e1677": {"id": "17e3def8-556f-4aa1-893d-1ef12d9e1677", "status": "completed", "created": "abe8c6ea-4d1d-11f0-b086-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\17e3def8-556f-4aa1-893d-1ef12d9e1677\\IFlow_a31d4192.zip", "debug": {"final_iflow_IFlow_a31d4192.xml": "genai_debug\\final_iflow_IFlow_a31d4192.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt", "raw_analysis_response_attempt1.txt": "genai_debug\\raw_analysis_response_attempt1.txt", "raw_analysis_response_attempt2.txt": "genai_debug\\raw_analysis_response_attempt2.txt"}}, "iflow_name": "IFlow_a31d4192", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_17e3def8", "package_id": "ConversionPackages", "iflow_name": "GeneratedIFlow_17e3def8", "response_code": 201, "method": "Bearer + <PERSON><PERSON>ers"}}, "b6fe9bc6-4952-406e-92b5-326b326b7de7": {"id": "b6fe9bc6-4952-406e-92b5-326b326b7de7", "status": "completed", "created": "0d335719-4ddb-11f0-8189-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\b6fe9bc6-4952-406e-92b5-326b326b7de7\\IFlow_3b846bdc.zip", "debug": {"final_iflow_IFlow_3b846bdc.xml": "genai_debug\\final_iflow_IFlow_3b846bdc.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt", "raw_analysis_response_attempt1.txt": "genai_debug\\raw_analysis_response_attempt1.txt", "raw_analysis_response_attempt2.txt": "genai_debug\\raw_analysis_response_attempt2.txt"}}, "iflow_name": "IFlow_3b846bdc", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_b6fe9bc6", "package_id": "ConversionPackages", "iflow_name": "GeneratedIFlow_b6fe9bc6", "response_code": 201, "method": "Bearer + <PERSON><PERSON>ers"}}, "ad37123e-f9dd-478b-bbda-5a710bdd970d": {"id": "ad37123e-f9dd-478b-bbda-5a710bdd970d", "status": "completed", "created": "1747921f-4ddd-11f0-be0c-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\ad37123e-f9dd-478b-bbda-5a710bdd970d\\IFlow_0d122826.zip", "debug": {"final_iflow_IFlow_0d122826.xml": "genai_debug\\final_iflow_IFlow_0d122826.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt", "raw_analysis_response_attempt1.txt": "genai_debug\\raw_analysis_response_attempt1.txt", "raw_analysis_response_attempt2.txt": "genai_debug\\raw_analysis_response_attempt2.txt"}}, "iflow_name": "IFlow_0d122826", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_ad37123e", "package_id": "ConversionPackages", "iflow_name": "GeneratedIFlow_ad37123e", "response_code": 201, "method": "Bearer + <PERSON><PERSON>ers"}}, "e0ba3ee4-68eb-4809-84e6-a15330fb4994": {"id": "e0ba3ee4-68eb-4809-84e6-a15330fb4994", "status": "completed", "created": "89c80511-4de4-11f0-b8af-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\e0ba3ee4-68eb-4809-84e6-a15330fb4994\\IFlow_981efaf7.zip", "debug": {"final_iflow_IFlow_981efaf7.xml": "genai_debug\\final_iflow_IFlow_981efaf7.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt", "raw_analysis_response_attempt1.txt": "genai_debug\\raw_analysis_response_attempt1.txt", "raw_analysis_response_attempt2.txt": "genai_debug\\raw_analysis_response_attempt2.txt"}}, "iflow_name": "IFlow_981efaf7", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_e0ba3ee4", "package_id": "ConversionPackages", "iflow_name": "GeneratedIFlow_e0ba3ee4", "response_code": 201, "method": "Bearer + <PERSON><PERSON>ers"}}, "c3980524-f5e1-42f0-a27c-21383eb4a275": {"id": "c3980524-f5e1-42f0-a27c-21383eb4a275", "status": "completed", "created": "15de987d-4deb-11f0-b479-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\c3980524-f5e1-42f0-a27c-21383eb4a275\\IFlow_d9fd3dbf.zip", "debug": {"final_iflow_IFlow_d9fd3dbf.xml": "genai_debug\\final_iflow_IFlow_d9fd3dbf.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt", "raw_analysis_response_attempt1.txt": "genai_debug\\raw_analysis_response_attempt1.txt", "raw_analysis_response_attempt2.txt": "genai_debug\\raw_analysis_response_attempt2.txt"}}, "iflow_name": "IFlow_d9fd3dbf", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_c3980524", "package_id": "ConversionPackages", "iflow_name": "GeneratedIFlow_c3980524", "response_code": 201, "method": "Bearer + <PERSON><PERSON>ers"}}, "c37c13dd-7ad8-4080-a1e8-bdf695caf25a": {"id": "c37c13dd-7ad8-4080-a1e8-bdf695caf25a", "status": "completed", "created": "0aa70955-5028-11f0-8b63-743af40c1803", "message": "iFlow generation completed successfully!", "files": {"zip": "results\\c37c13dd-7ad8-4080-a1e8-bdf695caf25a\\IFlow_639be49b.zip", "debug": {"final_iflow_IFlow_639be49b.xml": "genai_debug\\final_iflow_IFlow_639be49b.xml", "raw_analysis_response.txt": "genai_debug\\raw_analysis_response.txt", "raw_analysis_response_attempt1.txt": "genai_debug\\raw_analysis_response_attempt1.txt", "raw_analysis_response_attempt2.txt": "genai_debug\\raw_analysis_response_attempt2.txt"}}, "iflow_name": "IFlow_639be49b", "deployment_status": "completed", "deployment_message": "iFlow deployed successfully", "deployment_details": {"status": "success", "message": "iFlow deployed successfully", "iflow_id": "GeneratedIFlow_c37c13dd", "package_id": "ConversionPackages", "iflow_name": "GeneratedIFlow_c37c13dd", "response_code": 201, "method": "Bearer + <PERSON><PERSON>ers"}}}