<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation with Mermaid</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #1565c0;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            background: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
        }
        pre.mermaid {
            text-align: center;
            background: white;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .note {
            background: #e3f2fd;
            padding: 10px;
            border-left: 4px solid #1565c0;
            margin: 10px 0;
        }
        .insights {
            background: #f3e5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .best-practices {
            background: #e8f5e9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .security {
            background: #ffebee;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1 id="create-salesforce-opportunities-from-stripe-subscriptions-integration">Create Salesforce Opportunities from Stripe Subscriptions Integration</h1>
<h2 id="table-of-contents">Table of Contents</h2>
<ul>
<li><a href="#create-salesforce-opportunities-from-stripe-subscriptions-integration">Create Salesforce Opportunities from Stripe Subscriptions Integration</a></li>
<li><a href="#table-of-contents">Table of Contents</a></li>
<li><a href="#api-overview">API Overview</a></li>
<li><a href="#endpoints">Endpoints</a></li>
<li><a href="#current-dell-boomi-flow-logic">Current Dell Boomi Flow Logic</a><ul>
<li><a href="#process-flow-overview">Process Flow Overview</a></li>
<li><a href="#detailed-flow-steps">Detailed Flow Steps</a></li>
</ul>
</li>
<li><a href="#dataweave-transformations-explained">DataWeave Transformations Explained</a></li>
<li><a href="#sap-integration-suite-implementation">SAP Integration Suite Implementation</a><ul>
<li><a href="#component-mapping">Component Mapping</a></li>
<li><a href="#integration-flow-visualization">Integration Flow Visualization</a></li>
<li><a href="#configuration-details">Configuration Details</a></li>
</ul>
</li>
<li><a href="#environment-configuration">Environment Configuration</a></li>
<li><a href="#api-reference">API Reference</a></li>
</ul>
<h2 id="api-overview">API Overview</h2>
<p>This integration creates Salesforce Opportunities based on Stripe Subscription data. The integration listens for Stripe subscription events, processes the subscription data, and creates corresponding opportunity records in Salesforce.</p>
<ul>
<li><strong>Base URL</strong>: Determined by the Stripe webhook configuration</li>
<li><strong>Authentication</strong>: Likely uses API keys for both Stripe and Salesforce connections</li>
<li><strong>Rate Limiting</strong>: Subject to Stripe and Salesforce API rate limits</li>
<li><strong>General Response Format</strong>: JSON responses from both Stripe and Salesforce APIs</li>
</ul>
<h2 id="endpoints">Endpoints</h2>
<h3 id="stripe-webhook-endpoint">Stripe Webhook Endpoint</h3>
<ul>
<li><strong>HTTP Method</strong>: POST</li>
<li><strong>Purpose</strong>: Receives subscription events from Stripe</li>
<li><strong>Request Headers</strong>:</li>
<li><code>Content-Type</code>: application/json</li>
<li><code>Stripe-Signature</code>: Webhook signature for verification</li>
<li><strong>Request Body</strong>: Stripe event object containing subscription data</li>
<li><strong>Response</strong>: 200 OK for successful processing</li>
<li><strong>Error Handling</strong>: Returns appropriate HTTP status codes for errors</li>
</ul>
<h3 id="salesforce-opportunity-creation-endpoint">Salesforce Opportunity Creation Endpoint</h3>
<ul>
<li><strong>HTTP Method</strong>: POST</li>
<li><strong>Path</strong>: <code>/services/data/v[version]/sobjects/Opportunity</code></li>
<li><strong>Purpose</strong>: Creates new opportunity records in Salesforce</li>
<li><strong>Request Headers</strong>:</li>
<li><code>Authorization</code>: Bearer token for Salesforce authentication</li>
<li><code>Content-Type</code>: application/json</li>
<li><strong>Request Body</strong>: Transformed opportunity data from Stripe subscription</li>
<li><strong>Response</strong>: Salesforce API response with created record ID</li>
<li><strong>Error Handling</strong>: Returns Salesforce API error responses</li>
</ul>
<h2 id="current-dell-boomi-flow-logic">Current Dell Boomi Flow Logic</h2>
<h3 id="process-flow-overview">Process Flow Overview</h3>
<p>The Dell Boomi process "Create Salesforce Opportunities from Stripe Subscriptions" follows these main steps:
1. Starts with an event listener (shape1)
2. Sets dynamic properties (shape6)
3. Transforms the data (shape4)
4. Processes the transformed data (shape3)
5. Ends the process (shape5)</p>
<h3 id="detailed-flow-steps">Detailed Flow Steps</h3>
<ol>
<li><strong>Event Listener (shape1)</strong>:</li>
<li>Listens for Stripe subscription events</li>
<li>
<p>Triggers the integration process when a subscription event is received</p>
</li>
<li>
<p><strong>Set Dynamic Properties (shape6)</strong>:</p>
</li>
<li>Configures runtime properties needed for the integration</li>
<li>
<p>May include Salesforce connection details, API versions, and other configuration parameters</p>
</li>
<li>
<p><strong>Data Transformation (shape4)</strong>:</p>
</li>
<li>Transforms the Stripe subscription data into Salesforce Opportunity format</li>
<li>Maps relevant fields from Stripe to Salesforce data model</li>
<li>
<p>Applies business logic to determine opportunity values</p>
</li>
<li>
<p><strong>Data Processing (shape3)</strong>:</p>
</li>
<li>Sends the transformed data to Salesforce</li>
<li>Creates new Opportunity records in Salesforce</li>
<li>
<p>Handles any response processing</p>
</li>
<li>
<p><strong>Process End (shape5)</strong>:</p>
</li>
<li>Completes the integration process</li>
<li>May include logging or notification of successful completion</li>
</ol>
<h2 id="dataweave-transformations-explained">DataWeave Transformations Explained</h2>
<p>Based on the provided documentation, specific DataWeave transformation details are not available. However, the transformation (shape4) would likely include:</p>
<ol>
<li><strong>Input Format</strong>: Stripe Subscription JSON data</li>
<li><strong>Output Format</strong>: Salesforce Opportunity JSON data</li>
<li><strong>Key Transformations</strong>:</li>
<li>Mapping Stripe subscription ID to Salesforce external ID</li>
<li>Converting subscription amount to opportunity amount</li>
<li>Setting appropriate opportunity stages based on subscription status</li>
<li>Mapping customer information to opportunity contact roles</li>
<li>Setting opportunity close dates based on subscription dates</li>
</ol>
<p>A typical transformation might include mapping subscription plan details, customer information, payment status, and subscription period to the corresponding Salesforce Opportunity fields.</p>
<h2 id="sap-integration-suite-implementation">SAP Integration Suite Implementation</h2>
<h3 id="component-mapping">Component Mapping</h3>
<table>
<thead>
<tr>
<th>Dell Boomi Component</th>
<th>SAP Integration Suite Equivalent</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td>Event Listener (shape1)</td>
<td>HTTPS Adapter (Webhook Receiver)</td>
<td>Configured to receive Stripe webhook events</td>
</tr>
<tr>
<td>Set Dynamic Properties (shape6)</td>
<td>Content Modifier</td>
<td>Used to set properties and headers for the integration flow</td>
</tr>
<tr>
<td>Transform (shape4)</td>
<td>Message Mapping or Script</td>
<td>Maps Stripe subscription data to Salesforce opportunity format</td>
</tr>
<tr>
<td>Data Processing (shape3)</td>
<td>HTTPS Adapter (Sender)</td>
<td>Configured to connect to Salesforce REST API</td>
</tr>
<tr>
<td>Process End (shape5)</td>
<td>End Event</td>
<td>Terminates the integration flow</td>
</tr>
</tbody>
</table>
<h3 id="integration-flow-visualization">Integration Flow Visualization</h3>
<pre class="mermaid">
flowchart TD
    %% Define node styles
    classDef httpAdapter fill:#87CEEB,stroke:#333,stroke-width:2px
    classDef contentModifier fill:#98FB98,stroke:#333,stroke-width:2px
    classDef router fill:#FFB6C1,stroke:#333,stroke-width:2px
    classDef mapping fill:#DDA0DD,stroke:#333,stroke-width:2px
    classDef exception fill:#FFA07A,stroke:#333,stroke-width:2px
    classDef processCall fill:#F0E68C,stroke:#333,stroke-width:2px

    %% Main Flow
    Start((Start)) --> StripeWebhook[HTTPS Adapter - Stripe Webhook]:::httpAdapter
    StripeWebhook --> SetProperties[Content Modifier - Set Properties]:::contentModifier
    SetProperties --> TransformData[Message Mapping - Transform to Opportunity]:::mapping
    TransformData --> SalesforceConnector[HTTPS Adapter - Salesforce API]:::httpAdapter
    SalesforceConnector --> End((End))

    %% Error Handling
    StripeWebhook -->|Error| ErrorHandler[(Error Handler)]:::exception
    SetProperties -->|Error| ErrorHandler
    TransformData -->|Error| ErrorHandler
    SalesforceConnector -->|Error| ErrorHandler
    ErrorHandler --> LogError[Content Modifier - Log Error]:::contentModifier
    LogError --> ErrorEnd((Error End))
</pre>
<h3 id="configuration-details">Configuration Details</h3>
<ol>
<li><strong>HTTPS Adapter - Stripe Webhook (StripeWebhook)</strong>:</li>
<li>Connection Type: HTTPS</li>
<li>URL: [Stripe Webhook URL]</li>
<li>Authentication: API Key</li>
<li>Method: POST</li>
<li>
<p>Headers:</p>
<ul>
<li>Content-Type: application/json</li>
<li>Accept: application/json</li>
</ul>
</li>
<li>
<p><strong>Content Modifier - Set Properties (SetProperties)</strong>:</p>
</li>
<li>Sets dynamic properties for the integration flow</li>
<li>
<p>Properties:</p>
<ul>
<li>salesforce.api.version: v53.0</li>
<li>salesforce.endpoint: [Salesforce Instance URL]</li>
<li>opportunity.recordTypeId: [Salesforce Record Type ID]</li>
</ul>
</li>
<li>
<p><strong>Message Mapping - Transform to Opportunity (TransformData)</strong>:</p>
</li>
<li>Source Format: JSON (Stripe Subscription)</li>
<li>Target Format: JSON (Salesforce Opportunity)</li>
<li>
<p>Mapping:</p>
<ul>
<li>subscription.id → ExternalId__c</li>
<li>subscription.plan.name → Name</li>
<li>subscription.amount → Amount</li>
<li>subscription.current_period_end → CloseDate</li>
<li>subscription.status → StageName (with value mapping)</li>
<li>subscription.customer.id → AccountId (with lookup)</li>
</ul>
</li>
<li>
<p><strong>HTTPS Adapter - Salesforce API (SalesforceConnector)</strong>:</p>
</li>
<li>Connection Type: HTTPS</li>
<li>URL: ${property.salesforce.endpoint}/services/data/${property.salesforce.api.version}/sobjects/Opportunity</li>
<li>Authentication: OAuth</li>
<li>Method: POST</li>
<li>
<p>Headers:</p>
<ul>
<li>Content-Type: application/json</li>
<li>Authorization: Bearer ${property.salesforce.token}</li>
</ul>
</li>
<li>
<p><strong>Error Handler (ErrorHandler)</strong>:</p>
</li>
<li>Captures and processes errors from all steps</li>
<li>Logs error details</li>
<li>May send notifications for critical errors</li>
</ol>
<h2 id="environment-configuration">Environment Configuration</h2>
<p>Based on the provided documentation, the following environment configuration details can be inferred:</p>
<ol>
<li><strong>API Connections</strong>:</li>
<li>Stripe API credentials</li>
<li>
<p>Salesforce API credentials (OAuth)</p>
</li>
<li>
<p><strong>Environment Variables</strong>:</p>
</li>
<li>Stripe API key</li>
<li>Salesforce OAuth client ID and secret</li>
<li>Salesforce instance URL</li>
<li>
<p>Salesforce API version</p>
</li>
<li>
<p><strong>Security Settings</strong>:</p>
</li>
<li>Secure storage of API keys and OAuth tokens</li>
<li>HTTPS for all communications</li>
<li>
<p>Webhook signature verification for Stripe events</p>
</li>
<li>
<p><strong>Deployment Considerations</strong>:</p>
</li>
<li>Ensure proper network connectivity to both Stripe and Salesforce</li>
<li>Configure appropriate timeouts for API calls</li>
<li>
<p>Set up monitoring for integration health</p>
</li>
<li>
<p><strong>Required Resources</strong>:</p>
</li>
<li>Standard integration runtime resources</li>
<li>Sufficient memory for processing subscription data</li>
<li>Network bandwidth for API communications</li>
</ol>
<h2 id="api-reference">API Reference</h2>
<h3 id="stripe-api">Stripe API</h3>
<ol>
<li><strong>Subscription Webhook Event</strong></li>
<li><strong>Method</strong>: POST</li>
<li><strong>Description</strong>: Webhook event sent by Stripe when subscription events occur</li>
<li><strong>Event Types</strong>:<ul>
<li>customer.subscription.created</li>
<li>customer.subscription.updated</li>
<li>customer.subscription.deleted</li>
</ul>
</li>
<li>
<p><strong>Documentation</strong>: <a href="https://stripe.com/docs/api/webhook_endpoints">Stripe Webhook API</a></p>
</li>
<li>
<p><strong>Subscription Object</strong></p>
</li>
<li><strong>Fields</strong>:<ul>
<li>id: Unique identifier for the subscription</li>
<li>customer: ID of the customer</li>
<li>status: Current status of the subscription</li>
<li>current_period_start: Start of the current period</li>
<li>current_period_end: End of the current period</li>
<li>plan: Details about the subscription plan</li>
<li>items: Line items in the subscription</li>
</ul>
</li>
<li><strong>Documentation</strong>: <a href="https://stripe.com/docs/api/subscriptions">Stripe Subscription API</a></li>
</ol>
<h3 id="salesforce-api">Salesforce API</h3>
<ol>
<li><strong>Create Opportunity</strong></li>
<li><strong>Method</strong>: POST</li>
<li><strong>Endpoint</strong>: /services/data/v{version}/sobjects/Opportunity</li>
<li><strong>Description</strong>: Creates a new Opportunity record in Salesforce</li>
<li><strong>Request Body</strong>:<ul>
<li>Name: Name of the opportunity</li>
<li>StageName: Stage of the opportunity</li>
<li>CloseDate: Expected close date</li>
<li>Amount: Opportunity amount</li>
<li>AccountId: ID of the related account</li>
<li>Additional custom fields as needed</li>
</ul>
</li>
<li><strong>Response</strong>: Returns the ID of the created Opportunity</li>
<li>
<p><strong>Documentation</strong>: <a href="https://developer.salesforce.com/docs/atlas.en-us.api_rest.meta/api_rest/intro_what_is_rest_api.htm">Salesforce REST API</a></p>
</li>
<li>
<p><strong>Authentication</strong></p>
</li>
<li><strong>Method</strong>: OAuth 2.0</li>
<li><strong>Grant Types</strong>: Authorization Code, JWT Bearer</li>
<li><strong>Token Endpoint</strong>: /services/oauth2/token</li>
<li><strong>Documentation</strong>: <a href="https://developer.salesforce.com/docs/atlas.en-us.api_rest.meta/api_rest/intro_understanding_authentication.htm">Salesforce OAuth</a></li>
</ol>
    
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>