{"process_name": "SAP SuccessFactors to SFTP Integration with Error Handling", "description": "This integration extracts employee contact data from SAP SuccessFactors, transforms it into the required format, and securely transfers it to a designated SFTP location with comprehensive error handling.", "endpoints": [{"method": "GET", "path": "/SuccessFactors/ContactData", "purpose": "Retrieves contact information from SuccessFactors and transfers it to SFTP", "components": [{"type": "odata", "name": "Get_SuccessFactors_ContactData", "id": "odata_successfactors_1", "config": {"address": "https://${property.SF_API_BASE_URL}/odata/v2", "resource_path": "ContactData", "operation": "Query(GET)", "query_options": ""}}, {"type": "groovy_script", "name": "Validate_SF_Response", "id": "validate_sf_data_1", "config": {"script": "ValidateSFResponse.groovy"}}, {"type": "groovy_script", "name": "Transform_Canonical_To_Kafka_Avro", "id": "transform_data_1", "config": {"script": "TransformCanonicalToKafkaAvro.groovy"}}, {"type": "request_reply", "name": "SFTP_Transfer", "id": "sftp_transfer_1", "config": {"endpoint_path": "/incoming/successfactors/contacts_${date:now:yyyyMMdd_HHmmss}.json"}}, {"type": "enricher", "name": "Log_Success_Message", "id": "log_success_1", "config": {"content": "{ \"status\": \"success\", \"timestamp\": \"${date:now:yyyy-MM-dd'T'HH:mm:ss.SSS}\", \"message\": \"Successfully transferred data to SFTP\" }"}}], "error_handling": {"exception_subprocess": [{"type": "enricher", "name": "Prepare_Error_Message", "id": "error_message_1", "trigger": "any_error", "config": {"content": "{ \"status\": \"error\", \"timestamp\": \"${date:now:yyyy-MM-dd'T'HH:mm:ss.SSS}\", \"errorType\": \"${property.ErrorType}\", \"errorMessage\": \"${property.ErrorMessage}\" }"}}, {"type": "request_reply", "name": "Send_Error_Notification", "id": "error_notification_1", "trigger": "any_error", "config": {"endpoint_path": "/notification/email"}}, {"type": "enricher", "name": "Log_Error_Details", "id": "log_error_1", "trigger": "any_error", "config": {"content": "{ \"status\": \"error\", \"timestamp\": \"${date:now:yyyy-MM-dd'T'HH:mm:ss.SSS}\", \"errorType\": \"${property.ErrorType}\", \"errorMessage\": \"${property.ErrorMessage}\", \"errorDetails\": \"${property.ErrorDetails}\" }"}}]}, "branching": {"type": "exclusive", "branches": [{"condition": "Valid data received from SuccessFactors", "components": ["transform_data_1", "sftp_transfer_1", "log_success_1"], "sequence": ["transform_data_1", "sftp_transfer_1", "log_success_1"]}]}, "sequence": ["odata_successfactors_1", "validate_sf_data_1", "transform_data_1", "sftp_transfer_1", "log_success_1"], "transformations": [{"name": "ValidateSFResponse.groovy", "type": "groovy", "script": "import com.sap.gateway.ip.core.customdev.util.Message;\n\ndef Message processData(Message message) {\n    def body = message.getBody(java.lang.String);\n    def jsonData = new groovy.json.JsonSlurper().parseText(body);\n    \n    // Validate required fields are present\n    if (!jsonData.d || !jsonData.d.results || jsonData.d.results.size() == 0) {\n        message.setProperty(\"ErrorType\", \"ValidationError\");\n        message.setProperty(\"ErrorMessage\", \"Missing or empty results in SuccessFactors response\");\n        throw new Exception(\"Validation Error: Missing or empty results\");\n    }\n    \n    // Check for required fields in each result\n    jsonData.d.results.each { contact ->\n        if (!contact.contactID || !contact.username) {\n            message.setProperty(\"ErrorType\", \"ValidationError\");\n            message.setProperty(\"ErrorMessage\", \"Missing required fields in contact data\");\n            throw new Exception(\"Validation Error: Missing required fields\");\n        }\n    }\n    \n    return message;\n}"}, {"name": "TransformCanonicalToKafkaAvro.groovy", "type": "groovy", "script": "import com.sap.gateway.ip.core.customdev.util.Message;\nimport groovy.json.*;\n\ndef Message processData(Message message) {\n    def body = message.getBody(java.lang.String);\n    def jsonData = new JsonSlurper().parseText(body);\n    def result = [:];\n    \n    // Create the target structure\n    result.batchProcessingDirectives = [:];\n    result.batchProcessingDirectives.accountID = [:];\n    result.batchProcessingDirectives.batchProcessingOption = [];\n    result.batchContactList = [];\n    \n    // Map username to accountID\n    if (jsonData.d && jsonData.d.results && jsonData.d.results.size() > 0) {\n        result.batchProcessingDirectives.accountID.username = jsonData.d.results[0].username;\n        \n        // Add batch processing options\n        def option = [:];\n        option.name = \"processAll\";\n        result.batchProcessingDirectives.batchProcessingOption.add(option);\n        \n        // Process each contact\n        jsonData.d.results.each { sfContact ->\n            def contactEntry = [:];\n            contactEntry.contact = [];\n            \n            def contact = [:];\n            contact.contactID = sfContact.contactID;\n            contact.contactPointList = [];\n            \n            // Process contact points if available\n            if (sfContact.contactPoints) {\n                sfContact.contactPoints.each { cp ->\n                    def contactPointEntry = [:];\n                    contactPointEntry.contactPoint = [];\n                    \n                    def contactPoint = [:];\n                    contactPoint.type = cp.type;\n                    contactPoint.value = cp.value;\n                    \n                    contactPointEntry.contactPoint.add(contactPoint);\n                    contact.contactPointList.add(contactPointEntry);\n                }\n            }\n            \n            contactEntry.contact.add(contact);\n            result.batchContactList.add(contactEntry);\n        }\n    }\n    \n    // Convert result to JSON\n    def jsonBuilder = new JsonBuilder(result);\n    message.setBody(jsonBuilder.toString());\n    \n    return message;\n}"}]}]}