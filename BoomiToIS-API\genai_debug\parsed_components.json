{"process_name": "Create Salesforce Opportunities from Stripe Subscriptions", "description": "This integration creates Salesforce Opportunities based on Stripe Subscription data. It listens for Stripe subscription events, processes the subscription data, and creates corresponding opportunity records in Salesforce.", "endpoints": [{"method": "POST", "path": "/webhook/stripe/subscriptions", "purpose": "Receives subscription events from Stripe and creates Salesforce Opportunities", "components": [{"type": "enricher", "name": "Set_Properties_From_<PERSON>i", "id": "enricher_1", "config": {"content": "{\n  \"salesforce.api.version\": \"v53.0\",\n  \"salesforce.endpoint\": \"${property.salesforce_instance_url}\",\n  \"opportunity.recordTypeId\": \"${property.opportunity_record_type_id}\"\n}"}}, {"type": "groovy_script", "name": "Transform_Stripe_To_Salesforce", "id": "transform_1", "config": {"script": "TransformStripeToSalesforce.groovy"}}, {"type": "request_reply", "name": "Create_Salesforce_Opportunity", "id": "request_reply_1", "config": {"endpoint_path": "/services/data/${property.salesforce.api.version}/sobjects/Opportunity", "address": "${property.salesforce.endpoint}"}}], "error_handling": {"exception_subprocess": [{"type": "enricher", "name": "Set_Error_Message", "id": "error_enricher_1", "trigger": "any_error", "config": {"content": "{\n  \"error\": true,\n  \"message\": \"Error processing Stripe subscription\",\n  \"details\": ${exception.message}\n}"}}, {"type": "groovy_script", "name": "Log_Error_Details", "id": "error_script_1", "trigger": "any_error", "config": {"script": "LogErrorDetails.groovy"}}]}, "branching": {"type": "exclusive", "branches": [{"condition": "Default path", "components": ["enricher_1", "transform_1", "request_reply_1"], "sequence": ["enricher_1", "transform_1", "request_reply_1"]}]}, "sequence": ["enricher_1", "transform_1", "request_reply_1"], "transformations": [{"name": "TransformStripeToSalesforce.groovy", "type": "groovy", "script": "import groovy.json.*\n\ndef inputBody = message.getBody(String.class)\ndef jsonSlurper = new JsonSlurper()\ndef subscription = jsonSlurper.parseText(inputBody)\n\ndef opportunity = [:]\n\n// Map Stripe subscription fields to Salesforce Opportunity fields\nopportunity.Name = subscription.plan?.name ?: 'Subscription ' + subscription.id\nopportunity.ExternalId__c = subscription.id\nopportunity.Amount = subscription.plan?.amount ? (subscription.plan.amount / 100) : 0\n\n// Convert Unix timestamp to date format\ndef closeDate = new Date(subscription.current_period_end * 1000)\ndef dateFormat = new java.text.SimpleDateFormat(\"yyyy-MM-dd\")\nopportunity.CloseDate = dateFormat.format(closeDate)\n\n// Map subscription status to opportunity stage\nswitch(subscription.status) {\n    case \"active\":\n        opportunity.StageName = \"Closed Won\"\n        break\n    case \"past_due\":\n        opportunity.StageName = \"Negotiation/Review\"\n        break\n    case \"unpaid\":\n        opportunity.StageName = \"Negotiation/Review\"\n        break\n    case \"canceled\":\n        opportunity.StageName = \"Closed Lost\"\n        break\n    case \"trialing\":\n        opportunity.StageName = \"Proposal/Price Quote\"\n        break\n    default:\n        opportunity.StageName = \"Qualification\"\n}\n\n// Set record type ID from property\nopportunity.RecordTypeId = property.get(\"opportunity.recordTypeId\")\n\n// Set AccountId if customer ID is available\nif (subscription.customer) {\n    // In a real implementation, you might need to look up the Salesforce Account ID\n    // based on the Stripe customer ID\n    opportunity.AccountId = \"${property.default_account_id}\"\n}\n\n// Convert opportunity to JSON\ndef jsonBuilder = new JsonBuilder(opportunity)\nString jsonOutput = jsonBuilder.toString()\n\n// Set the message body to the transformed JSON\nmessage.setBody(jsonOutput)\nreturn message"}, {"name": "LogErrorDetails.groovy", "type": "groovy", "script": "import groovy.json.*\n\ndef errorDetails = [:]\nerrorDetails.timestamp = new Date().toString()\nerrorDetails.errorMessage = exception.getMessage()\nerrorDetails.errorType = exception.getClass().getName()\nerrorDetails.stackTrace = exception.getStackTrace().toString()\n\n// Log error details\nprintln \"Error in Stripe to Salesforce integration: \" + errorDetails.errorMessage\n\n// You could also send these details to a monitoring service\n// or store them in a database for later analysis\n\nreturn message"}]}]}