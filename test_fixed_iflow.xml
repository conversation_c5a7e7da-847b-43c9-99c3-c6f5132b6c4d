<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:ifl="http:///com.sap.ifl.model/Ifl.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1">
  <bpmn2:collaboration id="Collaboration_1" name="Collaboration">
    <bpmn2:documentation id="Documentation_1671742909132" textFormat="text/plain">Generated iFlow: Stripe_Salesforce_iFlow</bpmn2:documentation>
    <bpmn2:extensionElements>
      <ifl:property>
        <key>namespaceMapping</key>
        <value></value>
      </ifl:property>
      <ifl:property>
        <key>allowedHeaderList</key>
        <value>*</value>
      </ifl:property>
      <ifl:property>
        <key>httpSessionHandling</key>
        <value>None</value>
      </ifl:property>
      <ifl:property>
        <key>ServerTrace</key>
        <value>false</value>
      </ifl:property>
      <ifl:property>
        <key>returnExceptionToSender</key>
        <value>false</value>
      </ifl:property>
      <ifl:property>
        <key>log</key>
        <value>All events</value>
      </ifl:property>
      <ifl:property>
        <key>componentVersion</key>
        <value>1.1</value>
      </ifl:property>
      <ifl:property>
        <key>cmdVariantUri</key>
        <value>ctype::IFlowVariant/cname::IFlowConfiguration/version::1.1.16</value>
      </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:participant id="Participant_1" ifl:type="EndpointSender" name="Sender">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>enableBasicAuthentication</key>
            <value>false</value>
        </ifl:property>
        <ifl:property>
            <key>ifl:type</key>
            <value>EndpointSender</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:participant>
<bpmn2:participant id="Participant_Process" ifl:type="IntegrationProcess" name="Integration Process" processRef="Process_1">
    <bpmn2:extensionElements/>
</bpmn2:participant>
    <bpmn2:messageFlow id="MessageFlow_10" name="HTTPS" sourceRef="Participant_1" targetRef="StartEvent_2">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ComponentType</key>
            <value>HTTPS</value>
        </ifl:property>
        <ifl:property>
            <key>urlPath</key>
            <value>/Stripe_Salesforce_iFlow</value>
        </ifl:property>
        <ifl:property>
            <key>ComponentNS</key>
            <value>sap</value>
        </ifl:property>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.5</value>
        </ifl:property>
        <ifl:property>
            <key>senderAuthType</key>
            <value>RoleBased</value>
        </ifl:property>
        <ifl:property>
            <key>userRole</key>
            <value>ESBMessaging.send</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:messageFlow>
  </bpmn2:collaboration>
  <bpmn2:process id="Process_1" name="Integration Process" isExecutable="true">
    <bpmn2:startEvent id="StartEvent_2" name="Start">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.0</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::MessageStartEvent/version::1.0</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:outgoing>SequenceFlow_Start</bpmn2:outgoing>
    <bpmn2:messageEventDefinition/>
</bpmn2:startEvent>
<bpmn2:endEvent id="EndEvent_2" name="End">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.1</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::MessageEndEvent/version::1.1.0</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:incoming>SequenceFlow_End</bpmn2:incoming>
    <bpmn2:messageEventDefinition/>
</bpmn2:endEvent>
<bpmn2:callActivity id="content_modifier_1" name="Set Dynamic Properties">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>bodyType</key>
            <value>constant</value>
        </ifl:property>
        <ifl:property>
            <key>headerTable</key>
            <value></value>
        </ifl:property>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.6</value>
        </ifl:property>
        <ifl:property>
            <key>activityType</key>
            <value>Enricher</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::Enricher/version::1.6.0</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:incoming>SequenceFlow_0</bpmn2:incoming>
    <bpmn2:outgoing>SequenceFlow_1</bpmn2:outgoing>
</bpmn2:callActivity>
<bpmn2:callActivity id="mapping_1" name="Transform Subscription to Opportunity">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>additionalRootElementName</key>
            <value>root</value>
        </ifl:property>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.1</value>
        </ifl:property>
        <ifl:property>
            <key>activityType</key>
            <value>JsonToXmlConverter</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::JsonToXmlConverter/version::1.1.2</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:incoming>SequenceFlow_2</bpmn2:incoming>
    <bpmn2:outgoing>SequenceFlow_3</bpmn2:outgoing>
</bpmn2:callActivity>
<bpmn2:serviceTask id="salesforce_adapter_1" name="Create Salesforce Opportunity">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>activityType</key>
            <value>ExternalCall</value>
        </ifl:property>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.2</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::ExternalCall/version::1.0.4</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:incoming>SequenceFlow_3</bpmn2:incoming>
    <bpmn2:outgoing>SequenceFlow_4</bpmn2:outgoing>
</bpmn2:serviceTask>
<bpmn2:sequenceFlow id="SequenceFlow_0" sourceRef="StartEvent_2" targetRef="content_modifier_1"/>
<bpmn2:sequenceFlow id="SequenceFlow_1" sourceRef="content_modifier_1" targetRef="mapping_1"/>
<bpmn2:sequenceFlow id="SequenceFlow_3" sourceRef="mapping_1" targetRef="salesforce_adapter_1"/>
<bpmn2:sequenceFlow id="SequenceFlow_4" sourceRef="salesforce_adapter_1" targetRef="EndEvent_2"/>
</bpmn2:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane bpmnElement="Collaboration_1" id="BPMNPlane_1">
      <bpmndi:BPMNShape bpmnElement="Participant_1" id="BPMNShape_Participant_1">
    <dc:Bounds height="140.0" width="100.0" x="66.0" y="92.0"/>
</bpmndi:BPMNShape>
<bpmndi:BPMNShape bpmnElement="Participant_Process" id="BPMNShape_Participant_Process">
    <dc:Bounds height="220.0" width="1060.0" x="240.0" y="80.0"/>
</bpmndi:BPMNShape>
<bpmndi:BPMNShape bpmnElement="StartEvent_2" id="BPMNShape_StartEvent_2">
    <dc:Bounds height="32.0" width="32.0" x="300" y="154"/>
</bpmndi:BPMNShape>
<bpmndi:BPMNShape bpmnElement="EndEvent_2" id="BPMNShape_EndEvent_2">
    <dc:Bounds height="32.0" width="32.0" x="950" y="142"/>
</bpmndi:BPMNShape>
<bpmndi:BPMNShape bpmnElement="content_modifier_1" id="BPMNShape_content_modifier_1">
    <dc:Bounds height="60.0" width="100.0" x="300" y="140"/>
</bpmndi:BPMNShape>
<bpmndi:BPMNShape bpmnElement="mapping_1" id="BPMNShape_mapping_1">
    <dc:Bounds height="60.0" width="100.0" x="450" y="140"/>
</bpmndi:BPMNShape>
<bpmndi:BPMNShape bpmnElement="salesforce_adapter_1" id="BPMNShape_salesforce_adapter_1">
    <dc:Bounds height="60.0" width="100.0" x="600" y="140"/>
</bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="MessageFlow_10" id="BPMNEdge_MessageFlow_10" sourceElement="BPMNShape_Participant_1" targetElement="BPMNShape_StartEvent_2">
    <di:waypoint x="116.0" xsi:type="dc:Point" y="160.0"/>
    <di:waypoint x="308.0" xsi:type="dc:Point" y="160.0"/>
</bpmndi:BPMNEdge>
<bpmndi:BPMNEdge bpmnElement="SequenceFlow_0" id="BPMNEdge_SequenceFlow_0" sourceElement="BPMNShape_StartEvent_2" targetElement="BPMNShape_content_modifier_1">
    
    
    <di:waypoint x="332" xsi:type="dc:Point" y="170"/>
    <di:waypoint x="300" xsi:type="dc:Point" y="170"/>
</bpmndi:BPMNEdge>
<bpmndi:BPMNEdge bpmnElement="SequenceFlow_1" id="BPMNEdge_SequenceFlow_1" sourceElement="BPMNShape_content_modifier_1" targetElement="BPMNShape_mapping_1">
    
    
    <di:waypoint x="400" xsi:type="dc:Point" y="170"/>
    <di:waypoint x="300" xsi:type="dc:Point" y="170"/>
</bpmndi:BPMNEdge>
<bpmndi:BPMNEdge bpmnElement="SequenceFlow_3" id="BPMNEdge_SequenceFlow_3" sourceElement="BPMNShape_mapping_1" targetElement="BPMNShape_salesforce_adapter_1">
    
    
    <di:waypoint x="550" xsi:type="dc:Point" y="170"/>
    <di:waypoint x="300" xsi:type="dc:Point" y="170"/>
</bpmndi:BPMNEdge>
<bpmndi:BPMNEdge bpmnElement="SequenceFlow_4" id="BPMNEdge_SequenceFlow_4" sourceElement="BPMNShape_salesforce_adapter_1" targetElement="BPMNShape_EndEvent_2">
    
    
    <di:waypoint x="700" xsi:type="dc:Point" y="170"/>
    <di:waypoint x="950" xsi:type="dc:Point" y="158"/>
</bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn2:definitions>