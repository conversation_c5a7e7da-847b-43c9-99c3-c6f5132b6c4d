#!/usr/bin/env python3
"""
Dell Boomi Flow Documentation Generator

This module provides functionality to parse Dell Boomi process XML files
and generate comprehensive documentation for migration to SAP Integration Suite.
"""

import os
import xml.etree.ElementTree as ET
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class BoomiFlowDocumentationGenerator:
    """Generator for Dell Boomi process documentation"""
    
    def __init__(self):
        self.namespaces = {
            'bns': 'http://api.platform.boomi.com/',
            'xsi': 'http://www.w3.org/2001/XMLSchema-instance'
        }
        self.parsed_processes = []
        self.parsed_maps = []
        self.parsed_connectors = []
    
    def process_directory(self, directory_path: str) -> Dict[str, Any]:
        """
        Process a directory containing Boomi XML files
        
        Args:
            directory_path (str): Path to directory containing Boomi files
            
        Returns:
            Dict containing processing results
        """
        try:
            logger.info(f"Processing Boomi directory: {directory_path}")
            
            # Find and process all XML files
            xml_files = self._find_xml_files(directory_path)
            logger.info(f"Found {len(xml_files)} XML files to process")
            
            results = {
                'total_files': len(xml_files),
                'processed_files': 0,
                'processes': [],
                'maps': [],
                'connectors': [],
                'errors': []
            }
            
            for xml_file in xml_files:
                try:
                    result = self._process_xml_file(xml_file)
                    if result:
                        if result['type'] == 'process':
                            results['processes'].append(result)
                        elif result['type'] == 'map':
                            results['maps'].append(result)
                        elif result['type'] == 'connector':
                            results['connectors'].append(result)
                        
                        results['processed_files'] += 1
                        
                except Exception as e:
                    error_msg = f"Error processing {xml_file}: {str(e)}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
            
            logger.info(f"Successfully processed {results['processed_files']} out of {results['total_files']} files")
            return results
            
        except Exception as e:
            logger.error(f"Error processing Boomi directory: {e}")
            raise
    
    def _find_xml_files(self, directory_path: str) -> List[str]:
        """Find all XML files in the directory"""
        xml_files = []
        
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if file.lower().endswith('.xml'):
                    xml_files.append(os.path.join(root, file))
        
        return xml_files

    def _split_xml_documents(self, content: str) -> List[str]:
        """Split content that may contain multiple XML documents"""
        import re

        # Find all XML declarations and split on them
        xml_pattern = r'<\?xml[^>]*\?>'
        parts = re.split(xml_pattern, content)

        # Remove empty parts and reconstruct XML documents
        xml_documents = []
        xml_declaration = '<?xml version="1.0" encoding="UTF-8"?>'

        for part in parts:
            part = part.strip()
            if part and part.startswith('<'):
                xml_documents.append(xml_declaration + part)

        # If no split occurred, return the original content
        if not xml_documents:
            xml_documents = [content.strip()]

        return xml_documents
    
    def _process_xml_file(self, xml_file_path: str) -> Optional[Dict[str, Any]]:
        """Process a single XML file"""
        try:
            with open(xml_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Handle multiple XML documents in one file (common in Boomi exports)
            xml_documents = self._split_xml_documents(content)

            results = []
            for xml_doc in xml_documents:
                try:
                    root = ET.fromstring(xml_doc)

                    # Determine the type of Boomi component
                    component_type = self._determine_component_type(root)

                    if component_type == 'process':
                        result = self._parse_process(root, xml_file_path)
                    elif component_type == 'map':
                        result = self._parse_map(root, xml_file_path)
                    elif component_type == 'connector':
                        result = self._parse_connector(root, xml_file_path)
                    else:
                        logger.warning(f"Unknown component type '{component_type}' in {xml_file_path}")
                        continue

                    if result:
                        results.append(result)

                except ET.ParseError as e:
                    logger.error(f"XML parsing error in document from {xml_file_path}: {e}")
                    continue

            # Return the first valid result (or combine them if needed)
            return results[0] if results else None

        except Exception as e:
            logger.error(f"Error processing XML file {xml_file_path}: {e}")
            return None
    
    def _determine_component_type(self, root: ET.Element) -> str:
        """Determine the type of Boomi component"""
        # Check the type attribute on the Component element first
        component_type = root.get('type', '')

        if component_type == 'process':
            return 'process'
        elif component_type == 'transform.map':
            return 'map'
        elif component_type in ['connector-action', 'connector']:
            return 'connector'

        # Fallback: check for nested elements in bns:object
        bns_object = root.find('.//{http://api.platform.boomi.com/}object')
        if bns_object is not None:
            # Check for process element
            if bns_object.find('process') is not None:
                return 'process'
            # Check for Map element
            elif bns_object.find('Map') is not None:
                return 'map'
            # Check for Operation element
            elif bns_object.find('Operation') is not None:
                return 'connector'

        # Additional fallback checks
        if root.find('.//process') is not None:
            return 'process'
        elif root.find('.//Map') is not None:
            return 'map'
        elif root.find('.//Operation') is not None:
            return 'connector'

        logger.warning(f"Unknown component type: {component_type}")
        return 'unknown'
    
    def _parse_process(self, root: ET.Element, file_path: str) -> Dict[str, Any]:
        """Parse a Boomi process component"""
        component_info = self._extract_component_info(root)

        # Look for process element in bns:object first
        bns_object = root.find('.//{http://api.platform.boomi.com/}object')
        process_elem = None

        if bns_object is not None:
            process_elem = bns_object.find('process')

        # Fallback to direct search
        if process_elem is None:
            process_elem = root.find('.//process')

        if process_elem is None:
            logger.warning(f"No process element found in {file_path}")
            return None
        
        shapes = self._extract_shapes(process_elem)
        connections = self._extract_connections(shapes)
        
        return {
            'type': 'process',
            'file_path': file_path,
            'component': component_info,
            'process': {
                'allow_simultaneous': process_elem.get('allowSimultaneous'),
                'enable_user_log': process_elem.get('enableUserLog'),
                'process_log_on_error_only': process_elem.get('processLogOnErrorOnly'),
                'workload': process_elem.get('workload'),
                'shapes': shapes,
                'connections': connections
            },
            'integration_patterns': self._identify_integration_patterns(root)
        }
    
    def _parse_map(self, root: ET.Element, file_path: str) -> Dict[str, Any]:
        """Parse a Boomi map component with detailed business logic analysis"""
        component_info = self._extract_component_info(root)

        # Look for Map element in bns:object first
        bns_object = root.find('.//{http://api.platform.boomi.com/}object')
        map_elem = None

        if bns_object is not None:
            map_elem = bns_object.find('Map')

        # Fallback to direct search
        if map_elem is None:
            map_elem = root.find('.//Map')

        if map_elem is None:
            logger.warning(f"No Map element found in {file_path}")
            return None

        # Extract detailed mappings with business logic
        mappings = []
        function_steps = []
        default_values = []

        for mapping in map_elem.findall('.//Mapping'):
            mapping_info = {
                'from_key': mapping.get('fromKey'),
                'from_type': mapping.get('fromType'),
                'to_key': mapping.get('toKey'),
                'to_name_path': mapping.get('toNamePath'),
                'to_type': mapping.get('toType'),
                'business_logic': self._analyze_mapping_business_logic(mapping)
            }
            mappings.append(mapping_info)

        # Extract function steps (calculations, transformations)
        for func_step in map_elem.findall('.//FunctionStep'):
            function_info = self._extract_detailed_function_step(func_step)
            if function_info:
                function_steps.append(function_info)

        # Extract default values and constants
        for default in map_elem.findall('.//DefaultValue'):
            default_info = {
                'target_field': default.get('targetField'),
                'value': default.get('value'),
                'type': default.get('type')
            }
            default_values.append(default_info)

        # Analyze overall map complexity and purpose
        map_analysis = self._analyze_map_complexity(mappings, function_steps, default_values)

        return {
            'type': 'map',
            'file_path': file_path,
            'component': component_info,
            'map': {
                'from_profile': map_elem.get('fromProfile'),
                'to_profile': map_elem.get('toProfile'),
                'mappings': mappings,
                'function_steps': function_steps,
                'default_values': default_values,
                'analysis': map_analysis
            }
        }

    def _analyze_mapping_business_logic(self, mapping: ET.Element) -> Dict[str, Any]:
        """Analyze business logic within a mapping"""
        logic = {
            'type': 'direct_mapping',
            'description': 'Direct field mapping',
            'transformations': [],
            'conditions': []
        }

        # Check for function references
        func_refs = mapping.findall('.//FunctionRef')
        if func_refs:
            logic['type'] = 'function_mapping'
            logic['description'] = 'Mapping with function transformations'
            for func_ref in func_refs:
                logic['transformations'].append({
                    'function': func_ref.get('functionName'),
                    'purpose': self._determine_function_purpose(func_ref.get('functionName'))
                })

        # Check for conditional logic
        conditions = mapping.findall('.//Condition')
        if conditions:
            logic['type'] = 'conditional_mapping'
            logic['description'] = 'Conditional mapping with business rules'
            for condition in conditions:
                logic['conditions'].append({
                    'expression': condition.get('expression'),
                    'description': 'Business rule condition'
                })

        return logic

    def _extract_detailed_function_step(self, func_step: ET.Element) -> Dict[str, Any]:
        """Extract detailed function step information"""
        function_info = {
            'name': func_step.get('name'),
            'function_name': func_step.get('functionName'),
            'description': 'Function step',
            'inputs': [],
            'outputs': [],
            'business_purpose': 'Data transformation'
        }

        # Determine business purpose from function name
        func_name = str(function_info.get('function_name', '') or '').lower()
        if 'concat' in func_name:
            function_info['business_purpose'] = 'String concatenation for description formatting'
            function_info['description'] = 'Concatenates multiple values into a single string'
        elif 'date' in func_name:
            function_info['business_purpose'] = 'Date calculation for business rules'
            function_info['description'] = 'Calculates date values based on business logic'
        elif 'format' in func_name:
            function_info['business_purpose'] = 'Data formatting for target system'
            function_info['description'] = 'Formats data according to target system requirements'

        # Extract input and output parameters
        for input_elem in func_step.findall('.//Input'):
            function_info['inputs'].append({
                'name': input_elem.get('name'),
                'type': input_elem.get('type'),
                'source': input_elem.get('source')
            })

        for output_elem in func_step.findall('.//Output'):
            function_info['outputs'].append({
                'name': output_elem.get('name'),
                'type': output_elem.get('type'),
                'target': output_elem.get('target')
            })

        return function_info

    def _analyze_map_complexity(self, mappings: List[Dict], function_steps: List[Dict], default_values: List[Dict]) -> Dict[str, Any]:
        """Analyze the overall complexity and purpose of the map"""
        analysis = {
            'complexity': 'simple',
            'business_purpose': 'Data transformation',
            'key_features': [],
            'external_dependencies': []
        }

        # Determine complexity
        if function_steps:
            analysis['complexity'] = 'complex'
            analysis['key_features'].append('Function-based transformations')

        if any(m['business_logic']['type'] != 'direct_mapping' for m in mappings):
            analysis['complexity'] = 'complex'
            analysis['key_features'].append('Conditional mapping logic')

        if default_values:
            analysis['key_features'].append('Default value assignments')

        # Determine business purpose from field names
        target_fields = [m.get('to_name_path', '') for m in mappings]
        if any('opportunity' in field.lower() for field in target_fields):
            analysis['business_purpose'] = 'Salesforce Opportunity creation'

        return analysis

    def _determine_function_purpose(self, function_name: str) -> str:
        """Determine the business purpose of a function"""
        if not function_name:
            return 'Unknown function'

        func_name = str(function_name).lower()
        if 'concat' in func_name:
            return 'Concatenate values for description or naming'
        elif 'date' in func_name:
            return 'Calculate date values for business rules'
        elif 'format' in func_name:
            return 'Format data for target system compatibility'
        elif 'lookup' in func_name:
            return 'Lookup values from external sources'
        else:
            return f'Perform {function_name} transformation'
    
    def _parse_connector(self, root: ET.Element, file_path: str) -> Dict[str, Any]:
        """Parse a Boomi connector component"""
        component_info = self._extract_component_info(root)

        # Look for Operation element in bns:object first
        bns_object = root.find('.//{http://api.platform.boomi.com/}object')
        operation = None

        if bns_object is not None:
            operation = bns_object.find('Operation')

        # Fallback to direct search
        if operation is None:
            operation = root.find('.//Operation')

        if operation is None:
            logger.warning(f"No Operation element found in {file_path}")
            return None
        
        config = operation.find('Configuration')
        connector_info = {'type': 'generic'}
        
        if config is not None:
            # Extract Salesforce-specific configuration
            sf_action = config.find('SalesforceSendAction')
            if sf_action is not None:
                connector_info = {
                    'type': 'salesforce',
                    'object_action': sf_action.get('objectAction'),
                    'object_name': sf_action.get('objectName'),
                    'batch_size': sf_action.get('batchSize'),
                    'use_bulk_api': sf_action.get('useBulkAPI') == 'true'
                }
        
        return {
            'type': 'connector',
            'file_path': file_path,
            'component': component_info,
            'connector': connector_info
        }
    
    def _extract_component_info(self, root: ET.Element) -> Dict[str, Any]:
        """Extract basic component information"""
        return {
            'id': root.get('componentId'),
            'name': root.get('name'),
            'type': root.get('type'),
            'version': root.get('version'),
            'created_by': root.get('createdBy'),
            'created_date': root.get('createdDate'),
            'modified_by': root.get('modifiedBy'),
            'modified_date': root.get('modifiedDate'),
            'folder_path': root.get('folderFullPath'),
            'description': self._get_description(root)
        }
    
    def _extract_shapes(self, process_elem: ET.Element) -> List[Dict[str, Any]]:
        """Extract shape information from process"""
        shapes = []

        # Look for shapes container first
        shapes_container = process_elem.find('shapes')
        if shapes_container is not None:
            shape_elements = shapes_container.findall('shape')
        else:
            # Fallback to direct search
            shape_elements = process_elem.findall('shape')
            if not shape_elements:
                shape_elements = process_elem.findall('.//shape')

        for shape in shape_elements:
            shape_info = {
                'name': shape.get('name'),
                'type': shape.get('shapetype'),
                'image': shape.get('image'),
                'user_label': shape.get('userlabel'),
                'position': {
                    'x': float(shape.get('x', 0)),
                    'y': float(shape.get('y', 0))
                },
                'configuration': self._extract_shape_configuration(shape)
            }
            
            # Extract drag points (connections)
            dragpoints = []
            # Look for dragpoints container first
            dragpoints_container = shape.find('dragpoints')
            if dragpoints_container is not None:
                dragpoint_elements = dragpoints_container.findall('dragpoint')
            else:
                # Fallback to direct search
                dragpoint_elements = shape.findall('dragpoint')
                if not dragpoint_elements:
                    dragpoint_elements = shape.findall('.//dragpoint')

            for dragpoint in dragpoint_elements:
                dragpoints.append({
                    'name': dragpoint.get('name'),
                    'to_shape': dragpoint.get('toShape'),
                    'position': {
                        'x': float(dragpoint.get('x', 0)),
                        'y': float(dragpoint.get('y', 0))
                    }
                })
            shape_info['dragpoints'] = dragpoints
            
            shapes.append(shape_info)
        
        return shapes
    
    def _extract_shape_configuration(self, shape: ET.Element) -> Dict[str, Any]:
        """Extract detailed configuration from a shape"""
        config = {}
        config_elem = shape.find('configuration')

        if config_elem is not None:
            # Extract connector actions with detailed analysis
            connector_action = config_elem.find('connectoraction')
            if connector_action is not None:
                config['connector_action'] = self._extract_detailed_connector_action(connector_action)

            # Extract map configuration with detailed mapping analysis
            map_elem = config_elem.find('map')
            if map_elem is not None:
                config['map'] = self._extract_detailed_map_configuration(map_elem)

            # Extract document properties with business logic analysis
            doc_props = config_elem.find('documentproperties')
            if doc_props is not None:
                config['document_properties'] = self._extract_document_properties(doc_props)

        return config

    def _extract_detailed_connector_action(self, connector_action: ET.Element) -> Dict[str, Any]:
        """Extract detailed connector action information"""
        action_info = {
            'action_type': connector_action.get('actionType'),
            'connector_type': connector_action.get('connectorType'),
            'connection_id': connector_action.get('connectionId'),
            'operation_id': connector_action.get('operationId'),
            'detailed_analysis': {}
        }

        # Analyze based on connector type
        connector_type = action_info.get('connector_type', '').lower()
        action_type = action_info.get('action_type', '').lower()

        if 'salesforce' in connector_type:
            action_info['detailed_analysis'] = {
                'system': 'Salesforce',
                'purpose': self._determine_salesforce_purpose(action_type),
                'authentication': 'OAuth 2.0',
                'api_type': 'REST API'
            }
        elif 'http' in connector_type:
            action_info['detailed_analysis'] = {
                'system': 'HTTP API',
                'purpose': self._determine_http_purpose(action_type),
                'authentication': 'API Key or OAuth',
                'api_type': 'REST API'
            }
        elif 'database' in connector_type:
            action_info['detailed_analysis'] = {
                'system': 'Database',
                'purpose': self._determine_database_purpose(action_type),
                'authentication': 'Database credentials',
                'api_type': 'SQL'
            }

        return action_info

    def _extract_detailed_map_configuration(self, map_elem: ET.Element) -> Dict[str, Any]:
        """Extract detailed map configuration with business logic"""
        map_config = {
            'map_id': map_elem.get('mapId'),
            'purpose': 'Data transformation',
            'complexity': 'simple',
            'business_rules': []
        }

        # Try to determine purpose from map ID
        map_id = str(map_config.get('map_id', '') or '').lower()
        if 'salesforce' in map_id and 'opportunity' in map_id:
            map_config['purpose'] = 'Transform data to Salesforce Opportunity format'
            map_config['target_system'] = 'Salesforce'
            map_config['target_object'] = 'Opportunity'
        elif 'stripe' in map_id:
            map_config['purpose'] = 'Transform Stripe subscription data'
            map_config['source_system'] = 'Stripe'

        return map_config

    def _determine_salesforce_purpose(self, action_type: str) -> str:
        """Determine the purpose of a Salesforce connector action"""
        if action_type.lower() == 'send':
            return 'Create or update records in Salesforce'
        elif action_type.lower() == 'query':
            return 'Query records from Salesforce'
        elif action_type.lower() == 'listen':
            return 'Listen for Salesforce events'
        else:
            return f'Perform {action_type} operation in Salesforce'

    def _determine_http_purpose(self, action_type: str) -> str:
        """Determine the purpose of an HTTP connector action"""
        if action_type.lower() == 'send':
            return 'Send HTTP request to external API'
        elif action_type.lower() == 'listen':
            return 'Listen for incoming HTTP requests (webhook)'
        else:
            return f'Perform {action_type} HTTP operation'

    def _determine_database_purpose(self, action_type: str) -> str:
        """Determine the purpose of a database connector action"""
        if action_type.lower() == 'send':
            return 'Execute database operations (INSERT/UPDATE/DELETE)'
        elif action_type.lower() == 'query':
            return 'Query data from database'
        else:
            return f'Perform {action_type} database operation'
    
    def _extract_document_properties(self, doc_props: ET.Element) -> List[Dict[str, Any]]:
        """Extract detailed document properties configuration including HTTP calls and business logic"""
        properties = []

        for prop in doc_props.findall('documentproperty'):
            prop_info = {
                'name': prop.get('name'),
                'property_id': prop.get('propertyId'),
                'default_value': prop.get('defaultValue'),
                'persist': prop.get('persist') == 'true',
                'is_dynamic_credential': prop.get('isDynamicCredential') == 'true',
                'business_logic': self._analyze_property_business_logic(prop)
            }
            properties.append(prop_info)

        return properties

    def _analyze_property_business_logic(self, prop: ET.Element) -> Dict[str, Any]:
        """Analyze the business logic within a document property"""
        logic = {
            'type': 'simple',
            'description': 'Sets a simple property value',
            'http_calls': [],
            'calculations': [],
            'concatenations': [],
            'external_dependencies': []
        }

        # Look for connector parameters (HTTP calls)
        connector_params = prop.findall('.//connectorparameter')
        if connector_params:
            logic['type'] = 'complex_http'
            logic['description'] = 'Makes HTTP API calls to retrieve external data'

            for param in connector_params:
                http_call = self._extract_http_call_details(param)
                if http_call:
                    logic['http_calls'].append(http_call)
                    logic['external_dependencies'].append(http_call['endpoint'])

        # Look for function steps (calculations, concatenations)
        function_steps = prop.findall('.//functionstep')
        for step in function_steps:
            function_info = self._extract_function_step_details(step)
            if function_info:
                if function_info['type'] == 'date_calculation':
                    logic['calculations'].append(function_info)
                elif function_info['type'] == 'concatenation':
                    logic['concatenations'].append(function_info)

        # Look for relative date functions and date parameters
        relative_dates = prop.findall('.//relativedate')
        date_params = prop.findall('.//dateparameter')
        date_deltas = prop.findall('.//datedelta')

        for rel_date in relative_dates:
            date_calc = self._extract_relative_date_logic(rel_date)
            if date_calc:
                logic['calculations'].append(date_calc)
                if logic['type'] == 'simple':
                    logic['type'] = 'date_calculation'
                    logic['description'] = f'Calculates date using relative date logic: {date_calc["description"]}'

        for date_param in date_params:
            date_calc = self._extract_date_parameter_logic(date_param)
            if date_calc:
                logic['calculations'].append(date_calc)
                if logic['type'] == 'simple':
                    logic['type'] = 'date_calculation'
                    logic['description'] = f'Calculates date: {date_calc["description"]}'

        for date_delta in date_deltas:
            date_calc = self._extract_date_delta_logic(date_delta)
            if date_calc:
                logic['calculations'].append(date_calc)
                if logic['type'] == 'simple':
                    logic['type'] = 'date_calculation'
                    logic['description'] = f'Date calculation: {date_calc["description"]}'

        # Look for concatenation logic (multiple source values)
        source_values = prop.findall('.//sourcevalues')
        for source_val in source_values:
            param_values = source_val.findall('.//parametervalue')
            if len(param_values) > 1:
                # Multiple parameter values suggest concatenation
                concat_info = self._extract_concatenation_logic(param_values)
                if concat_info:
                    logic['concatenations'].append(concat_info)
                    if logic['type'] == 'simple':
                        logic['type'] = 'concatenation'
                        logic['description'] = f'String concatenation: {concat_info["description"]}'

        # Update description based on complexity
        if logic['http_calls'] and logic['calculations']:
            logic['description'] = 'Makes HTTP API calls and performs date calculations'
        elif logic['http_calls'] and logic['concatenations']:
            logic['description'] = 'Makes HTTP API calls and performs string concatenations'
        elif logic['calculations'] and logic['concatenations']:
            logic['description'] = 'Performs date calculations and string concatenations'

        return logic

    def _extract_http_call_details(self, connector_param: ET.Element) -> Dict[str, Any]:
        """Extract HTTP call details from connector parameters"""
        http_call = {
            'method': connector_param.get('actionType', 'GET'),
            'endpoint': 'External API endpoint',
            'purpose': 'Data retrieval',
            'authentication': 'API authentication required',
            'parameters': [],
            'output_field': 'Unknown'
        }

        # Extract connection ID and operation details
        connection_id = connector_param.get('connectionId')
        operation_id = connector_param.get('operationId')
        output_param_name = connector_param.get('outputParamName', '')

        if connection_id:
            http_call['connection_id'] = connection_id
        if operation_id:
            http_call['operation_id'] = operation_id
        if output_param_name:
            http_call['output_field'] = output_param_name

        # Extract input parameters to understand what data is being sent
        inputs = connector_param.findall('.//inputs/parametervalue')
        for input_param in inputs:
            element_name = input_param.get('elementToSetName', 'Unknown')
            http_call['parameters'].append(element_name)

        # Determine purpose from output parameter name and inputs
        if 'name' in output_param_name.lower():
            if any('customer' in param.lower() for param in http_call['parameters']):
                http_call['purpose'] = 'Retrieve customer name using customer ID'
                http_call['endpoint'] = 'Customer API - Get customer details'
            elif any('product' in param.lower() for param in http_call['parameters']):
                http_call['purpose'] = 'Retrieve product name using product ID'
                http_call['endpoint'] = 'Product API - Get product details'
            else:
                http_call['purpose'] = 'Retrieve name field from external API'

        # Infer API type from connector type
        connector_type = connector_param.get('connectorType', '').lower()
        if 'http' in connector_type:
            http_call['api_type'] = 'HTTP REST API'

        return http_call

    def _extract_function_step_details(self, function_step: ET.Element) -> Dict[str, Any]:
        """Extract function step details for calculations and concatenations"""
        function_info = {
            'type': 'unknown',
            'description': 'Unknown function',
            'inputs': [],
            'output': 'Unknown'
        }

        function_name = function_step.get('functionName', '') or ''
        function_name = function_name.lower()

        if 'concat' in function_name or 'concatenate' in function_name:
            function_info['type'] = 'concatenation'
            function_info['description'] = 'String concatenation operation'
        elif 'date' in function_name or 'time' in function_name:
            function_info['type'] = 'date_calculation'
            function_info['description'] = 'Date calculation operation'
        elif 'format' in function_name:
            function_info['type'] = 'formatting'
            function_info['description'] = 'Data formatting operation'

        return function_info

    def _extract_relative_date_logic(self, rel_date: ET.Element) -> Dict[str, Any]:
        """Extract relative date calculation logic"""
        date_calc = {
            'type': 'date_calculation',
            'description': 'Relative date calculation',
            'offset_amount': rel_date.get('offsetAmount', '0'),
            'offset_unit': rel_date.get('offsetUnit', 'days'),
            'base_date': rel_date.get('baseDate', 'current')
        }

        # Create human-readable description
        offset = int(date_calc['offset_amount']) if date_calc['offset_amount'].isdigit() else 0
        unit = date_calc['offset_unit']

        if offset < 0:
            date_calc['description'] = f'Calculate date {abs(offset)} {unit} ago from current date'
        elif offset > 0:
            date_calc['description'] = f'Calculate date {offset} {unit} from current date'
        else:
            date_calc['description'] = 'Use current date'

        return date_calc

    def _extract_date_parameter_logic(self, date_param: ET.Element) -> Dict[str, Any]:
        """Extract date parameter calculation logic"""
        date_calc = {
            'type': 'date_calculation',
            'description': 'Date parameter calculation',
            'parameter_type': date_param.get('dateparametertype', 'unknown'),
            'mask': date_param.get('datetimemask', 'default')
        }

        if date_calc['parameter_type'] == 'relative':
            date_calc['description'] = 'Relative date calculation'

        return date_calc

    def _extract_date_delta_logic(self, date_delta: ET.Element) -> Dict[str, Any]:
        """Extract date delta calculation logic"""
        date_calc = {
            'type': 'date_calculation',
            'description': 'Date delta calculation',
            'sign': date_delta.get('sign', 'plus'),
            'unit': date_delta.get('unit', 'days'),
            'value': date_delta.get('value', '0')
        }

        # Create human-readable description
        sign_text = 'subtract' if date_calc['sign'] == 'minus' else 'add'
        value = date_calc['value']
        unit = date_calc['unit']

        date_calc['description'] = f'Calculate date by {sign_text}ing {value} {unit} from current date'

        return date_calc

    def _extract_concatenation_logic(self, param_values: List[ET.Element]) -> Dict[str, Any]:
        """Extract concatenation logic from multiple parameter values"""
        concat_info = {
            'type': 'concatenation',
            'description': 'String concatenation',
            'components': [],
            'pattern': ''
        }

        components = []
        for param_val in param_values:
            value_type = param_val.get('valueType', 'unknown')

            if value_type == 'track':
                # Reference to another property
                track_param = param_val.find('.//trackparameter')
                if track_param is not None:
                    prop_name = track_param.get('propertyName', 'Unknown Property')
                    components.append(f'{{Property: {prop_name}}}')
            elif value_type == 'static':
                # Static text
                static_param = param_val.find('.//staticparameter')
                if static_param is not None:
                    static_text = static_param.get('staticproperty', '')
                    components.append(f'"{static_text}"')
            else:
                components.append(f'{{{value_type}}}')

        concat_info['components'] = components
        concat_info['pattern'] = ' + '.join(components)
        concat_info['description'] = f'Concatenate: {concat_info["pattern"]}'

        return concat_info
    
    def _extract_connections(self, shapes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract connections between shapes"""
        connections = []
        
        for shape in shapes:
            for dragpoint in shape.get('dragpoints', []):
                if dragpoint.get('to_shape'):
                    connections.append({
                        'from_shape': shape['name'],
                        'to_shape': dragpoint['to_shape'],
                        'from_position': dragpoint['position'],
                        'connection_type': 'flow'
                    })
        
        return connections
    
    def _identify_integration_patterns(self, root: ET.Element) -> List[str]:
        """Identify common integration patterns in the Boomi process"""
        patterns = []
        
        # Check for common patterns based on component types and configurations
        if root.find('.//connectoraction[@actionType="Listen"]') is not None:
            patterns.append('event_listener')
        
        if root.find('.//connectoraction[@actionType="Send"]') is not None:
            patterns.append('data_sender')
        
        if root.find('.//Map') is not None:
            patterns.append('data_transformation')
        
        if root.find('.//documentproperties') is not None:
            patterns.append('dynamic_properties')
        
        if root.find('.//SalesforceSendAction') is not None:
            patterns.append('salesforce_integration')
        
        return patterns
    
    def _get_description(self, root: ET.Element) -> str:
        """Extract description from component"""
        # Try with namespace first
        desc_elem = root.find('.//{http://api.platform.boomi.com/}description')
        if desc_elem is not None:
            return desc_elem.text or ""

        # Fallback to direct search
        desc_elem = root.find('.//description')
        return desc_elem.text if desc_elem is not None else ""
    
    def generate_documentation(self, processing_results: Dict[str, Any]) -> str:
        """Generate comprehensive markdown documentation from processing results"""
        doc_lines = []
        
        # Header
        doc_lines.append("# Dell Boomi Integration Documentation")
        doc_lines.append("")
        doc_lines.append(f"**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        doc_lines.append("")
        
        # Summary
        doc_lines.append("## Summary")
        doc_lines.append("")
        doc_lines.append(f"- **Total Files Processed:** {processing_results['processed_files']}")
        doc_lines.append(f"- **Processes:** {len(processing_results['processes'])}")
        doc_lines.append(f"- **Maps:** {len(processing_results['maps'])}")
        doc_lines.append(f"- **Connectors:** {len(processing_results['connectors'])}")
        if processing_results['errors']:
            doc_lines.append(f"- **Errors:** {len(processing_results['errors'])}")
        doc_lines.append("")
        
        # Processes
        if processing_results['processes']:
            doc_lines.append("## Dell Boomi Processes")
            doc_lines.append("")

            for i, process in enumerate(processing_results['processes'], 1):
                component = process['component']
                process_info = process['process']

                doc_lines.append(f"### {i}. {component.get('name', 'Unnamed Process')}")
                doc_lines.append("")

                if component.get('description'):
                    doc_lines.append(f"**Description:** {component['description']}")
                    doc_lines.append("")

                # Component details
                doc_lines.append("#### Component Information")
                doc_lines.append("")
                doc_lines.append(f"- **Type:** {component.get('type', 'Unknown')}")
                doc_lines.append(f"- **Version:** {component.get('version', 'Unknown')}")
                doc_lines.append(f"- **Created By:** {component.get('created_by', 'Unknown')}")
                doc_lines.append(f"- **Folder Path:** {component.get('folder_path', 'Unknown')}")
                doc_lines.append("")

                # Integration patterns
                if process.get('integration_patterns'):
                    doc_lines.append("#### Integration Patterns")
                    doc_lines.append("")
                    for pattern in process['integration_patterns']:
                        doc_lines.append(f"- {pattern.replace('_', ' ').title()}")
                    doc_lines.append("")

                # Detailed step-by-step analysis
                doc_lines.append("#### Detailed Process Flow Analysis")
                doc_lines.append("")
                doc_lines.extend(self._generate_detailed_process_analysis(process_info['shapes']))
                doc_lines.append("")

                # Process flow diagram
                doc_lines.append("#### Process Flow Diagram")
                doc_lines.append("")
                doc_lines.append(self._generate_flow_diagram(process_info['shapes'], process_info['connections']))
                doc_lines.append("")
        
        # Maps
        if processing_results['maps']:
            doc_lines.append("## Data Mappings and Transformations")
            doc_lines.append("")

            for i, map_info in enumerate(processing_results['maps'], 1):
                component = map_info['component']
                map_data = map_info['map']

                doc_lines.append(f"### {i}. {component.get('name', 'Unnamed Map')}")
                doc_lines.append("")

                # Map analysis
                if map_data.get('analysis'):
                    analysis = map_data['analysis']
                    doc_lines.append(f"**Business Purpose:** {analysis.get('business_purpose', 'Data transformation')}")
                    doc_lines.append(f"**Complexity:** {analysis.get('complexity', 'simple').title()}")
                    if analysis.get('key_features'):
                        doc_lines.append(f"**Key Features:** {', '.join(analysis['key_features'])}")
                    doc_lines.append("")

                # Detailed field mappings
                doc_lines.append("#### Field Mappings")
                doc_lines.append("")
                doc_lines.append("| From | To | Type | Business Logic |")
                doc_lines.append("|------|----|----- |-------------- |")
                for mapping in map_data.get('mappings', []):
                    from_path = mapping.get('from_key', 'Unknown')
                    to_path = mapping.get('to_name_path', 'Unknown')
                    mapping_type = mapping.get('to_type', 'Unknown')
                    business_logic = mapping.get('business_logic', {}).get('description', 'Direct mapping')
                    doc_lines.append(f"| {from_path} | {to_path} | {mapping_type} | {business_logic} |")
                doc_lines.append("")

                # Function steps
                if map_data.get('function_steps'):
                    doc_lines.append("#### Function Steps and Business Logic")
                    doc_lines.append("")
                    for func_step in map_data['function_steps']:
                        doc_lines.append(f"- **{func_step.get('name', 'Unknown Function')}**: {func_step.get('description', 'Function step')}")
                        doc_lines.append(f"  - **Purpose**: {func_step.get('business_purpose', 'Data transformation')}")
                        if func_step.get('inputs'):
                            inputs = ', '.join([inp.get('name', 'Unknown') for inp in func_step['inputs']])
                            doc_lines.append(f"  - **Inputs**: {inputs}")
                        if func_step.get('outputs'):
                            outputs = ', '.join([out.get('name', 'Unknown') for out in func_step['outputs']])
                            doc_lines.append(f"  - **Outputs**: {outputs}")
                    doc_lines.append("")

                # Default values
                if map_data.get('default_values'):
                    doc_lines.append("#### Default Values and Constants")
                    doc_lines.append("")
                    doc_lines.append("| Target Field | Default Value | Type |")
                    doc_lines.append("|------------- |-------------- |----- |")
                    for default in map_data['default_values']:
                        target = default.get('target_field', 'Unknown')
                        value = default.get('value', 'Unknown')
                        value_type = default.get('type', 'Unknown')
                        doc_lines.append(f"| {target} | {value} | {value_type} |")
                    doc_lines.append("")
        
        # Connectors
        if processing_results['connectors']:
            doc_lines.append("## Connectors")
            doc_lines.append("")
            
            for i, connector in enumerate(processing_results['connectors'], 1):
                component = connector['component']
                connector_info = connector['connector']
                
                doc_lines.append(f"### {i}. {component.get('name', 'Unnamed Connector')}")
                doc_lines.append("")
                doc_lines.append(f"- **Type:** {connector_info.get('type', 'Unknown')}")
                if connector_info.get('object_name'):
                    doc_lines.append(f"- **Object:** {connector_info['object_name']}")
                if connector_info.get('object_action'):
                    doc_lines.append(f"- **Action:** {connector_info['object_action']}")
                doc_lines.append("")
        
        # Errors
        if processing_results['errors']:
            doc_lines.append("## Processing Errors")
            doc_lines.append("")
            for error in processing_results['errors']:
                doc_lines.append(f"- {error}")
            doc_lines.append("")
        
        return "\n".join(doc_lines)
    
    def _generate_flow_diagram(self, shapes: List[Dict[str, Any]], connections: List[Dict[str, Any]]) -> str:
        """Generate a text-based flow diagram"""
        diagram_lines = ["```mermaid", "graph TD"]
        
        # Add shapes
        for shape in shapes:
            shape_name = shape['name']
            shape_type = shape['type']
            label = shape.get('user_label') or shape_name
            
            if shape_type == 'start':
                diagram_lines.append(f"    {shape_name}([{label}])")
            elif shape_type == 'stop':
                diagram_lines.append(f"    {shape_name}([{label}])")
            elif shape_type == 'connectoraction':
                diagram_lines.append(f"    {shape_name}[{label}]")
            elif shape_type == 'map':
                diagram_lines.append(f"    {shape_name}{{Transform: {label}}}")
            else:
                diagram_lines.append(f"    {shape_name}[{label}]")
        
        # Add connections
        for connection in connections:
            from_shape = connection['from_shape']
            to_shape = connection['to_shape']
            diagram_lines.append(f"    {from_shape} --> {to_shape}")
        
        diagram_lines.append("```")
        return "\n".join(diagram_lines)

    def _generate_detailed_process_analysis(self, shapes: List[Dict[str, Any]]) -> List[str]:
        """Generate detailed step-by-step process analysis"""
        analysis_lines = []

        for i, shape in enumerate(shapes, 1):
            shape_name = shape.get('name', 'Unknown')
            shape_type = shape.get('type', 'Unknown')
            user_label = shape.get('user_label', shape_name)
            config = shape.get('configuration', {})

            analysis_lines.append(f"**Step {i}: {user_label} ({shape_name})**")
            analysis_lines.append("")

            # Analyze based on shape type
            if shape_type == 'start':
                analysis_lines.append("- **Type**: Process trigger/start event")
                if config.get('connector_action'):
                    conn_action = config['connector_action']
                    if conn_action.get('detailed_analysis'):
                        analysis = conn_action['detailed_analysis']
                        analysis_lines.append(f"- **System**: {analysis.get('system', 'Unknown')}")
                        analysis_lines.append(f"- **Purpose**: {analysis.get('purpose', 'Unknown')}")
                        analysis_lines.append(f"- **Authentication**: {analysis.get('authentication', 'Unknown')}")
                else:
                    analysis_lines.append("- **Purpose**: Initiates the integration process")

            elif shape_type == 'documentproperties':
                analysis_lines.append("- **Type**: Document Properties (Dynamic Property Setting)")
                if config.get('document_properties'):
                    doc_props = config['document_properties']
                    analysis_lines.append("- **Properties Configured**:")

                    for prop in doc_props:
                        prop_name = prop.get('name', 'Unknown')
                        business_logic = prop.get('business_logic', {})
                        analysis_lines.append(f"  - **{prop_name}**: {business_logic.get('description', 'Sets property value')}")

                        # Detail HTTP calls
                        if business_logic.get('http_calls'):
                            analysis_lines.append("    - **External API Calls**:")
                            for http_call in business_logic['http_calls']:
                                analysis_lines.append(f"      - {http_call.get('purpose', 'API call')}")
                                analysis_lines.append(f"      - Endpoint: {http_call.get('endpoint', 'Unknown')}")

                        # Detail calculations
                        if business_logic.get('calculations'):
                            analysis_lines.append("    - **Calculations**:")
                            for calc in business_logic['calculations']:
                                analysis_lines.append(f"      - {calc.get('description', 'Calculation')}")

                        # Detail concatenations
                        if business_logic.get('concatenations'):
                            analysis_lines.append("    - **String Operations**:")
                            for concat in business_logic['concatenations']:
                                analysis_lines.append(f"      - {concat.get('description', 'String operation')}")
                else:
                    analysis_lines.append("- **Purpose**: Sets dynamic properties for the integration")

            elif shape_type == 'map':
                analysis_lines.append("- **Type**: Data Mapping/Transformation")
                if config.get('map'):
                    map_config = config['map']
                    analysis_lines.append(f"- **Purpose**: {map_config.get('purpose', 'Transform data')}")
                    if map_config.get('target_system'):
                        analysis_lines.append(f"- **Target System**: {map_config['target_system']}")
                    if map_config.get('target_object'):
                        analysis_lines.append(f"- **Target Object**: {map_config['target_object']}")
                else:
                    analysis_lines.append("- **Purpose**: Transforms data between different formats")

            elif shape_type == 'connectoraction':
                analysis_lines.append("- **Type**: Connector Action")
                if config.get('connector_action'):
                    conn_action = config['connector_action']
                    analysis_lines.append(f"- **Action Type**: {conn_action.get('action_type', 'Unknown')}")
                    analysis_lines.append(f"- **Connector Type**: {conn_action.get('connector_type', 'Unknown')}")

                    if conn_action.get('detailed_analysis'):
                        analysis = conn_action['detailed_analysis']
                        analysis_lines.append(f"- **System**: {analysis.get('system', 'Unknown')}")
                        analysis_lines.append(f"- **Purpose**: {analysis.get('purpose', 'Unknown')}")
                        analysis_lines.append(f"- **Authentication**: {analysis.get('authentication', 'Unknown')}")
                        analysis_lines.append(f"- **API Type**: {analysis.get('api_type', 'Unknown')}")
                else:
                    analysis_lines.append("- **Purpose**: Performs connector operation")

            elif shape_type == 'stop':
                analysis_lines.append("- **Type**: Process completion/end event")
                analysis_lines.append("- **Purpose**: Completes the integration process")

            else:
                analysis_lines.append(f"- **Type**: {shape_type}")
                analysis_lines.append("- **Purpose**: Process step")

            analysis_lines.append("")

        return analysis_lines
