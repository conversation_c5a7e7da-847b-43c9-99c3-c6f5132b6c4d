<?xml version="1.0" encoding="UTF-8"?><bpmn2:definitions xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:ifl="http:///com.sap.ifl.model/Ifl.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1">
    <bpmn2:collaboration id="Collaboration_1" name="Default Collaboration">
        <bpmn2:extensionElements>
            <ifl:property>
                <key>namespaceMapping</key>
                <value/>
            </ifl:property>
            <ifl:property>
                <key>httpSessionHandling</key>
                <value>None</value>
            </ifl:property>
            <ifl:property>
                <key>accessControlMaxAge</key>
                <value/>
            </ifl:property>
            <ifl:property>
                <key>returnExceptionToSender</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>log</key>
                <value>All events</value>
            </ifl:property>
            <ifl:property>
                <key>corsEnabled</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>exposedHeaders</key>
                <value/>
            </ifl:property>
            <ifl:property>
                <key>componentVersion</key>
                <value>1.2</value>
            </ifl:property>
            <ifl:property>
                <key>allowedHeaderList</key>
                <value/>
            </ifl:property>
            <ifl:property>
                <key>ServerTrace</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>allowedOrigins</key>
                <value/>
            </ifl:property>
            <ifl:property>
                <key>accessControlAllowCredentials</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>allowedHeaders</key>
                <value/>
            </ifl:property>
            <ifl:property>
                <key>allowedMethods</key>
                <value/>
            </ifl:property>
            <ifl:property>
                <key>cmdVariantUri</key>
                <value>ctype::IFlowVariant/cname::IFlowConfiguration/version::1.2.4</value>
            </ifl:property>
        </bpmn2:extensionElements>
        <bpmn2:participant id="Participant_1" ifl:type="EndpointSender" name="Sender">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>enableBasicAuthentication</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>ifl:type</key>
                    <value>EndpointSender</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:participant>
        <bpmn2:participant id="Participant_2" ifl:type="EndpointRecevier" name="Receiver">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>ifl:type</key>
                    <value>EndpointRecevier</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:participant>
        <bpmn2:participant id="Participant_4" ifl:type="EndpointRecevier" name="Receiver1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>ifl:type</key>
                    <value>EndpointRecevier</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:participant>
        <bpmn2:participant id="Participant_26" ifl:type="EndpointRecevier" name="Receiver2">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>ifl:type</key>
                    <value>EndpointRecevier</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:participant>
        <bpmn2:participant id="Participant_Process_1" ifl:type="IntegrationProcess" name="Integration Process" processRef="Process_1">
            <bpmn2:extensionElements/>
        </bpmn2:participant>
        <bpmn2:participant id="Participant_Process_29" ifl:type="IntegrationProcess" name="Local Integration Process 1" processRef="Process_29">
            <bpmn2:extensionElements/>
        </bpmn2:participant>
        <bpmn2:messageFlow id="MessageFlow_27" name="OData" sourceRef="ServiceTask_25" targetRef="Participant_26">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>batchEntities</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>Description</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>pagination</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>odataCertAuthPrivateKeyAlias</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>ComponentNS</key>
                    <value>sap</value>
                </ifl:property>
                <ifl:property>
                    <key>metadataAllowedURIParams</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>Name</key>
                    <value>OData</value>
                </ifl:property>
                <ifl:property>
                    <key>TransportProtocolVersion</key>
                    <value>1.25.1</value>
                </ifl:property>
                <ifl:property>
                    <key>ComponentSWCVName</key>
                    <value>external</value>
                </ifl:property>
                <ifl:property>
                    <key>proxyPort</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>enableMPLAttachments</key>
                    <value>true</value>
                </ifl:property>
                <ifl:property>
                    <key>csrfEnabled</key>
                    <value>true</value>
                </ifl:property>
                <ifl:property>
                    <key>receiveTimeOut</key>
                    <value>1</value>
                </ifl:property>
                <ifl:property>
                    <key>connectionReuse</key>
                    <value>true</value>
                </ifl:property>
                <ifl:property>
                    <key>alias</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>MessageProtocol</key>
                    <value>OData V4</value>
                </ifl:property>
                <ifl:property>
                    <key>ComponentSWCVId</key>
                    <value>1.25.1</value>
                </ifl:property>
                <ifl:property>
                    <key>direction</key>
                    <value>Receiver</value>
                </ifl:property>
                <ifl:property>
                    <key>scc_location_id</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>metadataAllowedHeaders</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>ComponentType</key>
                    <value>HCIOData</value>
                </ifl:property>
                <ifl:property>
                    <key>address</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>resourcePathForOdatav4</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>isXSDGenerationRequired</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>allowChunking</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>queryOptions</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>proxyType</key>
                    <value>default</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.17</value>
                </ifl:property>
                <ifl:property>
                    <key>proxyHost</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>edmxFilePath</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>system</key>
                    <value>Receiver2</value>
                </ifl:property>
                <ifl:property>
                    <key>authenticationMethod</key>
                    <value>None</value>
                </ifl:property>
                <ifl:property>
                    <key>whitelistResponseHeaders</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>TransportProtocol</key>
                    <value>HTTP</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::AdapterVariant/cname::sap:HCIOData/tp::HTTP/mp::OData V4/direction::Receiver/version::1.17.0</value>
                </ifl:property>
                <ifl:property>
                    <key>fields</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>whitelistRequestHeaders</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>operation</key>
                    <value>get</value>
                </ifl:property>
                <ifl:property>
                    <key>MessageProtocolVersion</key>
                    <value>1.25.1</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:messageFlow>
    </bpmn2:collaboration>
    <bpmn2:process id="Process_1" name="Integration Process">
        <bpmn2:extensionElements>
            <ifl:property>
                <key>transactionTimeout</key>
                <value>30</value>
            </ifl:property>
            <ifl:property>
                <key>componentVersion</key>
                <value>1.2</value>
            </ifl:property>
            <ifl:property>
                <key>cmdVariantUri</key>
                <value>ctype::FlowElementVariant/cname::IntegrationProcess/version::1.2.1</value>
            </ifl:property>
            <ifl:property>
                <key>transactionalHandling</key>
                <value>Not Required</value>
            </ifl:property>
        </bpmn2:extensionElements>
        <bpmn2:callActivity id="CallActivity_11" name="Operation Mapping 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>mappinguri</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>mappingname</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>mappingType</key>
                    <value>OperationMapping</value>
                </ifl:property>
                <ifl:property>
                    <key>mappingpath</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.1</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Mapping</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::OperationMapping/version::1.1.0</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_10" name="XSLT Mapping 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>mappingoutputformat</key>
                    <value>Bytes</value>
                </ifl:property>
                <ifl:property>
                    <key>mappingHeaderNameKey</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>mappingpath</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>mappingSource</key>
                    <value>mappingSrcIflow</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.2</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Mapping</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::XSLTMapping/version::1.2.0</value>
                </ifl:property>
                <ifl:property>
                    <key>subActivityType</key>
                    <value>XSLTMapping</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_47" name="XML Validator 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>xmlSchemaSource</key>
                    <value>iflowOption</value>
                </ifl:property>
                <ifl:property>
                    <key>preventException</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>xsd</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>2.2</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>XmlValidator</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::XmlValidator/version::2.2.3</value>
                </ifl:property>
                <ifl:property>
                    <key>headerSource</key>
                    <value/>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_21" name="Filter 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>xpathType</key>
                    <value>Nodelist</value>
                </ifl:property>
                <ifl:property>
                    <key>wrapContent</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.1</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Filter</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Filter/version::1.1.0</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_22" name="Groovy Script 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>scriptFunction</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>scriptBundleId</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.1</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Script</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::GroovyScript/version::1.1.2</value>
                </ifl:property>
                <ifl:property>
                    <key>subActivityType</key>
                    <value>GroovyScript</value>
                </ifl:property>
                <ifl:property>
                    <key>script</key>
                    <value/>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:serviceTask id="ServiceTask_25" name="Request Reply 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>ExternalCall</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::ExternalCall/version::1.0.4</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:serviceTask>
        <bpmn2:parallelGateway id="ParallelGateway_37" name="Sequential Multicast 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>routingSequenceTable</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.1</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>SequentialMulticast</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::SequentialMulticast/version::1.1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>subActivityType</key>
                    <value>parallel</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:parallelGateway>
        <bpmn2:parallelGateway id="ParallelGateway_35" name="Join 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Join</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Join/version::1.0.0</value>
                </ifl:property>
                <ifl:property>
                    <key>subActivityType</key>
                    <value>parallel</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:parallelGateway>
        <bpmn2:callActivity id="CallActivity_19" name="EDI Extractor 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>2.1</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>EDIExtractor</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::EDIExtractor/version::2.1.0</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:endEvent id="EndEvent_7" name="Error End 1">
            <bpmn2:errorEventDefinition>
                <bpmn2:extensionElements>
                    <ifl:property>
                        <key>cmdVariantUri</key>
                        <value>ctype::FlowstepVariant/cname::ErrorEndEvent</value>
                    </ifl:property>
                    <ifl:property>
                        <key>activityType</key>
                        <value>EndErrorEvent</value>
                    </ifl:property>
                </bpmn2:extensionElements>
            </bpmn2:errorEventDefinition>
        </bpmn2:endEvent>
        <bpmn2:startEvent id="StartEvent_2" name="Start">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::MessageStartEvent/version::1.0</value>
                </ifl:property>
            </bpmn2:extensionElements>
            <bpmn2:outgoing>SequenceFlow_3</bpmn2:outgoing>
            <bpmn2:messageEventDefinition/>
        </bpmn2:startEvent>
        <bpmn2:parallelGateway id="ParallelGateway_36" name="Parallel Multicast 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.1</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Multicast</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Multicast/version::1.1.1</value>
                </ifl:property>
                <ifl:property>
                    <key>subActivityType</key>
                    <value>parallel</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:parallelGateway>
        <bpmn2:callActivity id="CallActivity_44" name="Select 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>visibility</key>
                    <value>local</value>
                </ifl:property>
                <ifl:property>
                    <key>maxresults</key>
                    <value>1</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.7</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>DBstorage</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::select/version::1.7.1</value>
                </ifl:property>
                <ifl:property>
                    <key>operation</key>
                    <value>select</value>
                </ifl:property>
                <ifl:property>
                    <key>delete</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>storageName</key>
                    <value/>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:endEvent id="EndEvent_2" name="End">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.1</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::MessageEndEvent/version::1.1.0</value>
                </ifl:property>
            </bpmn2:extensionElements>
            <bpmn2:incoming>SequenceFlow_3</bpmn2:incoming>
            <bpmn2:messageEventDefinition/>
        </bpmn2:endEvent>
        <bpmn2:endEvent id="EndEvent_6" name="End 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::EndEvent</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>EndEvent</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:endEvent>
        <bpmn2:serviceTask id="ServiceTask_24" name="Content Enricher 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>enrichmentType</key>
                    <value>multiMappingAggregation</value>
                </ifl:property>
                <ifl:property>
                    <key>resourceMessageNodePath</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>originalMessageNodePath</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>originalMessageKeyElement</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>resourceMessageKeyElement</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.2</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>contentEnricherWithLookup</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::contentEnricherWithLookup/version::1.2.0</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:serviceTask>
        <bpmn2:callActivity id="CallActivity_23" name="XML Modifier 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>removeExternalDTDs</key>
                    <value>0</value>
                </ifl:property>
                <ifl:property>
                    <key>removeXmlDeclaration</key>
                    <value>0</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.1</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>XmlModifier</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::XmlModifier/version::1.1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>xmlCharacterHandling</key>
                    <value>none</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_46" name="Write Variables 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>visibility</key>
                    <value>local</value>
                </ifl:property>
                <ifl:property>
                    <key>encrypt</key>
                    <value>true</value>
                </ifl:property>
                <ifl:property>
                    <key>expire</key>
                    <value>90</value>
                </ifl:property>
                <ifl:property>
                    <key>variable</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.2</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Variables</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Variables/version::1.2.0</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_39" name="EDI Splitter 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>edifactSourceEncoding</key>
                    <value>UTF-8</value>
                </ifl:property>
                <ifl:property>
                    <key>tradacomsHeaderName</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>edifactNumberRangeName</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>edifactValidateMessage</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>edifactTransactionMode</key>
                    <value>interchange</value>
                </ifl:property>
                <ifl:property>
                    <key>edifactUnaRequired</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>tradacomsSplitPreference</key>
                    <value>NoSplit</value>
                </ifl:property>
                <ifl:property>
                    <key>x12TSNumberFrom</key>
                    <value>predefined</value>
                </ifl:property>
                <ifl:property>
                    <key>x12ExcludeAk3Ak4</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>x12InterchangeNumberFrom</key>
                    <value>useFromEDIMessage</value>
                </ifl:property>
                <ifl:property>
                    <key>x12UniqueTSNumber</key>
                    <value>notRequired</value>
                </ifl:property>
                <ifl:property>
                    <key>x12UniqueInterchangeNumber</key>
                    <value>notRequired</value>
                </ifl:property>
                <ifl:property>
                    <key>x12EdiSchemaSource</key>
                    <value>IntegrationProject</value>
                </ifl:property>
                <ifl:property>
                    <key>x12UniqueFGNumber</key>
                    <value>notRequired</value>
                </ifl:property>
                <ifl:property>
                    <key>x12ValidateMessageOptions</key>
                    <value>none</value>
                </ifl:property>
                <ifl:property>
                    <key>parallelProcessing</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>x12FGNumberRangeName</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>edifactContrlVersion</key>
                    <value>defaultVersion</value>
                </ifl:property>
                <ifl:property>
                    <key>tradacomsSchemaTable</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>x12SchemaTable</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>edifactSchemaTable</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>tradacomsSourceEncoding</key>
                    <value>ISO-8859-1</value>
                </ifl:property>
                <ifl:property>
                    <key>edifactValidate</key>
                    <value>envelopAndMessage</value>
                </ifl:property>
                <ifl:property>
                    <key>x12SourceEncoding</key>
                    <value>UTF-8</value>
                </ifl:property>
                <ifl:property>
                    <key>edifactSendInvalidMessages</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>edifactCreateAck</key>
                    <value>notRequired</value>
                </ifl:property>
                <ifl:property>
                    <key>edifactUniqueInterchangeNumber</key>
                    <value>notRequired</value>
                </ifl:property>
                <ifl:property>
                    <key>tradacomsEdiSchemaSource</key>
                    <value>IntegrationProject</value>
                </ifl:property>
                <ifl:property>
                    <key>x12CreateAck</key>
                    <value>notRequired</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>2.9</value>
                </ifl:property>
                <ifl:property>
                    <key>x12SendInvalidMessages</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>edifactHeaderName</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>splitType</key>
                    <value>EDISplitter</value>
                </ifl:property>
                <ifl:property>
                    <key>timeOut</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>x12NumberRangeName</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>edifactDecimalCharacter</key>
                    <value>dot</value>
                </ifl:property>
                <ifl:property>
                    <key>x12TransactionMode</key>
                    <value>interchange</value>
                </ifl:property>
                <ifl:property>
                    <key>x12TSNumberRangeName</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>x12HeaderName</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>x12FunctionalGroupNumberFrom</key>
                    <value>predefined</value>
                </ifl:property>
                <ifl:property>
                    <key>edifactCustomizeEnvelope</key>
                    <value>fromIncomingPayload</value>
                </ifl:property>
                <ifl:property>
                    <key>x12CustomizeEnvelope</key>
                    <value>fromIncomingPayload</value>
                </ifl:property>
                <ifl:property>
                    <key>edifactInterchangeNumberFrom</key>
                    <value>useFromEDIMessage</value>
                </ifl:property>
                <ifl:property>
                    <key>edifactEdiSchemaSource</key>
                    <value>IntegrationProject</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Splitter</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::EDISplitter/version::2.9.0</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_34" name="Gather 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>targetXPath</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>sourceXPath</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>messageType</key>
                    <value>SameXMLFormat</value>
                </ifl:property>
                <ifl:property>
                    <key>aggregationAlgorithm</key>
                    <value>sap-identical-multi-mapping</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.2</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Gather</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Gather/version::1.2.0</value>
                </ifl:property>
                <ifl:property>
                    <key>gatherFileNames</key>
                    <value/>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_45" name="Persist 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>stepid</key>
                    <value>Persist1</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Persist</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Persist/version::1.0.2</value>
                </ifl:property>
                <ifl:property>
                    <key>enableEncrypt</key>
                    <value>true</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_40" name="IDoc Splitter 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.1</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Splitter</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::IDoc/version::1.1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>splitType</key>
                    <value>IDoc</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_14" name="JSON to XML Converter 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>additionalRootElementName</key>
                    <value>root</value>
                </ifl:property>
                <ifl:property>
                    <key>jsonNamespaceMapping</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.1</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>JsonToXmlConverter</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::JsonToXmlConverter/version::1.1.2</value>
                </ifl:property>
                <ifl:property>
                    <key>useNamespaces</key>
                    <value>true</value>
                </ifl:property>
                <ifl:property>
                    <key>addXMLRootElement</key>
                    <value>true</value>
                </ifl:property>
                <ifl:property>
                    <key>additionalRootElementNamespace</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>jsonNamespaceSeparator</key>
                    <value>:</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_28" name="Process Call 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>processId</key>
                    <value>Process_29</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>ProcessCallElement</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::NonLoopingProcess/version::1.0.3</value>
                </ifl:property>
                <ifl:property>
                    <key>subActivityType</key>
                    <value>NonLoopingProcess</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_13" name="Content Modifier 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>bodyType</key>
                    <value>constant</value>
                </ifl:property>
                <ifl:property>
                    <key>propertyTable</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>headerTable</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>wrapContent</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.6</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Enricher</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Enricher/version::1.6.1</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_41" name="General Splitter 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>exprType</key>
                    <value>XPath</value>
                </ifl:property>
                <ifl:property>
                    <key>Streaming</key>
                    <value>true</value>
                </ifl:property>
                <ifl:property>
                    <key>StopOnExecution</key>
                    <value>true</value>
                </ifl:property>
                <ifl:property>
                    <key>SplitterThreads</key>
                    <value>10</value>
                </ifl:property>
                <ifl:property>
                    <key>splitExprValue</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>ParallelProcessing</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.6</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Splitter</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::GeneralSplitter/version::1.6.0</value>
                </ifl:property>
                <ifl:property>
                    <key>grouping</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>splitType</key>
                    <value>GeneralSplitter</value>
                </ifl:property>
                <ifl:property>
                    <key>timeOut</key>
                    <value>300</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_33" name="Aggregator 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>incomingFormat</key>
                    <value>XML_SAME_FORMAT</value>
                </ifl:property>
                <ifl:property>
                    <key>aggregationAlgorithm</key>
                    <value>sap-sequenced-id-list</value>
                </ifl:property>
                <ifl:property>
                    <key>messageSequenceExpression</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>lastMessageCondition</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>correlationExpression</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>datastoreName</key>
                    <value>Aggregator-1</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Aggregator</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Aggregator/version::1.0.5</value>
                </ifl:property>
                <ifl:property>
                    <key>timeout</key>
                    <value>60</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:startEvent id="StartEvent_8" name="Start Timer 1">
            <bpmn2:timerEventDefinition id="TimerEventDefinition_6656">
                <bpmn2:extensionElements>
                    <ifl:property>
                        <key>scheduleKey</key>
                        <value>&lt;row&gt;&lt;cell&gt;dateType&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;timeType&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;dayValue&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;monthValue&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;yearValue&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;onWeekly&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;onMonthly&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;OnEveryMinute&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;fromInterval&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;toInterval&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;timeZone&lt;/cell&gt;&lt;cell&gt;( UTC 0:00 ) Greenwich Mean Time(Etc/GMT)&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;secondValue&lt;/cell&gt;&lt;cell&gt;0&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;minutesValue&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;hourValue&lt;/cell&gt;&lt;cell&gt;&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;triggerType&lt;/cell&gt;&lt;cell&gt;simple&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;noOfSchedules&lt;/cell&gt;&lt;cell&gt;1&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell&gt;schedule1&lt;/cell&gt;&lt;cell&gt;fireNow=true&lt;/cell&gt;&lt;/row&gt;</value>
                    </ifl:property>
                    <ifl:property>
                        <key>componentVersion</key>
                        <value>1.4</value>
                    </ifl:property>
                    <ifl:property>
                        <key>cmdVariantUri</key>
                        <value>ctype::FlowstepVariant/cname::intermediatetimer/version::1.4.0</value>
                    </ifl:property>
                    <ifl:property>
                        <key>activityType</key>
                        <value>StartTimerEvent</value>
                    </ifl:property>
                </bpmn2:extensionElements>
            </bpmn2:timerEventDefinition>
        </bpmn2:startEvent>
        <bpmn2:callActivity id="CallActivity_9" name="Message Mapping 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>mappinguri</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>mappingname</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>mappingSourceValue</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>mappingType</key>
                    <value>MessageMapping</value>
                </ifl:property>
                <ifl:property>
                    <key>mappingReference</key>
                    <value>static</value>
                </ifl:property>
                <ifl:property>
                    <key>mappingpath</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.3</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Mapping</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::MessageMapping/version::1.3.1</value>
                </ifl:property>
                <ifl:property>
                    <key>messageMappingBundleId</key>
                    <value/>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_43" name="Write 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>visibility</key>
                    <value>local</value>
                </ifl:property>
                <ifl:property>
                    <key>alert</key>
                    <value>2</value>
                </ifl:property>
                <ifl:property>
                    <key>encrypt</key>
                    <value>true</value>
                </ifl:property>
                <ifl:property>
                    <key>expire</key>
                    <value>30</value>
                </ifl:property>
                <ifl:property>
                    <key>messageId</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.7</value>
                </ifl:property>
                <ifl:property>
                    <key>override</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>DBstorage</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::put/version::1.7.1</value>
                </ifl:property>
                <ifl:property>
                    <key>operation</key>
                    <value>put</value>
                </ifl:property>
                <ifl:property>
                    <key>storageName</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>includeMessageHeaders</key>
                    <value>false</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:exclusiveGateway id="ExclusiveGateway_38" name="Router 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.1</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>ExclusiveGateway</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::ExclusiveGateway/version::1.1.2</value>
                </ifl:property>
                <ifl:property>
                    <key>throwException</key>
                    <value>false</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:exclusiveGateway>
        <bpmn2:callActivity id="CallActivity_16" name="XML to CSV Converter 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>Field_Separator_in_CSV</key>
                    <value>,</value>
                </ifl:property>
                <ifl:property>
                    <key>CSV_Header</key>
                    <value>none</value>
                </ifl:property>
                <ifl:property>
                    <key>XML_Schema_File_Path</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>Include_Attribute</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>Include_Master</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.2</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>XmlToCsvConverter</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::XmlToCsvConverter/version::1.2.0</value>
                </ifl:property>
                <ifl:property>
                    <key>Master_XPath_Field_Location</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>XPath_Field_Location</key>
                    <value/>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_17" name="CSV to XML Converter 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>Field_Separator_in_CSV</key>
                    <value>,</value>
                </ifl:property>
                <ifl:property>
                    <key>ignoreFirstLineAsHeader</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>XML_Schema_File_Path</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>headerMapping</key>
                    <value>mapHeadersToXSD</value>
                </ifl:property>
                <ifl:property>
                    <key>Record_Identifier_in_CSV</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.4</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>CsvToXmlConverter</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::CsvToXmlConverter/version::1.4.0</value>
                </ifl:property>
                <ifl:property>
                    <key>XPath_Field_Location</key>
                    <value/>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_18" name="Base64 Decoder 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Decoder</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Base64 Decode/version::1.0.1</value>
                </ifl:property>
                <ifl:property>
                    <key>encoderType</key>
                    <value>Base64 Decode</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_15" name="XML to JSON Converter 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>xmlJsonUseStreaming</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>xmlJsonSuppressRootElement</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>xmlJsonPathTable</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>jsonOutputEncoding</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>jsonNamespaceMapping</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>xmlJsonConvertAllElements</key>
                    <value>specific</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>XmlToJsonConverter</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::XmlToJsonConverter/version::1.0.8</value>
                </ifl:property>
                <ifl:property>
                    <key>useNamespaces</key>
                    <value>true</value>
                </ifl:property>
                <ifl:property>
                    <key>jsonNamespaceSeparator</key>
                    <value>:</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_42" name="Get 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>visibility</key>
                    <value>local</value>
                </ifl:property>
                <ifl:property>
                    <key>dataStoreId</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.7</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>DBstorage</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::get/version::1.7.1</value>
                </ifl:property>
                <ifl:property>
                    <key>operation</key>
                    <value>get</value>
                </ifl:property>
                <ifl:property>
                    <key>delete</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>stopOnMissingEntry</key>
                    <value>true</value>
                </ifl:property>
                <ifl:property>
                    <key>storageName</key>
                    <value/>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_12" name="ID Mapping 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>visibility</key>
                    <value>local</value>
                </ifl:property>
                <ifl:property>
                    <key>sourceMessageID</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>expire</key>
                    <value>30</value>
                </ifl:property>
                <ifl:property>
                    <key>context</key>
                    <value>IDMapper1</value>
                </ifl:property>
                <ifl:property>
                    <key>targetHeader</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>IDMapper</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::IDMapper/version::1.0.0</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_48" name="EDI Validator 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>headerName</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>ediSchemaSource</key>
                    <value>IntegrationProject</value>
                </ifl:property>
                <ifl:property>
                    <key>sourceEncoding</key>
                    <value>ISO-8859-1</value>
                </ifl:property>
                <ifl:property>
                    <key>preventException</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.7</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>EDIValidator</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::EDIValidator/version::1.7.0</value>
                </ifl:property>
                <ifl:property>
                    <key>schemaTable</key>
                    <value/>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:callActivity id="CallActivity_20" name="Base64 Encoder 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.0</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Encoder</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Base64 Encode/version::1.0.1</value>
                </ifl:property>
                <ifl:property>
                    <key>encoderType</key>
                    <value>Base64 Encode</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:callActivity>
        <bpmn2:sequenceFlow id="SequenceFlow_3" sourceRef="StartEvent_2" targetRef="EndEvent_2"/>
    </bpmn2:process>
    <bpmn2:process id="Process_29" name="Local Integration Process 1">
        <bpmn2:extensionElements>
            <ifl:property>
                <key>transactionTimeout</key>
                <value>30</value>
            </ifl:property>
            <ifl:property>
                <key>processType</key>
                <value>directCall</value>
            </ifl:property>
            <ifl:property>
                <key>componentVersion</key>
                <value>1.1</value>
            </ifl:property>
            <ifl:property>
                <key>cmdVariantUri</key>
                <value>ctype::FlowElementVariant/cname::LocalIntegrationProcess/version::1.1.3</value>
            </ifl:property>
            <ifl:property>
                <key>transactionalHandling</key>
                <value>From Calling Process</value>
            </ifl:property>
        </bpmn2:extensionElements>
        <bpmn2:startEvent id="StartEvent_30" name="Start 1">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::StartEvent</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>StartEvent</value>
                </ifl:property>
            </bpmn2:extensionElements>
            <bpmn2:outgoing>SequenceFlow_32</bpmn2:outgoing>
        </bpmn2:startEvent>
        <bpmn2:endEvent id="EndEvent_31" name="End 2">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::EndEvent</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>EndEvent</value>
                </ifl:property>
            </bpmn2:extensionElements>
            <bpmn2:incoming>SequenceFlow_32</bpmn2:incoming>
        </bpmn2:endEvent>
        <bpmn2:sequenceFlow id="SequenceFlow_32" sourceRef="StartEvent_30" targetRef="EndEvent_31"/>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1" name="Default Collaboration Diagram">
        <bpmndi:BPMNPlane bpmnElement="Collaboration_1" id="BPMNPlane_1">
            <bpmndi:BPMNShape bpmnElement="EndEvent_2" id="BPMNShape_EndEvent_2">
                <dc:Bounds height="32.0" width="32.0" x="780.0" y="142.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="StartEvent_2" id="BPMNShape_StartEvent_2">
                <dc:Bounds height="32.0" width="32.0" x="369.0" y="142.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="Participant_2" id="BPMNShape_Participant_2">
                <dc:Bounds height="140.0" width="100.0" x="900.0" y="100.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="Participant_1" id="BPMNShape_Participant_1">
                <dc:Bounds height="140.0" width="100.0" x="40.0" y="100.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="Participant_Process_1" id="BPMNShape_Participant_Process_1">
                <dc:Bounds height="900.0" width="1245.0" x="85.0" y="60.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="Participant_4" id="BPMNShape_Participant_4">
                <dc:Bounds height="140.0" width="100.0" x="340.0" y="-107.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="EndEvent_6" id="BPMNShape_EndEvent_6">
                <dc:Bounds height="32.0" width="32.0" x="740.0" y="224.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="EndEvent_7" id="BPMNShape_EndEvent_7">
                <dc:Bounds height="32.0" width="32.0" x="620.0" y="224.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="StartEvent_8" id="BPMNShape_StartEvent_8">
                <dc:Bounds height="32.0" width="32.0" x="342.0" y="240.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_9" id="BPMNShape_CallActivity_9">
                <dc:Bounds height="60.0" width="100.0" x="414.0" y="338.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_10" id="BPMNShape_CallActivity_10">
                <dc:Bounds height="60.0" width="100.0" x="552.0" y="336.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_11" id="BPMNShape_CallActivity_11">
                <dc:Bounds height="60.0" width="100.0" x="690.0" y="327.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_12" id="BPMNShape_CallActivity_12">
                <dc:Bounds height="60.0" width="100.0" x="282.0" y="344.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_13" id="BPMNShape_CallActivity_13">
                <dc:Bounds height="60.0" width="100.0" x="287.0" y="438.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_14" id="BPMNShape_CallActivity_14">
                <dc:Bounds height="60.0" width="100.0" x="422.0" y="438.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_15" id="BPMNShape_CallActivity_15">
                <dc:Bounds height="60.0" width="100.0" x="552.0" y="438.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_16" id="BPMNShape_CallActivity_16">
                <dc:Bounds height="60.0" width="100.0" x="681.0" y="438.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_17" id="BPMNShape_CallActivity_17">
                <dc:Bounds height="60.0" width="100.0" x="800.0" y="433.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_18" id="BPMNShape_CallActivity_18">
                <dc:Bounds height="60.0" width="100.0" x="287.0" y="534.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_19" id="BPMNShape_CallActivity_19">
                <dc:Bounds height="60.0" width="100.0" x="422.0" y="534.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_20" id="BPMNShape_CallActivity_20">
                <dc:Bounds height="60.0" width="100.0" x="560.0" y="534.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_21" id="BPMNShape_CallActivity_21">
                <dc:Bounds height="60.0" width="100.0" x="700.0" y="534.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_22" id="BPMNShape_CallActivity_22">
                <dc:Bounds height="60.0" width="100.0" x="827.0" y="534.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_23" id="BPMNShape_CallActivity_23">
                <dc:Bounds height="60.0" width="100.0" x="287.0" y="669.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="ServiceTask_24" id="BPMNShape_ServiceTask_24">
                <dc:Bounds height="60.0" width="100.0" x="440.0" y="662.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="ServiceTask_25" id="BPMNShape_ServiceTask_25">
                <dc:Bounds height="60.0" width="100.0" x="590.0" y="662.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="Participant_26" id="BPMNShape_Participant_26">
                <dc:Bounds height="140.0" width="100.0" x="584.0" y="946.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_28" id="BPMNShape_CallActivity_28">
                <dc:Bounds height="60.0" width="100.0" x="750.0" y="662.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="Participant_Process_29" id="BPMNShape_Participant_Process_29">
                <dc:Bounds height="220.0" width="540.0" x="1116.0" y="1069.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="StartEvent_30" id="BPMNShape_StartEvent_30">
                <dc:Bounds height="32.0" width="32.0" x="1161.0" y="1142.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="EndEvent_31" id="BPMNShape_EndEvent_31">
                <dc:Bounds height="32.0" width="32.0" x="1548.0" y="1142.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_33" id="BPMNShape_CallActivity_33">
                <dc:Bounds height="60.0" width="100.0" x="105.0" y="766.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_34" id="BPMNShape_CallActivity_34">
                <dc:Bounds height="60.0" width="100.0" x="242.0" y="766.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="ParallelGateway_35" id="BPMNShape_ParallelGateway_35">
                <dc:Bounds height="40.0" width="40.0" x="402.0" y="771.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="ParallelGateway_36" id="BPMNShape_ParallelGateway_36">
                <dc:Bounds height="40.0" width="40.0" x="500.0" y="771.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="ParallelGateway_37" id="BPMNShape_ParallelGateway_37">
                <dc:Bounds height="40.0" width="40.0" x="595.0" y="776.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="ExclusiveGateway_38" id="BPMNShape_ExclusiveGateway_38">
                <dc:Bounds height="40.0" width="40.0" x="680.0" y="781.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_39" id="BPMNShape_CallActivity_39">
                <dc:Bounds height="60.0" width="100.0" x="777.0" y="781.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_40" id="BPMNShape_CallActivity_40">
                <dc:Bounds height="60.0" width="100.0" x="905.0" y="781.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_41" id="BPMNShape_CallActivity_41">
                <dc:Bounds height="60.0" width="100.0" x="1036.0" y="781.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_42" id="BPMNShape_CallActivity_42">
                <dc:Bounds height="60.0" width="100.0" x="105.0" y="864.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_43" id="BPMNShape_CallActivity_43">
                <dc:Bounds height="60.0" width="100.0" x="242.0" y="858.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_44" id="BPMNShape_CallActivity_44">
                <dc:Bounds height="60.0" width="100.0" x="392.0" y="858.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_45" id="BPMNShape_CallActivity_45">
                <dc:Bounds height="60.0" width="100.0" x="755.0" y="869.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_46" id="BPMNShape_CallActivity_46">
                <dc:Bounds height="60.0" width="100.0" x="900.0" y="864.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_47" id="BPMNShape_CallActivity_47">
                <dc:Bounds height="60.0" width="100.0" x="1036.0" y="864.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="CallActivity_48" id="BPMNShape_CallActivity_48">
                <dc:Bounds height="60.0" width="100.0" x="1200.0" y="858.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge bpmnElement="SequenceFlow_3" id="BPMNEdge_SequenceFlow_3" sourceElement="BPMNShape_StartEvent_2" targetElement="BPMNShape_EndEvent_2">
                <di:waypoint x="400.5" xsi:type="dc:Point" y="158.0"/>
                <di:waypoint x="780.5" xsi:type="dc:Point" y="158.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="MessageFlow_27" id="BPMNEdge_MessageFlow_27" sourceElement="BPMNShape_ServiceTask_25" targetElement="BPMNShape_Participant_26">
                <di:waypoint x="637.0" xsi:type="dc:Point" y="692.0"/>
                <di:waypoint x="637.0" xsi:type="dc:Point" y="946.5"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="SequenceFlow_32" id="BPMNEdge_SequenceFlow_32" sourceElement="BPMNShape_StartEvent_30" targetElement="BPMNShape_EndEvent_31">
                <di:waypoint x="1177.0" xsi:type="dc:Point" y="1158.0"/>
                <di:waypoint x="1564.0" xsi:type="dc:Point" y="1158.0"/>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>