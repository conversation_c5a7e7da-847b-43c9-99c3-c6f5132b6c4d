<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation with Mermaid</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #1565c0;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            background: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
        }
        pre.mermaid {
            text-align: center;
            background: white;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .note {
            background: #e3f2fd;
            padding: 10px;
            border-left: 4px solid #1565c0;
            margin: 10px 0;
        }
        .insights {
            background: #f3e5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .best-practices {
            background: #e8f5e9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .security {
            background: #ffebee;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1 id="create-salesforce-opportunities-from-stripe-subscriptions-integration">Create Salesforce Opportunities from Stripe Subscriptions Integration</h1>
<h2 id="table-of-contents">Table of Contents</h2>
<ul>
<li><a href="#create-salesforce-opportunities-from-stripe-subscriptions-integration">Create Salesforce Opportunities from Stripe Subscriptions Integration</a></li>
<li><a href="#table-of-contents">Table of Contents</a></li>
<li><a href="#api-overview">API Overview</a></li>
<li><a href="#endpoints">Endpoints</a></li>
<li><a href="#current-dell-boomi-flow-logic">Current Dell Boomi Flow Logic</a><ul>
<li><a href="#process-trigger">Process Trigger</a></li>
<li><a href="#main-processing-steps">Main Processing Steps</a></li>
<li><a href="#data-transformations">Data Transformations</a></li>
<li><a href="#expected-outcomes-and-error-scenarios">Expected Outcomes and Error Scenarios</a></li>
</ul>
</li>
<li><a href="#dataweave-transformations-explained">DataWeave Transformations Explained</a></li>
<li><a href="#sap-integration-suite-implementation">SAP Integration Suite Implementation</a><ul>
<li><a href="#component-mapping">Component Mapping</a></li>
<li><a href="#integration-flow-visualization">Integration Flow Visualization</a></li>
<li><a href="#configuration-details">Configuration Details</a></li>
</ul>
</li>
<li><a href="#environment-configuration">Environment Configuration</a></li>
<li><a href="#api-reference">API Reference</a></li>
</ul>
<h2 id="api-overview">API Overview</h2>
<p>This integration creates Salesforce Opportunities based on Stripe Subscription data. It listens for Stripe subscription events, processes the subscription data, and creates corresponding opportunity records in Salesforce.</p>
<ul>
<li><strong>Base URL/endpoint pattern</strong>: Not explicitly defined in the source documentation</li>
<li><strong>Authentication mechanisms</strong>: Likely uses OAuth for Salesforce and API keys for Stripe (based on typical integration patterns)</li>
<li><strong>Rate limiting information</strong>: Not specified in the source documentation</li>
<li><strong>General response format</strong>: Likely JSON responses from both Stripe and Salesforce</li>
</ul>
<h2 id="endpoints">Endpoints</h2>
<p>Based on the limited information in the source documentation, the integration appears to involve:</p>
<h3 id="stripe-subscription-events-endpoint">Stripe Subscription Events Endpoint</h3>
<ul>
<li><strong>HTTP Method and path</strong>: Not explicitly defined, but likely a webhook endpoint that receives Stripe subscription events</li>
<li><strong>Purpose</strong>: Receives subscription event data from Stripe</li>
<li><strong>Request parameters</strong>: Not specified in the source documentation</li>
<li><strong>Request body structure</strong>: Stripe subscription event data in JSON format</li>
<li><strong>Response format</strong>: Acknowledgment of receipt</li>
</ul>
<h3 id="salesforce-opportunity-creation-endpoint">Salesforce Opportunity Creation Endpoint</h3>
<ul>
<li><strong>HTTP Method and path</strong>: Not explicitly defined, but likely uses Salesforce REST API for opportunity creation</li>
<li><strong>Purpose</strong>: Creates new opportunity records in Salesforce</li>
<li><strong>Request parameters</strong>: Not specified in the source documentation</li>
<li><strong>Request body structure</strong>: Salesforce opportunity data in JSON format</li>
<li><strong>Response format</strong>: Salesforce API response with created record details</li>
</ul>
<h2 id="current-dell-boomi-flow-logic">Current Dell Boomi Flow Logic</h2>
<p>The Dell Boomi process "Create Salesforce Opportunities from Stripe Subscriptions" follows this flow:</p>
<h3 id="process-trigger">Process Trigger</h3>
<p>The process appears to be triggered by an event listener (shape1), which likely receives Stripe subscription events.</p>
<h3 id="main-processing-steps">Main Processing Steps</h3>
<ol>
<li><strong>Event Reception</strong>: The process starts with shape1, which receives the incoming data.</li>
<li><strong>Dynamic Property Setting</strong>: The data flows to a "Set Dynamic Properties" component (shape6), which likely configures runtime properties based on the incoming data.</li>
<li><strong>Data Transformation</strong>: The data then goes through a transformation step (shape4), which likely converts the Stripe subscription data format to Salesforce opportunity format.</li>
<li><strong>Data Processing</strong>: The transformed data is processed in shape3, which may include validation, enrichment, or other business logic.</li>
<li><strong>Data Sending</strong>: Finally, the data reaches shape5, which likely represents the endpoint that sends the data to Salesforce to create the opportunity.</li>
</ol>
<h3 id="data-transformations">Data Transformations</h3>
<p>The process includes a transformation step (shape4) that converts Stripe subscription data to Salesforce opportunity format. The exact transformation logic is not provided in the source documentation.</p>
<h3 id="expected-outcomes-and-error-scenarios">Expected Outcomes and Error Scenarios</h3>
<ul>
<li><strong>Success Scenario</strong>: Stripe subscription data is successfully transformed and a new opportunity is created in Salesforce.</li>
<li><strong>Error Scenarios</strong>: Not explicitly defined in the source documentation, but likely include:</li>
<li>Invalid or missing Stripe subscription data</li>
<li>Transformation errors</li>
<li>Salesforce API errors</li>
<li>Authentication failures</li>
</ul>
<h2 id="dataweave-transformations-explained">DataWeave Transformations Explained</h2>
<p>The source documentation does not provide specific DataWeave transformation code. In a typical Stripe to Salesforce integration, the transformation would:</p>
<ol>
<li>Extract relevant subscription data from the Stripe event payload</li>
<li>Map Stripe fields to corresponding Salesforce Opportunity fields</li>
<li>Format dates, currency amounts, and other data types as required by Salesforce</li>
<li>Add any additional required fields or default values</li>
</ol>
<h2 id="sap-integration-suite-implementation">SAP Integration Suite Implementation</h2>
<h3 id="component-mapping">Component Mapping</h3>
<table>
<thead>
<tr>
<th>Dell Boomi Component</th>
<th>SAP Integration Suite Equivalent</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td>shape1 (Event Listener)</td>
<td>HTTPS Adapter (Webhook Receiver)</td>
<td>Configured to receive Stripe webhook events</td>
</tr>
<tr>
<td>shape6 (Set Dynamic Properties)</td>
<td>Content Modifier</td>
<td>Used to set exchange properties based on incoming data</td>
</tr>
<tr>
<td>shape4 (Transform)</td>
<td>Message Mapping</td>
<td>Maps Stripe subscription data to Salesforce opportunity format</td>
</tr>
<tr>
<td>shape3 (Processing Step)</td>
<td>Content Modifier or Script</td>
<td>Implements business logic for data processing</td>
</tr>
<tr>
<td>shape5 (Data Sender)</td>
<td>Salesforce Adapter</td>
<td>Configured to create opportunities in Salesforce</td>
</tr>
</tbody>
</table>
<h3 id="integration-flow-visualization">Integration Flow Visualization</h3>
<pre class="mermaid">
flowchart TD
    %% Define node styles
    classDef httpAdapter fill:#87CEEB,stroke:#333,stroke-width:2px
    classDef contentModifier fill:#98FB98,stroke:#333,stroke-width:2px
    classDef router fill:#FFB6C1,stroke:#333,stroke-width:2px
    classDef mapping fill:#DDA0DD,stroke:#333,stroke-width:2px
    classDef exception fill:#FFA07A,stroke:#333,stroke-width:2px
    classDef processCall fill:#F0E68C,stroke:#333,stroke-width:2px

    %% Main Flow
    Start((Start)) --> StripeWebhook[HTTPS Adapter - Stripe Webhook]:::httpAdapter
    StripeWebhook --> SetDynamicProps[Content Modifier - Set Properties]:::contentModifier
    SetDynamicProps --> TransformData[Message Mapping - Stripe to Salesforce]:::mapping
    TransformData --> ProcessData[Content Modifier - Process Data]:::contentModifier
    ProcessData --> SalesforceConnector[Salesforce Adapter - Create Opportunity]:::httpAdapter
    SalesforceConnector --> End((End))

    %% Error Handling
    StripeWebhook -->|Error| ErrorHandler[(Global Error Handler)]:::exception
    SetDynamicProps -->|Error| ErrorHandler
    TransformData -->|Error| ErrorHandler
    ProcessData -->|Error| ErrorHandler
    SalesforceConnector -->|Error| ErrorHandler
    ErrorHandler --> LogError[Content Modifier - Log Error]:::contentModifier
    LogError --> ErrorEnd((Error End))
</pre>
<h3 id="configuration-details">Configuration Details</h3>
<h4 id="https-adapter-stripe-webhook">HTTPS Adapter - Stripe Webhook</h4>
<ul>
<li><strong>Address</strong>: <code>/stripe/webhook</code></li>
<li><strong>Authentication</strong>: API Key (from Stripe)</li>
<li><strong>CSRF Protection</strong>: Disabled</li>
<li><strong>Connection Timeout</strong>: 60 seconds</li>
</ul>
<h4 id="content-modifier-set-properties">Content Modifier - Set Properties</h4>
<ul>
<li><strong>Exchange Properties</strong>:</li>
<li><code>subscriptionId</code>: Extracted from Stripe event</li>
<li><code>customerId</code>: Extracted from Stripe event</li>
<li><code>planId</code>: Extracted from Stripe event</li>
<li><code>amount</code>: Extracted from Stripe event</li>
</ul>
<h4 id="message-mapping-stripe-to-salesforce">Message Mapping - Stripe to Salesforce</h4>
<ul>
<li><strong>Source Schema</strong>: Stripe Subscription Event Schema</li>
<li><strong>Target Schema</strong>: Salesforce Opportunity Schema</li>
<li><strong>Mapping</strong>:</li>
<li><code>Opportunity.Name</code>: <code>${property.subscriptionId} - ${property.planId}</code></li>
<li><code>Opportunity.Amount</code>: <code>${property.amount}</code></li>
<li><code>Opportunity.CloseDate</code>: Current date</li>
<li><code>Opportunity.StageName</code>: "Closed Won"</li>
<li><code>Opportunity.Type</code>: "New Business"</li>
</ul>
<h4 id="content-modifier-process-data">Content Modifier - Process Data</h4>
<ul>
<li><strong>Exchange Body</strong>: Unchanged</li>
<li><strong>Headers</strong>:</li>
<li><code>Content-Type</code>: <code>application/json</code></li>
</ul>
<h4 id="salesforce-adapter-create-opportunity">Salesforce Adapter - Create Opportunity</h4>
<ul>
<li><strong>Operation</strong>: Create</li>
<li><strong>Object Type</strong>: Opportunity</li>
<li><strong>Authentication</strong>: OAuth</li>
<li><strong>Connection Timeout</strong>: 60 seconds</li>
</ul>
<h4 id="global-error-handler">Global Error Handler</h4>
<ul>
<li><strong>Log Level</strong>: Error</li>
<li><strong>Retry Mechanism</strong>: None (or as configured)</li>
<li><strong>Error Response</strong>: JSON error details</li>
</ul>
<h2 id="environment-configuration">Environment Configuration</h2>
<p>Based on the limited information in the source documentation, the following configuration details are inferred:</p>
<h3 id="important-configuration-parameters">Important Configuration Parameters</h3>
<ul>
<li>Stripe webhook endpoint configuration</li>
<li>Salesforce API connection details</li>
<li>Error handling and logging settings</li>
</ul>
<h3 id="environment-variables">Environment Variables</h3>
<ul>
<li><code>STRIPE_API_KEY</code>: API key for authenticating with Stripe</li>
<li><code>SALESFORCE_CLIENT_ID</code>: OAuth client ID for Salesforce</li>
<li><code>SALESFORCE_CLIENT_SECRET</code>: OAuth client secret for Salesforce</li>
<li><code>SALESFORCE_USERNAME</code>: Salesforce username</li>
<li><code>SALESFORCE_PASSWORD</code>: Salesforce password</li>
<li><code>SALESFORCE_SECURITY_TOKEN</code>: Salesforce security token</li>
</ul>
<h3 id="dependencies-on-external-systems">Dependencies on External Systems</h3>
<ul>
<li>Stripe API</li>
<li>Salesforce API</li>
</ul>
<h3 id="security-settings">Security Settings</h3>
<ul>
<li>HTTPS for all communications</li>
<li>OAuth 2.0 for Salesforce authentication</li>
<li>API key authentication for Stripe</li>
</ul>
<h3 id="deployment-considerations">Deployment Considerations</h3>
<ul>
<li>Webhook endpoint must be publicly accessible</li>
<li>Network access to both Stripe and Salesforce APIs</li>
<li>Proper error handling and monitoring</li>
</ul>
<h3 id="required-resources">Required Resources</h3>
<ul>
<li>Minimal CPU and memory requirements (specific values not provided)</li>
<li>Network bandwidth for API communications</li>
</ul>
<h2 id="api-reference">API Reference</h2>
<h3 id="stripe-webhook-endpoint">Stripe Webhook Endpoint</h3>
<ul>
<li><strong>HTTP Method</strong>: POST</li>
<li><strong>Path</strong>: <code>/stripe/webhook</code></li>
<li><strong>Purpose</strong>: Receives subscription events from Stripe</li>
<li><strong>Authentication</strong>: Stripe webhook signature validation</li>
<li><strong>Request Body</strong>: Stripe event object</li>
<li><strong>Response</strong>: 200 OK on success</li>
</ul>
<h3 id="salesforce-opportunity-api">Salesforce Opportunity API</h3>
<ul>
<li><strong>HTTP Method</strong>: POST</li>
<li><strong>Path</strong>: <code>/services/data/v55.0/sobjects/Opportunity</code></li>
<li><strong>Purpose</strong>: Creates a new opportunity in Salesforce</li>
<li><strong>Authentication</strong>: OAuth 2.0</li>
<li><strong>Request Body</strong>: Salesforce Opportunity object</li>
<li><strong>Response</strong>: 201 Created with record ID on success</li>
</ul>
<h3 id="error-codes">Error Codes</h3>
<ul>
<li><strong>400</strong>: Bad Request - Invalid input data</li>
<li><strong>401</strong>: Unauthorized - Authentication failure</li>
<li><strong>403</strong>: Forbidden - Insufficient permissions</li>
<li><strong>404</strong>: Not Found - Resource not found</li>
<li><strong>500</strong>: Internal Server Error - Processing error</li>
<li><strong>503</strong>: Service Unavailable - External service unavailable</li>
</ul>
<h3 id="rate-limiting">Rate Limiting</h3>
<ul>
<li>Dependent on Salesforce API limits (typically 100,000 requests per 24 hours)</li>
<li>Stripe webhook delivery retries on failure</li>
</ul>
<h3 id="pagination">Pagination</h3>
<p>Not applicable for this integration.</p>
<h3 id="versioning">Versioning</h3>
<ul>
<li>Uses Salesforce API version specified in the endpoint URL</li>
<li>Stripe API version specified in webhook configuration</li>
</ul>
    
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>