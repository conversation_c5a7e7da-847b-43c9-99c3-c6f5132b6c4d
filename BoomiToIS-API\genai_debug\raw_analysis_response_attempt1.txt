{"process_name": "SAP SuccessFactors to SFTP Integration", "description": "Integration that extracts employee data from SAP SuccessFactors, transforms it to Kafka Avro format, and uploads it to an SFTP server with comprehensive error handling", "endpoints": [{"method": "GET", "path": "/SuccessFactors/Employee", "purpose": "Retrieves employee data from SuccessFactors, transforms it, and uploads to SFTP", "components": [{"type": "request_reply", "name": "Get_SuccessFactors_Employee_Data", "id": "sf_employee_request", "config": {"endpoint_path": "/odata/v2/Employee", "address": "${SF_API_URL}/odata/v2/Employee"}}, {"type": "enricher", "name": "Validate_SF_Response", "id": "validate_response", "config": {"content": "// Validate the response from SuccessFactors\nif (message.getBody(String.class) == null || message.getBody(String.class).isEmpty()) {\n    throw new Exception(\"Empty response from SuccessFactors\");\n}"}}, {"type": "groovy_script", "name": "Transform_Canonical_To_Kafka_Avro", "id": "transform_to_avro", "config": {"script": "TransformCanonicalToKafkaAvro.groovy"}}, {"type": "enricher", "name": "Prepare_SFTP_Content", "id": "prepare_sftp", "config": {"content": "// Set file name and other SFTP properties\nString timestamp = new java.text.SimpleDateFormat(\"yyyyMMdd_HHmmss\").format(new java.util.Date());\nString fileName = \"EmployeeData_\" + timestamp + \".json\";\nmessage.setHeader(\"CamelFileName\", fileName);\nreturn message;"}}, {"type": "request_reply", "name": "Upload_To_SFTP", "id": "sftp_upload", "config": {"endpoint_path": "sftp://${SFTP_USER}@${SFTP_HOST}:${SFTP_PORT}/${SFTP_REMOTE_DIR}"}}], "error_handling": {"exception_subprocess": [{"type": "enricher", "name": "Log_Error_Details", "id": "log_error", "trigger": "any_error", "config": {"content": "// Log error details\nString errorMessage = \"Error in SuccessFactors to SFTP integration: \" + exception.getMessage();\nlogger.error(errorMessage);\nmessage.setBody(errorMessage);\nreturn message;"}}, {"type": "request_reply", "name": "Send_Error_Notification", "id": "send_notification", "trigger": "any_error", "config": {"endpoint_path": "smtp://${SMTP_HOST}:${SMTP_PORT}"}}]}, "branching": {"type": "exclusive", "branches": [{"condition": "message.getBody() != null", "components": ["sf_employee_request", "validate_response", "transform_to_avro", "prepare_sftp", "sftp_upload"], "sequence": ["sf_employee_request", "validate_response", "transform_to_avro", "prepare_sftp", "sftp_upload"]}]}, "sequence": ["sf_employee_request", "validate_response", "transform_to_avro", "prepare_sftp", "sftp_upload"], "transformations": [{"name": "TransformCanonicalToKafkaAvro.groovy", "type": "groovy", "script": "import groovy.json.*\n\ndef inputBody = message.getBody(String.class)\ndef jsonSlurper = new JsonSlurper()\ndef data = jsonSlurper.parseText(inputBody)\n\ndef result = [\n    \"Object\": [\n        \"batchProcessingDirectives\": [\n            \"Object\": [\n                \"accountID\": [\n                    \"Object\": [\n                        \"username\": data.username // Map from profile field 9\n                    ]\n                ],\n                \"batchProcessingOption\": [\n                    \"Array\": [\n                        \"ArrayElement1\": [\n                            \"Object\": [\n                                \"name\": data.batchProcessingOptionName // Map from profile field 118\n                            ]\n                        ]\n                    ]\n                ]\n            ]\n        ],\n        \"batchContactList\": [\n            \"Array\": [\n                \"ArrayElement1\": [\n                    \"Object\": [\n                        \"contact\": [\n                            \"Array\": [\n                                \"ArrayElement1\": [\n                                    \"Object\": [\n                                        \"contactID\": data.contactId, // Map from profile field 91\n                                        \"contactPointList\": [\n                                            \"Array\": [\n                                                \"ArrayElement1\": [\n                                                    \"Object\": [\n                                                        \"contactPoint\": [\n                                                            \"Array\": [\n                                                                \"ArrayElement1\": [\n                                                                    \"Object\": [\n                                                                        \"type\": data.contactPointType // Map from profile field 111\n                                                                    ]\n                                                                ]\n                                                            ]\n                                                        ]\n                                                    ]\n                                                ]\n                                            ]\n                                        ]\n                                    ]\n                                ]\n                            ]\n                        ]\n                    ]\n                ]\n            ]\n        ]\n    ]\n]\n\nmessage.setBody(JsonOutput.toJson(result))\nreturn message"}]}]}