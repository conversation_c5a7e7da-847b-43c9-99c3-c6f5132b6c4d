{"process_name": "SAP SuccessFactors to SFTP Integration with Error Handling", "description": "Integration that extracts data from SAP SuccessFactors, transforms it to Kafka Avro format, and transfers it to an SFTP server with comprehensive error handling", "endpoints": [{"method": "GET", "path": "/successfactors/employee-data", "purpose": "Extract employee data from SAP SuccessFactors, transform to Kafka Avro format, and transfer to SFTP", "components": [{"type": "request_reply", "name": "Get_SuccessFactors_Data", "id": "sf_request_1", "config": {"endpoint_path": "/odata/v2/User", "address": "${SF_API_URL}"}}, {"type": "groovy_script", "name": "Validate_SF_Data", "id": "validate_data_1", "config": {"script": "ValidateSFData.groovy"}}, {"type": "router", "name": "Data_Validation_Router", "id": "router_1", "config": {"condition": "${property.isValid == 'true'}"}}, {"type": "groovy_script", "name": "Transform_Canonical_To_Avro", "id": "transform_1", "config": {"script": "CanonicalToKafkaAvro.groovy"}}, {"type": "request_reply", "name": "SFTP_Upload", "id": "sftp_upload_1", "config": {"endpoint_path": "${SFTP_DIRECTORY}/employee_data_${date:now:yyyyMMdd_HHmmss}.json", "address": "sftp://${SFTP_HOST}:${SFTP_PORT}"}}, {"type": "enricher", "name": "Log_Success", "id": "log_success_1", "config": {"content": "Integration completed successfully at ${date:now:yyyy-MM-dd HH:mm:ss}"}}, {"type": "enricher", "name": "Log_Validation_Error", "id": "log_error_1", "config": {"content": "Data validation failed: ${property.errorMessage}"}}, {"type": "request_reply", "name": "Send_Error_Notification", "id": "error_notification_1", "config": {"endpoint_path": "/notification/email", "address": "smtp://mail-server"}}], "sequence": ["sf_request_1", "validate_data_1", "router_1", "transform_1", "sftp_upload_1", "log_success_1"], "transformations": [{"name": "ValidateSFData.groovy", "type": "groovy", "script": "import com.sap.gateway.ip.core.customdev.util.Message;\n\ndef Message processData(Message message) {\n    def body = message.getBody(java.lang.String);\n    def properties = message.getProperties();\n    \n    // Validate required fields exist\n    def isValid = true;\n    def errorMessage = \"\";\n    \n    def jsonData = new groovy.json.JsonSlurper().parseText(body);\n    \n    // Check for required fields\n    if (!jsonData.d || !jsonData.d.results) {\n        isValid = false;\n        errorMessage = \"Missing required data structure\";\n    } else {\n        def results = jsonData.d.results;\n        if (results.size() == 0) {\n            isValid = false;\n            errorMessage = \"No employee records found\";\n        }\n    }\n    \n    // Set properties for routing\n    properties.put(\"isValid\", String.valueOf(isValid));\n    if (!isValid) {\n        properties.put(\"errorMessage\", errorMessage);\n    }\n    \n    return message;\n}"}, {"name": "CanonicalToKafkaAvro.groovy", "type": "groovy", "script": "import com.sap.gateway.ip.core.customdev.util.Message;\nimport groovy.json.*;\n\ndef Message processData(Message message) {\n    def body = message.getBody(java.lang.String);\n    def jsonData = new JsonSlurper().parseText(body);\n    def result = [:];\n    \n    // Create the output structure\n    result.batchProcessingDirectives = [:];\n    result.batchProcessingDirectives.accountID = [:];\n    result.batchProcessingDirectives.accountID.username = jsonData.profileField9;\n    \n    result.batchProcessingDirectives.batchProcessingOption = [[]];\n    result.batchProcessingDirectives.batchProcessingOption[0].name = jsonData.profileField118;\n    \n    result.batchContactList = [[]];\n    result.batchContactList[0].contact = [[]];\n    result.batchContactList[0].contact[0].contactID = jsonData.profileField91;\n    \n    result.batchContactList[0].contact[0].contactPointList = [[]];\n    result.batchContactList[0].contact[0].contactPointList[0].contactPoint = [[]];\n    result.batchContactList[0].contact[0].contactPointList[0].contactPoint[0].type = jsonData.profileField111;\n    \n    // Convert back to JSON\n    message.setBody(new JsonBuilder(result).toPrettyString());\n    return message;\n}"}]}]}