{"process_name": "Stripe to Salesforce Opportunity Integration", "description": "Integration that creates Salesforce Opportunities automatically when Stripe Subscriptions are completed", "endpoints": [{"method": "POST", "path": "/webhook/stripe/subscription", "purpose": "Receives webhook notifications from Stripe when subscription events occur and creates Salesforce Opportunities", "components": [{"type": "enricher", "name": "Set_Dynamic_Properties", "id": "enricher_1", "config": {"content": "Sets properties based on incoming Stripe webhook event"}}, {"type": "json_to_xml_converter", "name": "Convert_JSON_to_XML", "id": "converter_1", "config": {}}, {"type": "groovy_script", "name": "Transform_Subscription_to_Opportunity", "id": "transform_1", "config": {"script": "TransformSubscriptionToOpportunity.groovy"}}, {"type": "request_reply", "name": "Create_Salesforce_Opportunity", "id": "request_reply_1", "config": {"endpoint_path": "/services/data/v53.0/sobjects/Opportunity"}}], "sequence": ["enricher_1", "converter_1", "transform_1", "request_reply_1"], "transformations": [{"name": "TransformSubscriptionToOpportunity.groovy", "type": "groovy", "script": "import com.sap.gateway.ip.core.customdev.util.Message;\nimport groovy.json.JsonSlurper;\nimport groovy.xml.MarkupBuilder;\n\ndef Message processData(Message message) {\n    def body = message.getBody(String);\n    def jsonSlurper = new JsonSlurper();\n    def stripeEvent = jsonSlurper.parseText(body);\n    \n    // Extract subscription data from Stripe event\n    def subscription = stripeEvent.data.object;\n    \n    // Create XML for Salesforce Opportunity\n    def writer = new StringWriter();\n    def xml = new MarkupBuilder(writer);\n    \n    xml.Opportunity {\n        Name(subscription.plan.name + ' - Customer ' + subscription.customer.substring(0, 3));\n        Description('Subscription ID: ' + subscription.id + ', Plan: ' + subscription.plan.name);\n        CloseDate(new Date(subscription.current_period_start * 1000).format('yyyy-MM-dd'));\n        StageName('Closed Won');\n        Type('New Business');\n        if (subscription.plan.amount) {\n            Amount(subscription.plan.amount / 100); // Convert cents to dollars\n        }\n    }\n    \n    message.setBody(writer.toString());\n    message.setHeader('Content-Type', 'application/xml');\n    return message;\n}"}]}]}