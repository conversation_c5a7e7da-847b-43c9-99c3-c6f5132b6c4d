{"process_name": "Test StartEvent Connection", "description": "Test integration to verify StartEvent connections", "endpoints": [{"method": "GET", "path": "/test", "purpose": "Test endpoint", "components": [{"type": "odata", "name": "Test_OData_Call", "id": "test_odata_1", "config": {"address": "https://test.com/odata", "resource_path": "TestData"}}, {"type": "enricher", "name": "Test_Enricher", "id": "test_enricher_1", "config": {"content": "Test content"}}], "sequence": ["test_odata_1", "test_enricher_1"]}]}