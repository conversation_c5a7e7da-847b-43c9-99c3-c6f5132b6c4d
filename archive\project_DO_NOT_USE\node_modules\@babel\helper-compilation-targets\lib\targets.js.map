{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "exports", "safari", "browserNameMap", "and_chr", "and_ff", "android", "chrome", "edge", "firefox", "ie", "ie_mob", "ios_saf", "node", "deno", "op_mob", "opera", "samsung"], "sources": ["../src/targets.ts"], "sourcesContent": ["export const unreleasedLabels = {\n  safari: \"tp\",\n} as const;\n\n// Map from browserslist|@mdn/browser-compat-data browser names to @kangax/compat-table browser names\nexport const browserNameMap = {\n  and_chr: \"chrome\",\n  and_ff: \"firefox\",\n  android: \"android\",\n  chrome: \"chrome\",\n  edge: \"edge\",\n  firefox: \"firefox\",\n  ie: \"ie\",\n  ie_mob: \"ie\",\n  ios_saf: \"ios\",\n  node: \"node\",\n  deno: \"deno\",\n  op_mob: \"opera_mobile\",\n  opera: \"opera\",\n  safari: \"safari\",\n  samsung: \"samsung\",\n} as const;\n\nexport type BrowserslistBrowserName = keyof typeof browserNameMap;\n"], "mappings": ";;;;;;AAAO,MAAMA,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,GAAG;EAC9BE,MAAM,EAAE;AACV,CAAU;AAGH,MAAMC,cAAc,GAAAF,OAAA,CAAAE,cAAA,GAAG;EAC5BC,OAAO,EAAE,QAAQ;EACjBC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,EAAE,EAAE,IAAI;EACRC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,KAAK;EACdC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,cAAc;EACtBC,KAAK,EAAE,OAAO;EACdd,MAAM,EAAE,QAAQ;EAChBe,OAAO,EAAE;AACX,CAAU", "ignoreList": []}