#!/usr/bin/env python3
"""
Test that the enhanced documentation uses Boomi-specific terminology instead of MuleSoft terms.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, 'app')

def test_boomi_terminology():
    """Test that documentation uses correct Boomi terminology."""
    
    try:
        from documentation_enhancer import DocumentationEnhancer
        
        # Create enhancer instance
        enhancer = DocumentationEnhancer()
        
        print("🧪 Testing Boomi Terminology in Documentation Prompt")
        print("=" * 60)
        
        # Create sample Boomi documentation
        sample_boomi_doc = """
        # Dell Boomi Integration Documentation
        
        ## Summary
        - **Total Files Processed:** 1
        - **Processes:** 1
        - **Maps:** 1
        - **Connectors:** 1
        
        ## Dell Boomi Processes
        
        ### 1. Create Salesforce Opportunities from Stripe Subscriptions
        
        #### Detailed Process Flow Analysis
        
        **Step 1: Start Event (shape1)**
        - **Type**: Process trigger/start event
        - **Purpose**: Receives Stripe webhook events
        
        **Step 2: Document Properties (shape6)**
        - **Type**: Document Properties (Dynamic Property Setting)
        - **Properties Configured**:
          - **DDP_CustomerName**: Makes HTTP API calls to retrieve customer data
          - **DDP_Subscription**: Makes HTTP API calls to retrieve product data
          - **DDP_SalesforceDescription**: String concatenation logic
          - **DDP_CloseDate**: Date calculation (3 months ago)
        
        **Step 3: Map Component (shape4)**
        - **Type**: Data Mapping/Transformation
        - **Purpose**: Transform data to Salesforce Opportunity format
        
        **Step 4: Salesforce Connector (shape3)**
        - **Type**: Connector Action
        - **Purpose**: Create Opportunity records in Salesforce
        
        ## Data Mappings and Transformations
        
        ### 1. Subscription Completed JSON to SF Opportunity CREATE Request XML
        
        **Business Purpose:** Salesforce Opportunity creation
        **Complexity:** Complex
        
        #### Field Mappings
        | From | To | Type | Business Logic |
        |------|----|----- |-------------- |
        | DDP_CustomerName | Opportunity/Name | profile | Direct mapping |
        | DDP_SalesforceDescription | Opportunity/Description | profile | Direct mapping |
        | DDP_CloseDate | Opportunity/CloseDate | profile | Direct mapping |
        
        #### Function Steps and Business Logic
        - **Get Document Property**: Retrieves dynamic property values
          - **Purpose**: Access calculated property values for mapping
        
        #### Default Values and Constants
        | Target Field | Default Value | Type |
        |------------- |-------------- |----- |
        | StageName | Pipeline | constant |
        """
        
        print("📝 Generating enhanced documentation...")
        
        # Generate enhanced documentation
        enhanced_doc = enhancer.enhance_documentation(sample_boomi_doc)
        
        if not enhanced_doc or enhanced_doc == sample_boomi_doc:
            print("❌ Documentation enhancement failed or returned unchanged content")
            return False
        
        print(f"✅ Enhanced documentation generated ({len(enhanced_doc)} characters)")
        
        # Check for MuleSoft-specific terminology that should NOT be present
        mulesoft_terms = [
            "DataWeave",
            "MuleSoft",
            "Mule",
            "Flow Reference",
            "Choice Router",
            "Transform Message",
            "Set Variable",
            "Set Payload"
        ]
        
        # Check for Boomi-specific terminology that SHOULD be present
        boomi_terms = [
            "Dell Boomi",
            "Document Properties",
            "Map component",
            "Function Steps",
            "Connector Action",
            "Profile",
            "Business Logic"
        ]
        
        print("\n🔍 Terminology Analysis:")
        
        # Check for unwanted MuleSoft terms
        mulesoft_found = []
        for term in mulesoft_terms:
            if term in enhanced_doc:
                mulesoft_found.append(term)
        
        # Check for required Boomi terms
        boomi_found = []
        for term in boomi_terms:
            if term in enhanced_doc:
                boomi_found.append(term)
        
        print(f"\n❌ MuleSoft terms found: {len(mulesoft_found)}/{len(mulesoft_terms)}")
        if mulesoft_found:
            for term in mulesoft_found:
                print(f"   - {term}")
        
        print(f"\n✅ Boomi terms found: {len(boomi_found)}/{len(boomi_terms)}")
        for term in boomi_found:
            print(f"   - {term}")
        
        # Check specific sections that should use Boomi terminology
        section_checks = [
            ("Boomi Transformations section", "Dell Boomi Transformations" in enhanced_doc),
            ("Map Transformations section", "Boomi Map Transformations" in enhanced_doc),
            ("No DataWeave references", "DataWeave" not in enhanced_doc),
            ("Document Properties mentioned", "Document Properties Logic" in enhanced_doc),
            ("Function Steps mentioned", "Function Steps" in enhanced_doc)
        ]
        
        print(f"\n📊 Section Terminology Checks:")
        all_passed = True
        
        for check_name, check_result in section_checks:
            status = "✅" if check_result else "❌"
            print(f"   {status} {check_name}")
            if not check_result:
                all_passed = False
        
        # Save enhanced documentation for review
        with open('test_enhanced_boomi_terminology.md', 'w', encoding='utf-8') as f:
            f.write(enhanced_doc)
        print(f"\n📄 Enhanced documentation saved to: test_enhanced_boomi_terminology.md")
        
        # Final assessment
        success = (len(mulesoft_found) == 0 and len(boomi_found) >= 5 and all_passed)
        
        print(f"\n📈 Terminology Assessment:")
        print(f"   MuleSoft terms eliminated: {'✅' if len(mulesoft_found) == 0 else '❌'}")
        print(f"   Boomi terms present: {'✅' if len(boomi_found) >= 5 else '❌'}")
        print(f"   Section terminology correct: {'✅' if all_passed else '❌'}")
        
        return success
        
    except Exception as e:
        print(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔧 Testing Boomi Terminology in Enhanced Documentation")
    print("=" * 70)
    
    success = test_boomi_terminology()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Boomi terminology test PASSED!")
        print("✅ The enhanced documentation now uses:")
        print("   - Dell Boomi specific terminology")
        print("   - Correct component names (Map, Document Properties, etc.)")
        print("   - No MuleSoft-specific references")
        print("   - Proper business logic descriptions")
        print("\n🚀 Documentation is now Boomi-focused and accurate!")
        return 0
    else:
        print("⚠️ Boomi terminology test FAILED!")
        print("❌ Some MuleSoft terminology is still present")
        print("❌ Or missing required Boomi terminology")
        return 1

if __name__ == "__main__":
    exit(main())
