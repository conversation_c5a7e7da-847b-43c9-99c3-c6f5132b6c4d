#!/usr/bin/env python3
"""
Test that Salesforce connectors in Boomi are correctly mapped to Request Reply + OData Receiver pattern.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, 'BoomiToIS-API')

def test_salesforce_odata_mapping():
    """Test that Salesforce connectors map to Request Reply + OData Receiver."""

    try:
        from enhanced_genai_iflow_generator import EnhancedGenAIIFlowGenerator

        # Create generator instance
        generator = EnhancedGenAIIFlowGenerator()

        print("🧪 Testing Salesforce to OData Receiver Mapping")
        print("=" * 60)

        # Test 1: Check if the prompt includes correct Salesforce to OData mapping instructions
        print("📋 Test 1: Checking prompt instructions...")

        sample_markdown = "Test markdown content"
        prompt = generator._create_detailed_analysis_prompt(sample_markdown)

        # Check for key Salesforce to OData mapping instructions
        salesforce_checks = [
            ("Salesforce to Request Reply mapping", "Boomi Salesforce Connector → Request Reply with OData Receiver Adapter" in prompt),
            ("OData receiver adapter instruction", "receiver_adapter" in prompt and "odata_adapter" in prompt),
            ("Salesforce specific conversion", "SALESFORCE-SPECIFIC CONVERSIONS" in prompt),
            ("OData POST operation", "Request Reply + OData POST operation" in prompt),
            ("Receiver adapter requirement", 'Always include "receiver_adapter" with "type": "odata_adapter"' in prompt)
        ]

        print(f"\n🔍 Prompt Instruction Analysis:")

        all_instructions_present = True
        for check_name, check_result in salesforce_checks:
            status = "✅" if check_result else "❌"
            print(f"   {status} {check_name}")
            if not check_result:
                all_instructions_present = False

        # Test 2: Check the specific Salesforce example in the prompt
        print(f"\n📋 Test 2: Checking Salesforce example structure...")

        # Look for the specific Salesforce example
        salesforce_example_checks = [
            ("Salesforce XML example", '<Component type="connector-action" subType="salesforce">' in prompt),
            ("Request Reply type", '"type": "request_reply"' in prompt),
            ("Receiver adapter config", '"receiver_adapter": {' in prompt),
            ("OData adapter type", '"type": "odata_adapter"' in prompt),
            ("Salesforce endpoint", '/services/data/v52.0/sobjects/Opportunity' in prompt)
        ]

        all_examples_present = True
        for check_name, check_result in salesforce_example_checks:
            status = "✅" if check_result else "❌"
            print(f"   {status} {check_name}")
            if not check_result:
                all_examples_present = False

        # Test 3: Verify the component mapping section
        print(f"\n📋 Test 3: Checking component mapping section...")

        mapping_checks = [
            ("Salesforce connector mapping", "Boomi Salesforce Connector Action → request_reply with odata_adapter receiver" in prompt),
            ("Receiver configuration", "request_reply with receiver adapter configuration" in prompt),
            ("Salesforce REST API", "Salesforce REST API" in prompt)
        ]

        all_mappings_present = True
        for check_name, check_result in mapping_checks:
            status = "✅" if check_result else "❌"
            print(f"   {status} {check_name}")
            if not check_result:
                all_mappings_present = False

        # Save prompt for review
        with open('test_salesforce_prompt_analysis.txt', 'w', encoding='utf-8') as f:
            f.write(prompt)
        print(f"\n� Full prompt saved to: test_salesforce_prompt_analysis.txt")

        # Overall assessment
        overall_success = all_instructions_present and all_examples_present and all_mappings_present

        print(f"\n📈 Prompt Analysis Results:")
        print(f"   Instructions present: {'✅' if all_instructions_present else '❌'}")
        print(f"   Examples present: {'✅' if all_examples_present else '❌'}")
        print(f"   Mappings present: {'✅' if all_mappings_present else '❌'}")

        return overall_success
        
    except Exception as e:
        print(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔧 Testing Salesforce to OData Receiver Mapping")
    print("=" * 70)
    
    success = test_salesforce_odata_mapping()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Salesforce OData mapping test PASSED!")
        print("✅ The iFlow generation now correctly:")
        print("   - Maps Salesforce connectors to Request Reply components")
        print("   - Includes OData receiver adapters for Salesforce operations")
        print("   - Configures proper POST operations for Salesforce REST API")
        print("   - Sets correct Salesforce API endpoints")
        print("\n🚀 Salesforce integrations will now have proper receiver adapters!")
        return 0
    else:
        print("⚠️ Salesforce OData mapping test FAILED!")
        print("❌ Request Reply components are missing OData receiver adapters")
        print("❌ Or Salesforce endpoints are not properly configured")
        return 1

if __name__ == "__main__":
    exit(main())
