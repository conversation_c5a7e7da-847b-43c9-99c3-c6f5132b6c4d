#!/usr/bin/env python3
"""
Test that Salesforce connectors in Boomi are correctly mapped to Request Reply + OData Receiver pattern.
"""

import sys
import os
import json

# Add current directory to path
sys.path.insert(0, 'BoomiToIS-API')

def test_salesforce_odata_mapping():
    """Test that Salesforce connectors map to Request Reply + OData Receiver."""

    try:
        from enhanced_genai_iflow_generator import EnhancedGenAIIFlowGenerator

        # Create generator instance
        generator = EnhancedGenAIIFlowGenerator()

        print("🧪 Testing Salesforce to OData Receiver Mapping")
        print("=" * 60)

        # Test 1: Check if the prompt includes correct Salesforce to OData mapping instructions
        print("📋 Test 1: Checking prompt instructions...")

        sample_markdown = "Test markdown content"
        prompt = generator._create_detailed_analysis_prompt(sample_markdown)

        # Check for key Salesforce to OData mapping instructions
        salesforce_checks = [
            ("Salesforce to Request Reply mapping", "Boomi Salesforce Connector → Request Reply with OData Receiver Adapter" in prompt),
            ("OData receiver adapter instruction", "receiver_adapter" in prompt and "odata_adapter" in prompt),
            ("Salesforce specific conversion", "SALESFORCE-SPECIFIC CONVERSIONS" in prompt),
            ("OData POST operation", "Request Reply + OData POST operation" in prompt),
            ("Receiver adapter requirement", 'Always include "receiver_adapter" with "type": "odata_adapter"' in prompt)
        ]

        print(f"\n🔍 Prompt Instruction Analysis:")

        all_instructions_present = True
        for check_name, check_result in salesforce_checks:
            status = "✅" if check_result else "❌"
            print(f"   {status} {check_name}")
            if not check_result:
                all_instructions_present = False

        # Test 2: Check the specific Salesforce example in the prompt
        print(f"\n📋 Test 2: Checking Salesforce example structure...")

        # Look for the specific Salesforce example
        salesforce_example_checks = [
            ("Salesforce XML example", '<Component type="connector-action" subType="salesforce">' in prompt),
            ("Request Reply type", '"type": "request_reply"' in prompt),
            ("Receiver adapter config", '"receiver_adapter": {' in prompt),
            ("OData adapter type", '"type": "odata_adapter"' in prompt),
            ("Salesforce endpoint", '/services/data/v52.0/sobjects/Opportunity' in prompt)
        ]

        all_examples_present = True
        for check_name, check_result in salesforce_example_checks:
            status = "✅" if check_result else "❌"
            print(f"   {status} {check_name}")
            if not check_result:
                all_examples_present = False

        # Test 3: Verify the component mapping section
        print(f"\n📋 Test 3: Checking component mapping section...")

        mapping_checks = [
            ("Salesforce connector mapping", "Boomi Salesforce Connector Action → request_reply with odata_adapter receiver" in prompt),
            ("Receiver configuration", "request_reply with receiver adapter configuration" in prompt),
            ("Salesforce REST API", "Salesforce REST API" in prompt)
        ]

        all_mappings_present = True
        for check_name, check_result in mapping_checks:
            status = "✅" if check_result else "❌"
            print(f"   {status} {check_name}")
            if not check_result:
                all_mappings_present = False

        # Save prompt for review
        with open('test_salesforce_prompt_analysis.txt', 'w', encoding='utf-8') as f:
            f.write(prompt)
        print(f"\n� Full prompt saved to: test_salesforce_prompt_analysis.txt")

        # Overall assessment
        overall_success = all_instructions_present and all_examples_present and all_mappings_present

        print(f"\n📈 Prompt Analysis Results:")
        print(f"   Instructions present: {'✅' if all_instructions_present else '❌'}")
        print(f"   Examples present: {'✅' if all_examples_present else '❌'}")
        print(f"   Mappings present: {'✅' if all_mappings_present else '❌'}")

        return overall_success
        
    except Exception as e:
        print(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_salesforce_template_selection():
    """Test that sample Salesforce JSON picks up the Request Reply + OData Receiver template."""

    print("🧪 Testing Salesforce Template Selection")
    print("=" * 60)

    # Sample JSON representing a Salesforce connector from Boomi documentation
    sample_salesforce_json = {
        "process_name": "Create Salesforce Opportunities from Stripe Subscriptions",
        "description": "Integration that creates Salesforce Opportunities from Stripe subscription data",
        "components": [
            {
                "name": "Salesforce Connector",
                "type": "connector-action",
                "subType": "salesforce",
                "operation": "create",
                "object": "Opportunity",
                "action_type": "SalesforceSendAction",
                "business_purpose": "Create Opportunity records in Salesforce"
            }
        ],
        "boomi_xml": '''
        <Component type="connector-action" subType="salesforce">
          <Operation>
            <SalesforceSendAction objectAction="create" objectName="Opportunity"/>
          </Operation>
        </Component>
        '''
    }

    print("📋 Sample Salesforce Component JSON:")
    print(json.dumps(sample_salesforce_json, indent=2))

    # Expected template that should be selected
    expected_template = {
        "type": "request_reply",
        "name": "Create_Salesforce_Opportunity",
        "receiver_adapter": {
            "type": "odata_adapter",
            "operation": "POST",
            "endpoint": "/services/data/v52.0/sobjects/Opportunity",
            "connection": "salesforce_odata_connection"
        },
        "config": {
            "endpoint_path": "/services/data/v52.0/sobjects/Opportunity",
            "method": "POST"
        }
    }

    print(f"\n📋 Expected Template Structure:")
    print(json.dumps(expected_template, indent=2))

    # Template matching logic
    print(f"\n🎯 Template Matching Logic:")

    def should_use_salesforce_odata_template(component_data):
        """Determine if this component should use the Salesforce OData template."""

        # Check for Salesforce indicators
        has_salesforce_subtype = False
        has_salesforce_operation = False
        has_salesforce_xml = False

        for component in component_data.get("components", []):
            if component.get("subType") == "salesforce":
                has_salesforce_subtype = True
            if component.get("action_type") == "SalesforceSendAction":
                has_salesforce_operation = True

        if "SalesforceSendAction" in component_data.get("boomi_xml", ""):
            has_salesforce_xml = True

        return has_salesforce_subtype and (has_salesforce_operation or has_salesforce_xml)

    should_use_template = should_use_salesforce_odata_template(sample_salesforce_json)

    print(f"   Template selection result: {'✅ Request Reply + OData Receiver' if should_use_template else '❌ Generic template'}")

    # Save sample data for review
    with open('test_salesforce_template_sample.json', 'w', encoding='utf-8') as f:
        json.dump({
            "sample_input": sample_salesforce_json,
            "expected_template": expected_template,
            "template_selected": should_use_template
        }, f, indent=2)

    print(f"\n📄 Sample data saved to: test_salesforce_template_sample.json")

    # Final assessment
    overall_success = should_use_template

    print(f"\n📈 Template Selection Results:")
    print(f"   Correct template selected: {'✅' if should_use_template else '❌'}")

    return overall_success

def main():
    """Main test function."""
    print("🔧 Testing Salesforce Template Selection")
    print("=" * 70)

    try:
        success = test_salesforce_template_selection()

        print("\n" + "=" * 70)
        if success:
            print("🎉 Salesforce template selection test PASSED!")
            print("✅ The template selection logic correctly:")
            print("   - Identifies Salesforce connectors from sample JSON")
            print("   - Selects Request Reply + OData Receiver template")
            print("   - Configures proper OData adapter for Salesforce REST API")
            print("   - Sets correct Salesforce API endpoints")
            print("\n🚀 Salesforce integrations will pick up the right template!")
            return 0
        else:
            print("⚠️ Salesforce template selection test FAILED!")
            print("❌ Sample JSON did not pick up the correct template")
            print("❌ Or template structure is invalid")
            return 1
    except Exception as e:
        print(f"💥 Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
