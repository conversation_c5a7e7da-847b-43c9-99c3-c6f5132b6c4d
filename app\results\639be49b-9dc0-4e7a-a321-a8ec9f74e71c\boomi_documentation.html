<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation with Mermaid</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #1565c0;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            background: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
        }
        pre.mermaid {
            text-align: center;
            background: white;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .note {
            background: #e3f2fd;
            padding: 10px;
            border-left: 4px solid #1565c0;
            margin: 10px 0;
        }
        .insights {
            background: #f3e5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .best-practices {
            background: #e8f5e9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .security {
            background: #ffebee;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1 id="stripe-to-salesforce-opportunity-integration">Stripe to Salesforce Opportunity Integration</h1>
<h2 id="table-of-contents">Table of Contents</h2>
<ul>
<li><a href="#api-overview">API Overview</a></li>
<li><a href="#endpoints">Endpoints</a></li>
<li><a href="#stripe-subscription-webhook">Stripe Subscription Webhook</a></li>
<li><a href="#salesforce-opportunity-creation">Salesforce Opportunity Creation</a></li>
<li><a href="#current-dell-boomi-flow-logic">Current Dell Boomi Flow Logic</a></li>
<li><a href="#process-trigger">Process Trigger</a></li>
<li><a href="#processing-steps">Processing Steps</a></li>
<li><a href="#data-transformations">Data Transformations</a></li>
<li><a href="#expected-outcomes">Expected Outcomes</a></li>
<li><a href="#dataweave-transformations-explained">DataWeave Transformations Explained</a></li>
<li><a href="#subscription-json-to-opportunity-xml-transformation">Subscription JSON to Opportunity XML Transformation</a></li>
<li><a href="#sap-integration-suite-implementation">SAP Integration Suite Implementation</a></li>
<li><a href="#component-mapping">Component Mapping</a></li>
<li><a href="#integration-flow-visualization">Integration Flow Visualization</a></li>
<li><a href="#configuration-details">Configuration Details</a></li>
<li><a href="#environment-configuration">Environment Configuration</a></li>
<li><a href="#configuration-parameters">Configuration Parameters</a></li>
<li><a href="#environment-variables">Environment Variables</a></li>
<li><a href="#security-settings">Security Settings</a></li>
<li><a href="#api-reference">API Reference</a></li>
<li><a href="#stripe-webhook-api">Stripe Webhook API</a></li>
<li><a href="#salesforce-api">Salesforce API</a></li>
</ul>
<h2 id="api-overview">API Overview</h2>
<p>This integration creates Salesforce Opportunities automatically when Stripe Subscriptions are completed. The integration listens for Stripe webhook events, specifically for subscription completion events, transforms the subscription data into Salesforce Opportunity format, and creates new Opportunity records in Salesforce.</p>
<ul>
<li><strong>Base URL/endpoint pattern</strong>: Webhook endpoint that receives Stripe subscription events</li>
<li><strong>Authentication mechanisms</strong>: </li>
<li>Stripe webhook authentication using webhook signatures</li>
<li>Salesforce authentication using OAuth credentials</li>
<li><strong>Rate limiting information</strong>: Subject to Salesforce API limits (typically 100,000 requests per 24-hour period)</li>
<li><strong>General response format</strong>: Success/failure confirmation for created Opportunities</li>
</ul>
<h2 id="endpoints">Endpoints</h2>
<h3 id="stripe-subscription-webhook">Stripe Subscription Webhook</h3>
<ul>
<li><strong>HTTP Method and full path</strong>: POST /webhook/stripe/subscription</li>
<li><strong>Purpose</strong>: Receives webhook notifications from Stripe when subscription events occur</li>
<li><strong>Request parameters</strong>:</li>
<li><strong>Headers</strong>:<ul>
<li><code>Stripe-Signature</code>: Signature to verify webhook authenticity</li>
<li><code>Content-Type</code>: application/json</li>
</ul>
</li>
<li><strong>Request body</strong>: Stripe subscription event in JSON format</li>
<li><strong>Response</strong>: 200 OK on successful processing</li>
</ul>
<h3 id="salesforce-opportunity-creation">Salesforce Opportunity Creation</h3>
<ul>
<li><strong>HTTP Method and full path</strong>: POST /services/data/v[VERSION]/sobjects/Opportunity</li>
<li><strong>Purpose</strong>: Creates a new Opportunity record in Salesforce</li>
<li><strong>Request parameters</strong>:</li>
<li><strong>Headers</strong>:<ul>
<li><code>Authorization</code>: Bearer token for Salesforce authentication</li>
<li><code>Content-Type</code>: application/xml</li>
</ul>
</li>
<li><strong>Request body</strong>: Opportunity data in XML format</li>
<li><strong>Response</strong>: <ul>
<li>201 Created with Opportunity ID on success</li>
<li>4xx/5xx errors with error details on failure</li>
</ul>
</li>
</ul>
<h2 id="current-dell-boomi-flow-logic">Current Dell Boomi Flow Logic</h2>
<h3 id="process-trigger">Process Trigger</h3>
<p>The integration flow is triggered by an incoming Stripe webhook event, specifically for subscription completion events. The webhook payload contains detailed information about the subscription that has been completed.</p>
<h3 id="processing-steps">Processing Steps</h3>
<ol>
<li><strong>Event Reception</strong>: The process starts with receiving the Stripe webhook event (shape1)</li>
<li><strong>Dynamic Property Setting</strong>: Sets dynamic properties based on the incoming event (shape6)</li>
<li><strong>Data Transformation</strong>: Transforms the Stripe subscription JSON data to Salesforce Opportunity XML format (shape4)</li>
<li><strong>Salesforce Operation</strong>: Creates a new Opportunity in Salesforce using the transformed data (shape3)</li>
<li><strong>Process Completion</strong>: Ends the process flow after successful Opportunity creation (shape5)</li>
</ol>
<h3 id="data-transformations">Data Transformations</h3>
<p>The key transformation in this flow converts Stripe subscription data in JSON format to Salesforce Opportunity data in XML format. The mapping includes:
- Subscription details mapped to Opportunity Description
- Subscription name mapped to Opportunity Name
- Subscription completion date mapped to Opportunity CloseDate</p>
<h3 id="expected-outcomes">Expected Outcomes</h3>
<ul>
<li><strong>Success Scenario</strong>: A new Opportunity is created in Salesforce with data from the Stripe subscription</li>
<li><strong>Error Scenarios</strong>:</li>
<li>Invalid webhook signature from Stripe</li>
<li>Transformation errors due to missing required fields</li>
<li>Salesforce API errors (authentication, validation, etc.)</li>
</ul>
<h2 id="dataweave-transformations-explained">DataWeave Transformations Explained</h2>
<h3 id="subscription-json-to-opportunity-xml-transformation">Subscription JSON to Opportunity XML Transformation</h3>
<p>This transformation converts the Stripe subscription data in JSON format to the XML format required by the Salesforce Opportunity CREATE operation.</p>
<p><strong>Input Format</strong>: JSON data from Stripe webhook containing subscription details
<strong>Output Format</strong>: XML structure conforming to Salesforce Opportunity object requirements</p>
<p>The mapping focuses on three key fields:
1. <strong>Description</strong>: Maps subscription details to the Opportunity Description field
2. <strong>Name</strong>: Maps subscription name to the Opportunity Name field
3. <strong>CloseDate</strong>: Maps subscription completion date to the Opportunity CloseDate field</p>
<p>While the exact DataWeave code is not provided in the source documentation, the mapping relationships are defined as follows:</p>
<table>
<thead>
<tr>
<th>From Field (Stripe)</th>
<th>To Field (Salesforce)</th>
<th>Type</th>
</tr>
</thead>
<tbody>
<tr>
<td>3</td>
<td>Opportunity/Description</td>
<td>profile</td>
</tr>
<tr>
<td>3</td>
<td>Opportunity/Name</td>
<td>profile</td>
</tr>
<tr>
<td>3</td>
<td>Opportunity/CloseDate</td>
<td>profile</td>
</tr>
</tbody>
</table>
<p>The "3" reference likely indicates a specific node or element in the source JSON structure, and "profile" indicates a mapping profile is being used to handle any necessary data type conversions or formatting.</p>
<h2 id="sap-integration-suite-implementation">SAP Integration Suite Implementation</h2>
<h3 id="component-mapping">Component Mapping</h3>
<table>
<thead>
<tr>
<th>Dell Boomi Component</th>
<th>SAP Integration Suite Equivalent</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td>shape1 (Start)</td>
<td>Start Message Event</td>
<td>Webhook receiver for Stripe events</td>
</tr>
<tr>
<td>shape6 (Set Dynamic Properties)</td>
<td>Content Modifier</td>
<td>Sets properties/headers based on incoming message</td>
</tr>
<tr>
<td>shape4 (Transform)</td>
<td>Message Mapping</td>
<td>Maps JSON to XML using SAP Cloud Integration mapping</td>
</tr>
<tr>
<td>shape3 (Salesforce Operation)</td>
<td>Salesforce Adapter</td>
<td>Uses SAP CPI Salesforce adapter to create Opportunity</td>
</tr>
<tr>
<td>shape5 (End)</td>
<td>End Message Event</td>
<td>Completes the integration flow</td>
</tr>
</tbody>
</table>
<h3 id="integration-flow-visualization">Integration Flow Visualization</h3>
<pre class="mermaid">
flowchart TD
    %% Define node styles
    classDef httpAdapter fill:#87CEEB,stroke:#333,stroke-width:2px
    classDef contentModifier fill:#98FB98,stroke:#333,stroke-width:2px
    classDef router fill:#FFB6C1,stroke:#333,stroke-width:2px
    classDef mapping fill:#DDA0DD,stroke:#333,stroke-width:2px
    classDef exception fill:#FFA07A,stroke:#333,stroke-width:2px
    classDef processCall fill:#F0E68C,stroke:#333,stroke-width:2px

    %% Main Flow
    Start((Start)) --> StripeWebhookReceiver[Stripe Webhook Receiver]:::httpAdapter
    StripeWebhookReceiver --> SetDynamicProperties[Set Dynamic Properties]:::contentModifier
    SetDynamicProperties --> TransformSubscriptionToOpportunity[Transform Subscription to Opportunity]:::mapping
    TransformSubscriptionToOpportunity --> CreateSalesforceOpportunity[Create Salesforce Opportunity]:::httpAdapter
    CreateSalesforceOpportunity --> End((End))

    %% Error Handling
    StripeWebhookReceiver -->|Error| ErrorHandler[(Global Error Handler)]:::exception
    SetDynamicProperties -->|Error| ErrorHandler
    TransformSubscriptionToOpportunity -->|Error| ErrorHandler
    CreateSalesforceOpportunity -->|Error| ErrorHandler
    ErrorHandler --> LogError[Log Error Details]:::contentModifier
    LogError --> ErrorResponse[Set Error Response]:::contentModifier
    ErrorResponse --> ErrorEnd((Error End))
</pre>
<h3 id="configuration-details">Configuration Details</h3>
<h4 id="stripe-webhook-receiver">Stripe Webhook Receiver</h4>
<ul>
<li><strong>Component Type</strong>: HTTP Adapter (Receiver)</li>
<li><strong>Parameters</strong>:</li>
<li>Path: /webhook/stripe/subscription</li>
<li>Method: POST</li>
<li>Authentication: None (relies on webhook signature validation)</li>
<li>CSRF Protection: Disabled</li>
</ul>
<h4 id="set-dynamic-properties">Set Dynamic Properties</h4>
<ul>
<li><strong>Component Type</strong>: Content Modifier</li>
<li><strong>Parameters</strong>:</li>
<li>Action: Set Properties</li>
<li>Property Names: Based on Stripe subscription data</li>
<li>Property Values: Extracted from webhook payload</li>
</ul>
<h4 id="transform-subscription-to-opportunity">Transform Subscription to Opportunity</h4>
<ul>
<li><strong>Component Type</strong>: Message Mapping</li>
<li><strong>Parameters</strong>:</li>
<li>Source Format: JSON</li>
<li>Target Format: XML</li>
<li>Mapping Details:<ul>
<li>Source Path 3 → Target Path Opportunity/Description</li>
<li>Source Path 3 → Target Path Opportunity/Name</li>
<li>Source Path 3 → Target Path Opportunity/CloseDate</li>
</ul>
</li>
</ul>
<h4 id="create-salesforce-opportunity">Create Salesforce Opportunity</h4>
<ul>
<li><strong>Component Type</strong>: Salesforce Adapter</li>
<li><strong>Parameters</strong>:</li>
<li>Operation: Create</li>
<li>Object: Opportunity</li>
<li>Authentication: OAuth (configured in Security Material)</li>
<li>Request Format: XML</li>
</ul>
<h4 id="error-handler">Error Handler</h4>
<ul>
<li><strong>Component Type</strong>: Exception Subprocess</li>
<li><strong>Parameters</strong>:</li>
<li>Error Types: All</li>
<li>Error Logging: Enabled</li>
<li>Response Code: Based on error type</li>
</ul>
<h2 id="environment-configuration">Environment Configuration</h2>
<h3 id="configuration-parameters">Configuration Parameters</h3>
<ul>
<li><strong>Stripe Webhook Secret</strong>: Used to validate incoming webhook signatures</li>
<li><strong>Salesforce API Version</strong>: Version of the Salesforce API to use (e.g., v53.0)</li>
<li><strong>Salesforce Instance URL</strong>: URL of the Salesforce instance (e.g., https://yourinstance.salesforce.com)</li>
</ul>
<h3 id="environment-variables">Environment Variables</h3>
<ul>
<li><strong>STRIPE_WEBHOOK_SECRET</strong>: Secret key for validating Stripe webhook signatures</li>
<li><strong>SF_CLIENT_ID</strong>: Salesforce OAuth client ID</li>
<li><strong>SF_CLIENT_SECRET</strong>: Salesforce OAuth client secret</li>
<li><strong>SF_USERNAME</strong>: Salesforce username for authentication</li>
<li><strong>SF_PASSWORD</strong>: Salesforce password for authentication</li>
<li><strong>SF_SECURITY_TOKEN</strong>: Salesforce security token (if required)</li>
</ul>
<h3 id="security-settings">Security Settings</h3>
<ul>
<li><strong>Stripe Webhook Authentication</strong>: Validate incoming webhook requests using the Stripe-Signature header</li>
<li><strong>Salesforce Authentication</strong>: OAuth 2.0 authentication for Salesforce API access</li>
<li><strong>Credential Storage</strong>: Store credentials securely in SAP Cloud Integration's Security Material store</li>
<li><strong>Transport Layer Security</strong>: HTTPS for all external communications</li>
</ul>
<h2 id="api-reference">API Reference</h2>
<h3 id="stripe-webhook-api">Stripe Webhook API</h3>
<h4 id="subscription-events">Subscription Events</h4>
<ul>
<li><strong>Endpoint</strong>: POST /webhook/stripe/subscription</li>
<li><strong>Authentication</strong>: Stripe-Signature header validation</li>
<li><strong>Request Body</strong>: JSON payload with subscription event details</li>
<li><strong>Event Types</strong>:</li>
<li>customer.subscription.created</li>
<li>customer.subscription.updated</li>
<li>customer.subscription.deleted</li>
<li>customer.subscription.trial_will_end</li>
</ul>
<h4 id="example-webhook-payload">Example Webhook Payload</h4>
<div class="codehilite"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;evt_1234567890&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;object&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;event&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;api_version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2020-08-27&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1610000000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;object&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sub_1234567890&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;object&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;subscription&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;customer&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cus_1234567890&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;plan&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;plan_1234567890&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Premium Plan&quot;</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;current_period_start&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1610000000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;current_period_end&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1612678400</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;customer.subscription.created&quot;</span>
<span class="p">}</span>
</code></pre></div>

<h3 id="salesforce-api">Salesforce API</h3>
<h4 id="create-opportunity">Create Opportunity</h4>
<ul>
<li><strong>Endpoint</strong>: POST /services/data/v{version}/sobjects/Opportunity</li>
<li><strong>Authentication</strong>: OAuth 2.0 Bearer Token</li>
<li><strong>Content-Type</strong>: application/xml</li>
<li><strong>Request Body</strong>: XML representation of Opportunity object</li>
</ul>
<h4 id="example-request-body">Example Request Body</h4>
<div class="codehilite"><pre><span></span><code><span class="nt">&lt;Opportunity&gt;</span>
<span class="w">  </span><span class="nt">&lt;Name&gt;</span>Premium<span class="w"> </span>Plan<span class="w"> </span>-<span class="w"> </span>Customer<span class="w"> </span>ABC<span class="nt">&lt;/Name&gt;</span>
<span class="w">  </span><span class="nt">&lt;Description&gt;</span>Subscription<span class="w"> </span>ID:<span class="w"> </span>sub_1234567890,<span class="w"> </span>Plan:<span class="w"> </span>Premium<span class="w"> </span>Plan<span class="nt">&lt;/Description&gt;</span>
<span class="w">  </span><span class="nt">&lt;CloseDate&gt;</span>2023-01-15<span class="nt">&lt;/CloseDate&gt;</span>
<span class="w">  </span><span class="nt">&lt;StageName&gt;</span>Closed<span class="w"> </span>Won<span class="nt">&lt;/StageName&gt;</span>
<span class="w">  </span><span class="nt">&lt;Amount&gt;</span>99.99<span class="nt">&lt;/Amount&gt;</span>
<span class="w">  </span><span class="nt">&lt;Type&gt;</span>New<span class="w"> </span>Business<span class="nt">&lt;/Type&gt;</span>
<span class="nt">&lt;/Opportunity&gt;</span>
</code></pre></div>

<h4 id="response-codes">Response Codes</h4>
<ul>
<li><strong>201 Created</strong>: Opportunity successfully created</li>
<li><strong>400 Bad Request</strong>: Invalid request format or missing required fields</li>
<li><strong>401 Unauthorized</strong>: Authentication failure</li>
<li><strong>403 Forbidden</strong>: Insufficient permissions</li>
<li><strong>500 Internal Server Error</strong>: Server-side error processing the request</li>
</ul>
    
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>