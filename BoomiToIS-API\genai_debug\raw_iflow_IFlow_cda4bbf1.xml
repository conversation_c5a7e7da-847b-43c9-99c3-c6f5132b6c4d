<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
                   xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                   xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                   xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                   xmlns:ifl="http:///com.sap.ifl.model/Ifl.xsd"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1">
    <bpmn2:collaboration id="Collaboration_1" name="Default Collaboration">
        <bpmn2:extensionElements>
            <ifl:property>
                <key>namespaceMapping</key>
                <value></value>
            </ifl:property>
            <ifl:property>
                <key>httpSessionHandling</key>
                <value>None</value>
            </ifl:property>
            <ifl:property>
                <key>returnExceptionToSender</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>log</key>
                <value>All events</value>
            </ifl:property>
            <ifl:property>
                <key>corsEnabled</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>componentVersion</key>
                <value>1.2</value>
            </ifl:property>
            <ifl:property>
                <key>ServerTrace</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>xsrfProtection</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>cmdVariantUri</key>
                <value>ctype::IFlowVariant/cname::IFlowConfiguration/version::1.2.4</value>
            </ifl:property>
        </bpmn2:extensionElements>
<bpmn2:participant id="Participant_1" ifl:type="EndpointSender" name="Sender">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>enableBasicAuthentication</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>ifl:type</key>
                    <value>EndpointSender</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:participant>
<bpmn2:participant id="Participant_Process_1" ifl:type="IntegrationProcess" name="Integration Process" processRef="Process_1">
            <bpmn2:extensionElements/>
        </bpmn2:participant>
<bpmn2:participant id="Participant_sf_odata_request" ifl:type="EndpointRecevier" name="SAP SuccessFactors OData Request_SuccessFactors">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ifl:type</key>
            <value>EndpointRecevier</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:participant>
<bpmn2:participant id="Participant_sftp_transfer" ifl:type="EndpointRecevier" name="SFTP File Transfer_SFTP">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ifl:type</key>
            <value>EndpointRecevier</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:participant>
<bpmn2:participant id="Participant_error_notification" ifl:type="EndpointReceiver" name="Error Notification_Receiver">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ifl:type</key>
            <value>EndpointReceiver</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:participant>
<bpmn2:messageFlow id="MessageFlow_10" name="HTTPS" sourceRef="Participant_1" targetRef="StartEvent_2">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>ComponentType</key>
                    <value>HTTPS</value>
                </ifl:property>
                <ifl:property>
                    <key>Description</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>maximumBodySize</key>
                    <value>40</value>
                </ifl:property>
                <ifl:property>
                    <key>ComponentNS</key>
                    <value>sap</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.4</value>
                </ifl:property>
                <ifl:property>
                    <key>urlPath</key>
                    <value>/test</value>
                </ifl:property>
                <ifl:property>
                    <key>TransportProtocolVersion</key>
                    <value>1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>xsrfProtection</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>TransportProtocol</key>
                    <value>HTTPS</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::AdapterVariant/cname::sap:HTTPS/tp::HTTPS/mp::None/direction::Sender/version::1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>userRole</key>
                    <value>ESBMessaging.send</value>
                </ifl:property>
                <ifl:property>
                    <key>senderAuthType</key>
                    <value>RoleBased</value>
                </ifl:property>
                <ifl:property>
                    <key>MessageProtocol</key>
                    <value>None</value>
                </ifl:property>
                <ifl:property>
                    <key>MessageProtocolVersion</key>
                    <value>1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>direction</key>
                    <value>Sender</value>
                </ifl:property>
                <ifl:property>
                    <key>clientCertificates</key>
                    <value></value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:messageFlow>
<bpmn2:messageFlow id="MessageFlow_sf_odata_request" name="SuccessFactors" sourceRef="sf_odata_request" targetRef="Participant_sf_odata_request">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ComponentType</key>
            <value>SuccessFactors</value>
        </ifl:property>
        <ifl:property>
            <key>Description</key>
            <value>SuccessFactors OData Connection</value>
        </ifl:property>
        <ifl:property>
            <key>ComponentNS</key>
            <value>sap</value>
        </ifl:property>
        <ifl:property>
            <key>address</key>
            <value>https://example.com/api</value>
        </ifl:property>
        <ifl:property>
            <key>operation</key>
            <value>Query(GET)(GET)</value>
        </ifl:property>
        <ifl:property>
            <key>authenticationMethod</key>
            <value>OAuth</value>
        </ifl:property>
        <ifl:property>
            <key>TransportProtocolVersion</key>
            <value>1.25.0</value>
        </ifl:property>
        <ifl:property>
            <key>MessageProtocol</key>
            <value>OData V2</value>
        </ifl:property>
        <ifl:property>
            <key>direction</key>
            <value>Receiver</value>
        </ifl:property>
        <ifl:property>
            <key>TransportProtocol</key>
            <value>HTTPS</value>
        </ifl:property>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.25</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::AdapterVariant/cname::sap:SuccessFactors/tp::HTTPS/mp::OData V2/direction::Receiver/version::1.25.0</value>
        </ifl:property>
        <ifl:property>
            <key>contentType</key>
            <value>application/json</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:messageFlow>
<bpmn2:messageFlow id="MessageFlow_sftp_transfer" name="SFTP" sourceRef="sftp_transfer" targetRef="Participant_sftp_transfer">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ComponentType</key>
            <value>SFTP</value>
        </ifl:property>
        <ifl:property>
            <key>Description</key>
            <value>SFTP Connection for file upload</value>
        </ifl:property>
        <ifl:property>
            <key>ComponentNS</key>
            <value>sap</value>
        </ifl:property>
        <ifl:property>
            <key>host</key>
            <value>${SFTP_HOST}</value>
        </ifl:property>
        <ifl:property>
            <key>port</key>
            <value>22</value>
        </ifl:property>
        <ifl:property>
            <key>path</key>
            <value></value>
        </ifl:property>
        <ifl:property>
            <key>authentication</key>
            <value>username/password</value>
        </ifl:property>
        <ifl:property>
            <key>username</key>
            <value></value>
        </ifl:property>
        <ifl:property>
            <key>operation</key>
            <value>PUT</value>
        </ifl:property>
        <ifl:property>
            <key>TransportProtocolVersion</key>
            <value>1.11.2</value>
        </ifl:property>
        <ifl:property>
            <key>MessageProtocol</key>
            <value>File</value>
        </ifl:property>
        <ifl:property>
            <key>direction</key>
            <value>Receiver</value>
        </ifl:property>
        <ifl:property>
            <key>TransportProtocol</key>
            <value>SFTP</value>
        </ifl:property>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.11</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::AdapterVariant/cname::sap:SFTP/tp::SFTP/mp::File/direction::Receiver/version::1.11.2</value>
        </ifl:property>
        <ifl:property>
            <key>fileExist</key>
            <value>Override</value>
        </ifl:property>
        <ifl:property>
            <key>autoCreate</key>
            <value>1</value>
        </ifl:property>
        <ifl:property>
            <key>connectTimeout</key>
            <value>10000</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:messageFlow>
<bpmn2:messageFlow id="MessageFlow_error_notification" name="HTTP" sourceRef="error_notification" targetRef="Participant_error_notification">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ComponentType</key>
            <value>HTTP</value>
        </ifl:property>
        <ifl:property>
            <key>address</key>
            <value>https://example.com/api</value>
        </ifl:property>
        <ifl:property>
            <key>method</key>
            <value>GET</value>
        </ifl:property>
        <ifl:property>
            <key>TransportProtocol</key>
            <value>HTTPS</value>
        </ifl:property>
        <ifl:property>
            <key>direction</key>
            <value>Receiver</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:messageFlow>
    </bpmn2:collaboration>
    <bpmn2:process id="Process_1" name="Integration Process">
        <bpmn2:extensionElements>
            <ifl:property>
                <key>transactionTimeout</key>
                <value>30</value>
            </ifl:property>
            <ifl:property>
                <key>componentVersion</key>
                <value>1.2</value>
            </ifl:property>
            <ifl:property>
                <key>cmdVariantUri</key>
                <value>ctype::FlowElementVariant/cname::IntegrationProcess/version::1.2.1</value>
            </ifl:property>
            <ifl:property>
                <key>transactionalHandling</key>
                <value>Not Required</value>
            </ifl:property>
        </bpmn2:extensionElements>
        
            <bpmn2:startEvent id="StartEvent_2" name="Start">
                <bpmn2:extensionElements>
                    <ifl:property>
                        <key>componentVersion</key>
                        <value>1.0</value>
                    </ifl:property>
                    <ifl:property>
                        <key>cmdVariantUri</key>
                        <value>ctype::FlowstepVariant/cname::MessageStartEvent/version::1.0</value>
                    </ifl:property>
                </bpmn2:extensionElements>
                <bpmn2:outgoing>SequenceFlow_Start</bpmn2:outgoing>
                <bpmn2:messageEventDefinition id="MessageEventDefinition_StartEvent_2"/>
            </bpmn2:startEvent>
            <bpmn2:serviceTask id="sf_odata_request" name="SAP SuccessFactors OData Request">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.0</value>
        </ifl:property>
        <ifl:property>
            <key>activityType</key>
            <value>ExternalCall</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::ExternalCall/version::1.0.4</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:incoming>SequenceFlow_sf_odata_request_in</bpmn2:incoming>
    <bpmn2:outgoing>SequenceFlow_sf_odata_request_out</bpmn2:outgoing>
</bpmn2:serviceTask>
            <bpmn2:callActivity id="data_validation" name="Data Validation">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>bodyType</key>
                    <value>expression</value>
                </ifl:property>
                <ifl:property>
                    <key>propertyTable</key>
                    <value></value>
                </ifl:property>
                <ifl:property>
                    <key>headerTable</key>
                    <value></value>
                </ifl:property>
                <ifl:property>
                    <key>wrapContent</key>
                    <value></value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.4</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Enricher</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Enricher/version::1.4.2</value>
                </ifl:property>
                <ifl:property>
                    <key>bodyContent</key>
                    <value>${body}</value>
                </ifl:property>
            </bpmn2:extensionElements>
            <bpmn2:incoming>SequenceFlow_1</bpmn2:incoming>
            <bpmn2:outgoing>SequenceFlow_2</bpmn2:outgoing>
        </bpmn2:callActivity>
            <bpmn2:callActivity id="canonical_to_avro_mapping" name="Canonical To Kafka Avro Mapping">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>bodyType</key>
                    <value>expression</value>
                </ifl:property>
                <ifl:property>
                    <key>propertyTable</key>
                    <value></value>
                </ifl:property>
                <ifl:property>
                    <key>headerTable</key>
                    <value></value>
                </ifl:property>
                <ifl:property>
                    <key>wrapContent</key>
                    <value></value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.4</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Enricher</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Enricher/version::1.4.2</value>
                </ifl:property>
                <ifl:property>
                    <key>bodyContent</key>
                    <value>${body}</value>
                </ifl:property>
            </bpmn2:extensionElements>
            <bpmn2:incoming>SequenceFlow_1</bpmn2:incoming>
            <bpmn2:outgoing>SequenceFlow_2</bpmn2:outgoing>
        </bpmn2:callActivity>
            <bpmn2:callActivity id="error_handler" name="Error Handler">
                        <bpmn2:extensionElements>
                            <ifl:property>
                                <key>scriptFunction</key>
                                <value>processError</value>
                            </ifl:property>
                            <ifl:property>
                                <key>componentVersion</key>
                                <value>1.0</value>
                            </ifl:property>
                            <ifl:property>
                                <key>activityType</key>
                                <value>Script</value>
                            </ifl:property>
                            <ifl:property>
                                <key>cmdVariantUri</key>
                                <value>ctype::FlowstepVariant/cname::GroovyScript/version::1.0.1</value>
                            </ifl:property>
                            <ifl:property>
                                <key>subActivityType</key>
                                <value>GroovyScript</value>
                            </ifl:property>
                            <ifl:property>
                                <key>script</key>
                                <value>Error Handler.groovy</value>
                            </ifl:property>
                        </bpmn2:extensionElements>
                    </bpmn2:callActivity>
            <bpmn2:callActivity id="sftp_error_handler" name="SFTP Error Handler">
                        <bpmn2:extensionElements>
                            <ifl:property>
                                <key>scriptFunction</key>
                                <value>processError</value>
                            </ifl:property>
                            <ifl:property>
                                <key>componentVersion</key>
                                <value>1.0</value>
                            </ifl:property>
                            <ifl:property>
                                <key>activityType</key>
                                <value>Script</value>
                            </ifl:property>
                            <ifl:property>
                                <key>cmdVariantUri</key>
                                <value>ctype::FlowstepVariant/cname::GroovyScript/version::1.0.1</value>
                            </ifl:property>
                            <ifl:property>
                                <key>subActivityType</key>
                                <value>GroovyScript</value>
                            </ifl:property>
                            <ifl:property>
                                <key>script</key>
                                <value>SFTP Error Handler.groovy</value>
                            </ifl:property>
                        </bpmn2:extensionElements>
                    </bpmn2:callActivity>
            <bpmn2:callActivity id="success_logger" name="Success Logger">
                        <bpmn2:extensionElements>
                            <ifl:property>
                                <key>scriptFunction</key>
                                <value>processMessage</value>
                            </ifl:property>
                            <ifl:property>
                                <key>componentVersion</key>
                                <value>1.0</value>
                            </ifl:property>
                            <ifl:property>
                                <key>activityType</key>
                                <value>Script</value>
                            </ifl:property>
                            <ifl:property>
                                <key>cmdVariantUri</key>
                                <value>ctype::FlowstepVariant/cname::GroovyScript/version::1.0.1</value>
                            </ifl:property>
                            <ifl:property>
                                <key>subActivityType</key>
                                <value>GroovyScript</value>
                            </ifl:property>
                            <ifl:property>
                                <key>script</key>
                                <value>Success Logger.groovy</value>
                            </ifl:property>
                        </bpmn2:extensionElements>
                    </bpmn2:callActivity>
            <bpmn2:serviceTask id="sftp_transfer" name="SFTP File Transfer">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.0</value>
        </ifl:property>
        <ifl:property>
            <key>activityType</key>
            <value>ExternalCall</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::ExternalCall/version::1.0.4</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:incoming>SequenceFlow_sftp_transfer_in</bpmn2:incoming>
    <bpmn2:outgoing>SequenceFlow_sftp_transfer_out</bpmn2:outgoing>
</bpmn2:serviceTask>
            <bpmn2:serviceTask id="error_notification" name="Error Notification">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.0</value>
        </ifl:property>
        <ifl:property>
            <key>activityType</key>
            <value>ExternalCall</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::ExternalCall/version::1.0.4</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:incoming>SequenceFlow_error_notification_in</bpmn2:incoming>
    <bpmn2:outgoing>SequenceFlow_error_notification_out</bpmn2:outgoing>
</bpmn2:serviceTask>
            <bpmn2:endEvent id="EndEvent_2" name="End">
                <bpmn2:extensionElements>
                    <ifl:property>
                        <key>componentVersion</key>
                        <value>1.1</value>
                    </ifl:property>
                    <ifl:property>
                        <key>cmdVariantUri</key>
                        <value>ctype::FlowstepVariant/cname::MessageEndEvent/version::1.1.0</value>
                    </ifl:property>
                </bpmn2:extensionElements>
                <bpmn2:incoming>SequenceFlow_End</bpmn2:incoming>
                <bpmn2:messageEventDefinition id="MessageEventDefinition_EndEvent_2"/>
            </bpmn2:endEvent>
            <bpmn2:sequenceFlow id="SequenceFlow_Start" sourceRef="sf_odata_request" targetRef="data_validation" isImmediate="true"/>
            <bpmn2:sequenceFlow id="SequenceFlow_1" sourceRef="data_validation" targetRef="canonical_to_avro_mapping" isImmediate="true"/>
            <bpmn2:sequenceFlow id="SequenceFlow_2" sourceRef="canonical_to_avro_mapping" targetRef="error_handler" isImmediate="true"/>
            <bpmn2:sequenceFlow id="SequenceFlow_3" sourceRef="error_handler" targetRef="sftp_error_handler" isImmediate="true"/>
            <bpmn2:sequenceFlow id="SequenceFlow_4" sourceRef="sftp_error_handler" targetRef="success_logger" isImmediate="true"/>
            <bpmn2:sequenceFlow id="SequenceFlow_5" sourceRef="success_logger" targetRef="sftp_transfer" isImmediate="true"/>
            <bpmn2:sequenceFlow id="SequenceFlow_End" sourceRef="sftp_transfer" targetRef="error_notification" isImmediate="true"/>
            <bpmn2:sequenceFlow id="SequenceFlow_End" sourceRef="error_notification" targetRef="EndEvent_2" isImmediate="true"/>
            <bpmn2:sequenceFlow id="SequenceFlow_Start" sourceRef="StartEvent_2" targetRef="sf_odata_request" isImmediate="true"/>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1" name="Default Collaboration Diagram">
        
                <bpmndi:BPMNPlane bpmnElement="Collaboration_1" id="BPMNPlane_1"><bpmndi:BPMNShape bpmnElement="Participant_1" id="BPMNShape_Participant_1">
    <dc:Bounds height="140.0" width="100.0" x="100" y="100"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="Participant_Process_1" id="BPMNShape_Participant_Process_1">
    <dc:Bounds height="294" width="957" x="220" y="150"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="Participant_sf_odata_request" id="BPMNShape_Participant_sf_odata_request">
    <dc:Bounds height="140.0" width="100.0" x="340" y="100"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="Participant_sftp_transfer" id="BPMNShape_Participant_sftp_transfer">
    <dc:Bounds height="140.0" width="100.0" x="460" y="100"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="Participant_error_notification" id="BPMNShape_Participant_error_notification">
    <dc:Bounds height="140.0" width="100.0" x="580" y="400"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="StartEvent_2" id="BPMNShape_StartEvent_2">
    <dc:Bounds height="32.0" width="32.0" x="263" y="126"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="sf_odata_request" id="BPMNShape_sf_odata_request">
    <dc:Bounds height="60" width="100" x="300" y="140"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="data_validation" id="BPMNShape_data_validation">
    <dc:Bounds height="60" width="100" x="420" y="140"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="canonical_to_avro_mapping" id="BPMNShape_canonical_to_avro_mapping">
    <dc:Bounds height="60" width="100" x="540" y="140"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="error_handler" id="BPMNShape_error_handler">
    <dc:Bounds height="60" width="100" x="660" y="140"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="sftp_error_handler" id="BPMNShape_sftp_error_handler">
    <dc:Bounds height="60" width="100" x="780" y="140"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="success_logger" id="BPMNShape_success_logger">
    <dc:Bounds height="60" width="100" x="900" y="140"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="sftp_transfer" id="BPMNShape_sftp_transfer">
    <dc:Bounds height="60" width="100" x="1020" y="140"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="error_notification" id="BPMNShape_error_notification">
    <dc:Bounds height="60" width="100" x="1140" y="140"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="EndEvent_2" id="BPMNShape_EndEvent_2">
    <dc:Bounds height="32.0" width="32.0" x="950" y="112"/>
</bpmndi:BPMNShape>
                    <bpmndi:BPMNEdge bpmnElement="MessageFlow_10" id="BPMNEdge_MessageFlow_10" sourceElement="BPMNShape_Participant_1" targetElement="BPMNShape_StartEvent_2">
                        <di:waypoint x="150" xsi:type="dc:Point" y="170"/>
                        <di:waypoint x="250" xsi:type="dc:Point" y="170"/>
                    </bpmndi:BPMNEdge>
                    <bpmndi:BPMNEdge bpmnElement="MessageFlow_sf_odata_request" id="BPMNEdge_MessageFlow_sf_odata_request" sourceElement="BPMNShape_sf_odata_request" targetElement="BPMNShape_Participant_sf_odata_request">
                        <di:waypoint x="420" xsi:type="dc:Point" y="142"/>
                        <di:waypoint x="420" xsi:type="dc:Point" y="300"/>
                    </bpmndi:BPMNEdge>
                    <bpmndi:BPMNEdge bpmnElement="MessageFlow_sftp_transfer" id="BPMNEdge_MessageFlow_sftp_transfer" sourceElement="BPMNShape_sftp_transfer" targetElement="BPMNShape_Participant_sftp_transfer">
                        <di:waypoint x="1140" xsi:type="dc:Point" y="142"/>
                        <di:waypoint x="1140" xsi:type="dc:Point" y="300"/>
                    </bpmndi:BPMNEdge>
                    <bpmndi:BPMNEdge bpmnElement="MessageFlow_error_notification" id="BPMNEdge_MessageFlow_error_notification" sourceElement="BPMNShape_error_notification" targetElement="BPMNShape_Participant_error_notification">
                        <di:waypoint x="1260" xsi:type="dc:Point" y="142"/>
                        <di:waypoint x="1260" xsi:type="dc:Point" y="300"/>
                    </bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_Start" id="BPMNEdge_SequenceFlow_Start" sourceElement="BPMNShape_StartEvent_2" targetElement="BPMNShape_sf_odata_request">
    <di:waypoint x="295" xsi:type="dc:Point" y="142.0"/>
    <di:waypoint x="300" xsi:type="dc:Point" y="170.0"/>
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_1" id="BPMNEdge_SequenceFlow_1" sourceElement="BPMNShape_data_validation" targetElement="BPMNShape_canonical_to_avro_mapping">
    <di:waypoint x="520" xsi:type="dc:Point" y="170.0"/>
    <di:waypoint x="540" xsi:type="dc:Point" y="170.0"/>
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_2" id="BPMNEdge_SequenceFlow_2" sourceElement="BPMNShape_canonical_to_avro_mapping" targetElement="BPMNShape_error_handler">
    <di:waypoint x="640" xsi:type="dc:Point" y="170.0"/>
    <di:waypoint x="660" xsi:type="dc:Point" y="170.0"/>
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_3" id="BPMNEdge_SequenceFlow_3" sourceElement="BPMNShape_error_handler" targetElement="BPMNShape_sftp_error_handler">
    <di:waypoint x="760" xsi:type="dc:Point" y="170.0"/>
    <di:waypoint x="780" xsi:type="dc:Point" y="170.0"/>
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_4" id="BPMNEdge_SequenceFlow_4" sourceElement="BPMNShape_sftp_error_handler" targetElement="BPMNShape_success_logger">
    <di:waypoint x="880" xsi:type="dc:Point" y="170.0"/>
    <di:waypoint x="900" xsi:type="dc:Point" y="170.0"/>
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_5" id="BPMNEdge_SequenceFlow_5" sourceElement="BPMNShape_success_logger" targetElement="BPMNShape_sftp_transfer">
    <di:waypoint x="1000" xsi:type="dc:Point" y="170.0"/>
    <di:waypoint x="1020" xsi:type="dc:Point" y="170.0"/>
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_End" id="BPMNEdge_SequenceFlow_End" sourceElement="BPMNShape_error_notification" targetElement="BPMNShape_EndEvent_2">
    <di:waypoint x="1240" xsi:type="dc:Point" y="170.0"/>
    <di:waypoint x="950" xsi:type="dc:Point" y="128.0"/>
</bpmndi:BPMNEdge>
                </bpmndi:BPMNPlane>
            
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>