<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="800" viewBox="0 0 1400 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients -->
    <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="frontendGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="backendGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="databaseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EF4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="externalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6B7280;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4B5563;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="sapGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>

    <!-- Arrow marker -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4B5563" />
    </marker>

    <!-- Animated arrow marker -->
    <marker id="arrowheadAnimated" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#059669" />
    </marker>
  </defs>

  <!-- Background -->
  <rect width="1400" height="800" fill="#F8FAFC"/>

  <!-- Title -->
  <text x="700" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#1F2937">
    MuleSoft to SAP Integration Suite - AI-Powered Migration Architecture
  </text>

  <!-- Layer Labels -->
  <text x="50" y="80" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6B7280">USER LAYER</text>
  <text x="300" y="80" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6B7280">FRONTEND LAYER</text>
  <text x="600" y="80" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6B7280">BACKEND SERVICES</text>
  <text x="900" y="80" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6B7280">AI SERVICES</text>
  <text x="1200" y="80" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6B7280">EXTERNAL</text>

  <!-- User Layer -->
  <g id="users">
    <rect x="50" y="200" width="180" height="80" rx="12" fill="url(#userGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <circle cx="80" cy="220" r="8" fill="#FFFFFF"/>
    <text x="100" y="225" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">Users</text>
    <text x="60" y="245" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">Developers &amp; Architects</text>
    <text x="60" y="260" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">Migration Platform Users</text>
  </g>

  <!-- Frontend Layer -->
  <g id="frontend-app">
    <rect x="300" y="120" width="180" height="80" rx="12" fill="url(#frontendGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <rect x="315" y="135" width="12" height="8" fill="#FFFFFF"/>
    <text x="335" y="145" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">Frontend App</text>
    <text x="310" y="165" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">React.js Interface</text>
    <text x="310" y="180" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">File Upload &amp; Dashboard</text>
  </g>

  <g id="api-gateway">
    <rect x="300" y="250" width="180" height="80" rx="12" fill="url(#frontendGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <circle cx="320" cy="270" r="6" fill="#FFFFFF"/>
    <text x="335" y="275" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">API Gateway</text>
    <text x="310" y="295" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">Cloud Foundry Router</text>
    <text x="310" y="310" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">Request Routing</text>
  </g>

  <!-- Backend Services -->
  <g id="doc-api">
    <rect x="550" y="100" width="180" height="80" rx="12" fill="url(#backendGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <rect x="565" y="115" width="10" height="12" fill="#FFFFFF"/>
    <text x="585" y="125" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">Documentation API</text>
    <text x="560" y="145" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">MuleSoft Parser</text>
    <text x="560" y="160" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">XML Processing &amp; Docs</text>
  </g>

  <g id="matcher-api">
    <rect x="550" y="200" width="180" height="80" rx="12" fill="url(#backendGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <polygon points="565,215 575,220 565,225" fill="#FFFFFF"/>
    <text x="585" y="225" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">SAP Matcher API</text>
    <text x="560" y="245" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">Integration Finder</text>
    <text x="560" y="260" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">Pattern Matching</text>
  </g>

  <g id="iflow-generator">
    <rect x="550" y="300" width="180" height="80" rx="12" fill="url(#backendGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <circle cx="570" cy="320" r="8" fill="#FFFFFF"/>
    <text x="590" y="325" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">iFlow Generator</text>
    <text x="560" y="345" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">AI-Powered Creation</text>
    <text x="560" y="360" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">Template Engine</text>
    <!-- AI Badge -->
    <circle cx="710" cy="310" r="12" fill="#FCD34D"/>
    <text x="710" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#92400E">AI</text>
  </g>

  <!-- Machine Learning Services -->
  <g id="ml-pattern-matcher">
    <rect x="850" y="150" width="180" height="80" rx="12" fill="url(#aiGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <circle cx="870" cy="170" r="8" fill="#FFFFFF"/>
    <text x="890" y="175" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">ML Pattern Matcher</text>
    <text x="860" y="195" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">NLTK + ML Engine</text>
    <text x="860" y="210" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">Pattern Recognition</text>
  </g>

  <!-- Database -->
  <g id="database">
    <rect x="550" y="450" width="180" height="80" rx="12" fill="url(#databaseGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <ellipse cx="575" cy="470" rx="8" ry="5" fill="#FFFFFF"/>
    <ellipse cx="575" cy="480" rx="8" ry="5" fill="none" stroke="#FFFFFF" stroke-width="1"/>
    <text x="595" y="475" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">PostgreSQL DB</text>
    <text x="560" y="495" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">Google Cloud Platform</text>
    <text x="560" y="510" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">Data Persistence</text>
  </g>

  <!-- External Services -->
  <g id="github-api">
    <rect x="1150" y="150" width="180" height="80" rx="12" fill="url(#externalGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <circle cx="1170" cy="170" r="6" fill="#FFFFFF"/>
    <path d="M1165,170 L1175,165 M1175,165 L1175,175 M1175,175 L1165,170" stroke="#FFFFFF" stroke-width="1" fill="none"/>
    <text x="1185" y="175" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">GitHub API</text>
    <text x="1160" y="195" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">Repository Access</text>
    <text x="1160" y="210" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">SAP Samples</text>
  </g>

  <g id="anthropic-api">
    <rect x="1150" y="280" width="180" height="80" rx="12" fill="url(#externalGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <circle cx="1170" cy="300" r="8" fill="#FFFFFF"/>
    <text x="1190" y="305" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">Anthropic API</text>
    <text x="1160" y="325" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">Claude AI</text>
    <text x="1160" y="340" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">NLP &amp; Generation</text>
  </g>

  <!-- iFlow Code (Generated Output) -->
  <g id="iflow-code">
    <rect x="1150" y="450" width="180" height="80" rx="12" fill="url(#backendGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <rect x="1170" y="470" width="12" height="8" fill="#FFFFFF"/>
    <rect x="1170" y="485" width="8" height="8" fill="#FFFFFF"/>
    <text x="1195" y="475" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">iFlow Code</text>
    <text x="1160" y="495" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">Generated Output</text>
    <text x="1160" y="510" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">XML Structure</text>
  </g>

  <!-- SAP Integration Suite -->
  <g id="sap-integration">
    <rect x="850" y="550" width="180" height="80" rx="12" fill="url(#sapGradient)" stroke="#FFFFFF" stroke-width="2"/>
    <rect x="870" y="565" width="12" height="8" fill="#FFFFFF"/>
    <rect x="870" y="580" width="8" height="8" fill="#FFFFFF"/>
    <text x="895" y="575" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">SAP Integration Suite</text>
    <text x="860" y="595" font-family="Arial, sans-serif" font-size="11" fill="#E5E7EB">Target Platform</text>
    <text x="860" y="610" font-family="Arial, sans-serif" font-size="10" fill="#E5E7EB">iFlow Deployment</text>
    <!-- TARGET Badge -->
    <rect x="995" y="560" width="30" height="15" rx="7" fill="#059669"/>
    <text x="1010" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#FFFFFF">TARGET</text>
  </g>

  <!-- Connections -->
  <!-- User to Frontend -->
  <line x1="230" y1="240" x2="300" y2="160" stroke="#4B5563" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- Frontend to API Gateway -->
  <line x1="390" y1="200" x2="390" y2="250" stroke="#4B5563" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- API Gateway to Backend Services -->
  <line x1="480" y1="280" x2="550" y2="140" stroke="#4B5563" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="480" y1="290" x2="550" y2="240" stroke="#4B5563" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="480" y1="300" x2="550" y2="340" stroke="#4B5563" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- STEP 1: Documentation API to Anthropic API (Generate documentation) -->
  <line x1="730" y1="140" x2="1150" y2="320" stroke="#8B5CF6" stroke-width="3" marker-end="url(#arrowhead)" stroke-dasharray="5,5">
    <animate attributeName="stroke-dashoffset" values="0;-10" dur="1s" repeatCount="indefinite"/>
  </line>

  <!-- STEP 2: Documentation API to SAP Matcher API (Pass docs) -->
  <line x1="640" y1="180" x2="640" y2="200" stroke="#10B981" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="3,3">
    <animate attributeName="stroke-dashoffset" values="0;-6" dur="1.2s" repeatCount="indefinite"/>
  </line>

  <!-- STEP 3: Documentation API to iFlow Generator (Pass docs) -->
  <line x1="640" y1="180" x2="640" y2="300" stroke="#10B981" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="3,3">
    <animate attributeName="stroke-dashoffset" values="0;-6" dur="1.2s" repeatCount="indefinite"/>
  </line>

  <!-- STEP 4: SAP Matcher API to ML Pattern Matcher (Similarity search) -->
  <line x1="730" y1="240" x2="850" y2="190" stroke="#F59E0B" stroke-width="3" marker-end="url(#arrowhead)" stroke-dasharray="5,5">
    <animate attributeName="stroke-dashoffset" values="0;-10" dur="1s" repeatCount="indefinite"/>
  </line>

  <!-- STEP 5: ML Pattern Matcher to GitHub API (Repository access) -->
  <line x1="1030" y1="190" x2="1150" y2="190" stroke="#4B5563" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- STEP 6: iFlow Generator to Anthropic API (Generate iFlow) -->
  <line x1="730" y1="340" x2="1150" y2="320" stroke="#8B5CF6" stroke-width="3" marker-end="url(#arrowhead)" stroke-dasharray="5,5">
    <animate attributeName="stroke-dashoffset" values="0;-10" dur="1s" repeatCount="indefinite"/>
  </line>

  <!-- STEP 7: Anthropic API to iFlow Code (Generated code) -->
  <line x1="1240" y1="360" x2="1240" y2="450" stroke="#059669" stroke-width="3" marker-end="url(#arrowhead)" stroke-dasharray="4,4">
    <animate attributeName="stroke-dashoffset" values="0;-8" dur="0.9s" repeatCount="indefinite"/>
  </line>

  <!-- STEP 8: iFlow Code to SAP Integration Suite (Deploy) -->
  <line x1="1150" y1="490" x2="1030" y2="590" stroke="#059669" stroke-width="4" marker-end="url(#arrowheadAnimated)" stroke-dasharray="8,4">
    <animate attributeName="stroke-dashoffset" values="0;-12" dur="0.8s" repeatCount="indefinite"/>
  </line>

  <!-- Backend to Database -->
  <line x1="640" y1="380" x2="640" y2="450" stroke="#4B5563" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- Data Flow Legend -->
  <g id="legend">
    <rect x="50" y="650" width="300" height="120" rx="8" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="1"/>
    <text x="60" y="670" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1F2937">Data Flow Legend</text>

    <line x1="70" y1="690" x2="120" y2="690" stroke="#4B5563" stroke-width="2" marker-end="url(#arrowhead)"/>
    <text x="130" y="695" font-family="Arial, sans-serif" font-size="12" fill="#4B5563">Standard Data Flow</text>

    <line x1="70" y1="710" x2="120" y2="710" stroke="#8B5CF6" stroke-width="3" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
    <text x="130" y="715" font-family="Arial, sans-serif" font-size="12" fill="#8B5CF6">AI-Powered Processing</text>

    <line x1="70" y1="725" x2="120" y2="725" stroke="#F59E0B" stroke-width="3" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
    <text x="130" y="730" font-family="Arial, sans-serif" font-size="12" fill="#F59E0B">ML Pattern Matching</text>

    <line x1="70" y1="745" x2="120" y2="745" stroke="#059669" stroke-width="4" marker-end="url(#arrowheadAnimated)" stroke-dasharray="8,4"/>
    <text x="130" y="750" font-family="Arial, sans-serif" font-size="12" fill="#059669">Deployment Pipeline</text>

    <text x="60" y="755" font-family="Arial, sans-serif" font-size="10" fill="#6B7280">Click components for detailed information</text>
  </g>

</svg>
