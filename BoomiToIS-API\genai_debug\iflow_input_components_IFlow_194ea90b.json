{"process_name": "Create Salesforce Opportunities from Stripe Subscriptions", "description": "This integration creates Salesforce Opportunities based on Stripe Subscription data. It listens for Stripe subscription events, processes the subscription data, and creates corresponding opportunity records in Salesforce.", "endpoints": [{"method": "POST", "path": "/stripe/webhook", "purpose": "Receives subscription events from Stripe and creates corresponding Salesforce opportunities", "components": [{"type": "enricher", "name": "Set_Dynamic_Properties", "id": "set_dynamic_props_1", "config": {"content": "{\n  \"subscriptionId\": \"${body.data.object.id}\",\n  \"customerId\": \"${body.data.object.customer}\",\n  \"planId\": \"${body.data.object.plan.id}\",\n  \"amount\": \"${body.data.object.plan.amount / 100}\"\n}"}}, {"type": "enricher", "name": "Process_Data", "id": "process_data_1", "config": {"content": "{\n  \"headers\": {\n    \"Content-Type\": \"application/json\"\n  }\n}"}}, {"type": "request_reply", "name": "Create_Salesforce_Opportunity", "id": "create_sf_opportunity_1", "config": {"endpoint_path": "/services/data/v55.0/sobjects/Opportunity"}}, {"type": "groovy_script", "name": "Transform_Stripe_To_Salesforce", "id": "transform_stripe_to_sf_1", "config": {"script": "TransformStripeToSalesforce.groovy"}}], "error_handling": {"exception_subprocess": [{"type": "enricher", "name": "Log_Error", "id": "log_error_1", "trigger": "any_error", "config": {"content": "{\n  \"error\": \"${exception.message}\",\n  \"stacktrace\": \"${exception.stacktrace}\",\n  \"timestamp\": \"${date:now:yyyy-MM-dd'T'HH:mm:ss.SSS}\"\n}"}}, {"type": "request_reply", "name": "Send_Error_Notification", "id": "send_error_notification_1", "trigger": "any_error", "config": {"endpoint_path": "/api/notifications/email"}}]}, "branching": {"type": "exclusive", "branches": [{"condition": "Default path", "components": ["set_dynamic_props_1", "transform_stripe_to_sf_1", "process_data_1", "create_sf_opportunity_1"], "sequence": ["set_dynamic_props_1", "transform_stripe_to_sf_1", "process_data_1", "create_sf_opportunity_1"]}]}, "sequence": ["set_dynamic_props_1", "transform_stripe_to_sf_1", "process_data_1", "create_sf_opportunity_1"], "transformations": [{"name": "TransformStripeToSalesforce.groovy", "type": "groovy", "script": "import groovy.json.*\n\ndef inputBody = message.getBody(String.class)\ndef jsonSlurper = new JsonSlurper()\ndef stripeData = jsonSlurper.parseText(inputBody)\n\n// Extract properties from exchange\ndef subscriptionId = property.get(\"subscriptionId\")\ndef customerId = property.get(\"customerId\")\ndef planId = property.get(\"planId\")\ndef amount = property.get(\"amount\")\n\n// Create Salesforce opportunity object\ndef opportunity = [\n    Name: subscriptionId + \" - \" + planId,\n    Amount: amount,\n    CloseDate: new Date().format(\"yyyy-MM-dd\"),\n    StageName: \"Closed Won\",\n    Type: \"New Business\"\n]\n\n// Convert to JSON and set as message body\ndef jsonBuilder = new JsonBuilder(opportunity)\nmessage.setBody(jsonBuilder.toString())\n\nreturn message"}], "sequence_flows": [{"id": "SequenceFlow_Start", "source": "set_dynamic_props_1", "target": "process_data_1", "is_immediate": true, "xml_content": "<bpmn2:sequenceFlow id=\"SequenceFlow_Start\" sourceRef=\"set_dynamic_props_1\" targetRef=\"process_data_1\" isImmediate=\"true\"/>"}, {"id": "SequenceFlow_1", "source": "process_data_1", "target": "create_sf_opportunity_1", "is_immediate": true, "xml_content": "<bpmn2:sequenceFlow id=\"SequenceFlow_1\" sourceRef=\"process_data_1\" targetRef=\"create_sf_opportunity_1\" isImmediate=\"true\"/>"}, {"id": "SequenceFlow_End", "source": "create_sf_opportunity_1", "target": "transform_stripe_to_sf_1", "is_immediate": true, "xml_content": "<bpmn2:sequenceFlow id=\"SequenceFlow_End\" sourceRef=\"create_sf_opportunity_1\" targetRef=\"transform_stripe_to_sf_1\" isImmediate=\"true\"/>"}]}]}