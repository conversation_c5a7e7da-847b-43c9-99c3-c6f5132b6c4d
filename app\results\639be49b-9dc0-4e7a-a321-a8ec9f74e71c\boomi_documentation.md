# Stripe to Salesforce Opportunity Integration

## Table of Contents
- [API Overview](#api-overview)
- [Endpoints](#endpoints)
  - [Stripe Subscription Webhook](#stripe-subscription-webhook)
  - [Salesforce Opportunity Creation](#salesforce-opportunity-creation)
- [Current Dell Boomi Flow Logic](#current-dell-boomi-flow-logic)
  - [Process Trigger](#process-trigger)
  - [Processing Steps](#processing-steps)
  - [Data Transformations](#data-transformations)
  - [Expected Outcomes](#expected-outcomes)
- [DataWeave Transformations Explained](#dataweave-transformations-explained)
  - [Subscription JSON to Opportunity XML Transformation](#subscription-json-to-opportunity-xml-transformation)
- [SAP Integration Suite Implementation](#sap-integration-suite-implementation)
  - [Component Mapping](#component-mapping)
  - [Integration Flow Visualization](#integration-flow-visualization)
  - [Configuration Details](#configuration-details)
- [Environment Configuration](#environment-configuration)
  - [Configuration Parameters](#configuration-parameters)
  - [Environment Variables](#environment-variables)
  - [Security Settings](#security-settings)
- [API Reference](#api-reference)
  - [Stripe Webhook API](#stripe-webhook-api)
  - [Salesforce API](#salesforce-api)

## API Overview

This integration creates Salesforce Opportunities automatically when Stripe Subscriptions are completed. The integration listens for Stripe webhook events, specifically for subscription completion events, transforms the subscription data into Salesforce Opportunity format, and creates new Opportunity records in Salesforce.

- **Base URL/endpoint pattern**: Webhook endpoint that receives Stripe subscription events
- **Authentication mechanisms**: 
  - Stripe webhook authentication using webhook signatures
  - Salesforce authentication using OAuth credentials
- **Rate limiting information**: Subject to Salesforce API limits (typically 100,000 requests per 24-hour period)
- **General response format**: Success/failure confirmation for created Opportunities

## Endpoints

### Stripe Subscription Webhook

- **HTTP Method and full path**: POST /webhook/stripe/subscription
- **Purpose**: Receives webhook notifications from Stripe when subscription events occur
- **Request parameters**:
  - **Headers**:
    - `Stripe-Signature`: Signature to verify webhook authenticity
    - `Content-Type`: application/json
  - **Request body**: Stripe subscription event in JSON format
  - **Response**: 200 OK on successful processing

### Salesforce Opportunity Creation

- **HTTP Method and full path**: POST /services/data/v[VERSION]/sobjects/Opportunity
- **Purpose**: Creates a new Opportunity record in Salesforce
- **Request parameters**:
  - **Headers**:
    - `Authorization`: Bearer token for Salesforce authentication
    - `Content-Type`: application/xml
  - **Request body**: Opportunity data in XML format
  - **Response**: 
    - 201 Created with Opportunity ID on success
    - 4xx/5xx errors with error details on failure

## Current Dell Boomi Flow Logic

### Process Trigger
The integration flow is triggered by an incoming Stripe webhook event, specifically for subscription completion events. The webhook payload contains detailed information about the subscription that has been completed.

### Processing Steps
1. **Event Reception**: The process starts with receiving the Stripe webhook event (shape1)
2. **Dynamic Property Setting**: Sets dynamic properties based on the incoming event (shape6)
3. **Data Transformation**: Transforms the Stripe subscription JSON data to Salesforce Opportunity XML format (shape4)
4. **Salesforce Operation**: Creates a new Opportunity in Salesforce using the transformed data (shape3)
5. **Process Completion**: Ends the process flow after successful Opportunity creation (shape5)

### Data Transformations
The key transformation in this flow converts Stripe subscription data in JSON format to Salesforce Opportunity data in XML format. The mapping includes:
- Subscription details mapped to Opportunity Description
- Subscription name mapped to Opportunity Name
- Subscription completion date mapped to Opportunity CloseDate

### Expected Outcomes
- **Success Scenario**: A new Opportunity is created in Salesforce with data from the Stripe subscription
- **Error Scenarios**:
  - Invalid webhook signature from Stripe
  - Transformation errors due to missing required fields
  - Salesforce API errors (authentication, validation, etc.)

## DataWeave Transformations Explained

### Subscription JSON to Opportunity XML Transformation

This transformation converts the Stripe subscription data in JSON format to the XML format required by the Salesforce Opportunity CREATE operation.

**Input Format**: JSON data from Stripe webhook containing subscription details
**Output Format**: XML structure conforming to Salesforce Opportunity object requirements

The mapping focuses on three key fields:
1. **Description**: Maps subscription details to the Opportunity Description field
2. **Name**: Maps subscription name to the Opportunity Name field
3. **CloseDate**: Maps subscription completion date to the Opportunity CloseDate field

While the exact DataWeave code is not provided in the source documentation, the mapping relationships are defined as follows:

| From Field (Stripe) | To Field (Salesforce) | Type |
|---------------------|----------------------|------|
| 3 | Opportunity/Description | profile |
| 3 | Opportunity/Name | profile |
| 3 | Opportunity/CloseDate | profile |

The "3" reference likely indicates a specific node or element in the source JSON structure, and "profile" indicates a mapping profile is being used to handle any necessary data type conversions or formatting.

## SAP Integration Suite Implementation

### Component Mapping

| Dell Boomi Component | SAP Integration Suite Equivalent | Notes |
|----------------------|----------------------------------|-------|
| shape1 (Start) | Start Message Event | Webhook receiver for Stripe events |
| shape6 (Set Dynamic Properties) | Content Modifier | Sets properties/headers based on incoming message |
| shape4 (Transform) | Message Mapping | Maps JSON to XML using SAP Cloud Integration mapping |
| shape3 (Salesforce Operation) | Salesforce Adapter | Uses SAP CPI Salesforce adapter to create Opportunity |
| shape5 (End) | End Message Event | Completes the integration flow |

### Integration Flow Visualization

```mermaid
flowchart TD
    %% Define node styles
    classDef httpAdapter fill:#87CEEB,stroke:#333,stroke-width:2px
    classDef contentModifier fill:#98FB98,stroke:#333,stroke-width:2px
    classDef router fill:#FFB6C1,stroke:#333,stroke-width:2px
    classDef mapping fill:#DDA0DD,stroke:#333,stroke-width:2px
    classDef exception fill:#FFA07A,stroke:#333,stroke-width:2px
    classDef processCall fill:#F0E68C,stroke:#333,stroke-width:2px

    %% Main Flow
    Start((Start)) --> StripeWebhookReceiver[Stripe Webhook Receiver]:::httpAdapter
    StripeWebhookReceiver --> SetDynamicProperties[Set Dynamic Properties]:::contentModifier
    SetDynamicProperties --> TransformSubscriptionToOpportunity[Transform Subscription to Opportunity]:::mapping
    TransformSubscriptionToOpportunity --> CreateSalesforceOpportunity[Create Salesforce Opportunity]:::httpAdapter
    CreateSalesforceOpportunity --> End((End))

    %% Error Handling
    StripeWebhookReceiver -->|Error| ErrorHandler[(Global Error Handler)]:::exception
    SetDynamicProperties -->|Error| ErrorHandler
    TransformSubscriptionToOpportunity -->|Error| ErrorHandler
    CreateSalesforceOpportunity -->|Error| ErrorHandler
    ErrorHandler --> LogError[Log Error Details]:::contentModifier
    LogError --> ErrorResponse[Set Error Response]:::contentModifier
    ErrorResponse --> ErrorEnd((Error End))
```

### Configuration Details

#### Stripe Webhook Receiver
- **Component Type**: HTTP Adapter (Receiver)
- **Parameters**:
  - Path: /webhook/stripe/subscription
  - Method: POST
  - Authentication: None (relies on webhook signature validation)
  - CSRF Protection: Disabled

#### Set Dynamic Properties
- **Component Type**: Content Modifier
- **Parameters**:
  - Action: Set Properties
  - Property Names: Based on Stripe subscription data
  - Property Values: Extracted from webhook payload

#### Transform Subscription to Opportunity
- **Component Type**: Message Mapping
- **Parameters**:
  - Source Format: JSON
  - Target Format: XML
  - Mapping Details:
    - Source Path 3 → Target Path Opportunity/Description
    - Source Path 3 → Target Path Opportunity/Name
    - Source Path 3 → Target Path Opportunity/CloseDate

#### Create Salesforce Opportunity
- **Component Type**: Salesforce Adapter
- **Parameters**:
  - Operation: Create
  - Object: Opportunity
  - Authentication: OAuth (configured in Security Material)
  - Request Format: XML

#### Error Handler
- **Component Type**: Exception Subprocess
- **Parameters**:
  - Error Types: All
  - Error Logging: Enabled
  - Response Code: Based on error type

## Environment Configuration

### Configuration Parameters
- **Stripe Webhook Secret**: Used to validate incoming webhook signatures
- **Salesforce API Version**: Version of the Salesforce API to use (e.g., v53.0)
- **Salesforce Instance URL**: URL of the Salesforce instance (e.g., https://yourinstance.salesforce.com)

### Environment Variables
- **STRIPE_WEBHOOK_SECRET**: Secret key for validating Stripe webhook signatures
- **SF_CLIENT_ID**: Salesforce OAuth client ID
- **SF_CLIENT_SECRET**: Salesforce OAuth client secret
- **SF_USERNAME**: Salesforce username for authentication
- **SF_PASSWORD**: Salesforce password for authentication
- **SF_SECURITY_TOKEN**: Salesforce security token (if required)

### Security Settings
- **Stripe Webhook Authentication**: Validate incoming webhook requests using the Stripe-Signature header
- **Salesforce Authentication**: OAuth 2.0 authentication for Salesforce API access
- **Credential Storage**: Store credentials securely in SAP Cloud Integration's Security Material store
- **Transport Layer Security**: HTTPS for all external communications

## API Reference

### Stripe Webhook API

#### Subscription Events
- **Endpoint**: POST /webhook/stripe/subscription
- **Authentication**: Stripe-Signature header validation
- **Request Body**: JSON payload with subscription event details
- **Event Types**:
  - customer.subscription.created
  - customer.subscription.updated
  - customer.subscription.deleted
  - customer.subscription.trial_will_end

#### Example Webhook Payload
```json
{
  "id": "evt_1234567890",
  "object": "event",
  "api_version": "2020-08-27",
  "created": 1610000000,
  "data": {
    "object": {
      "id": "sub_1234567890",
      "object": "subscription",
      "customer": "cus_1234567890",
      "status": "active",
      "plan": {
        "id": "plan_1234567890",
        "name": "Premium Plan"
      },
      "current_period_start": 1610000000,
      "current_period_end": 1612678400
    }
  },
  "type": "customer.subscription.created"
}
```

### Salesforce API

#### Create Opportunity
- **Endpoint**: POST /services/data/v{version}/sobjects/Opportunity
- **Authentication**: OAuth 2.0 Bearer Token
- **Content-Type**: application/xml
- **Request Body**: XML representation of Opportunity object

#### Example Request Body
```xml
<Opportunity>
  <Name>Premium Plan - Customer ABC</Name>
  <Description>Subscription ID: sub_1234567890, Plan: Premium Plan</Description>
  <CloseDate>2023-01-15</CloseDate>
  <StageName>Closed Won</StageName>
  <Amount>99.99</Amount>
  <Type>New Business</Type>
</Opportunity>
```

#### Response Codes
- **201 Created**: Opportunity successfully created
- **400 Bad Request**: Invalid request format or missing required fields
- **401 Unauthorized**: Authentication failure
- **403 Forbidden**: Insufficient permissions
- **500 Internal Server Error**: Server-side error processing the request