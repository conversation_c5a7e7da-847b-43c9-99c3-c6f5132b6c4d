{"process_name": "SAP SuccessFactors to SFTP Integration with Error Handling", "description": "This integration extracts employee data from SAP SuccessFactors and transfers it to an SFTP server with comprehensive error handling.", "endpoints": [{"method": "GET", "path": "/successfactors/employee-data", "purpose": "Extract employee data from SAP SuccessFactors and transfer to SFTP server", "components": [{"type": "request_reply", "name": "Get_SuccessFactors_Employee_Data", "id": "successfactors_request", "config": {"endpoint_path": "/successfactors/api/employee-data", "address": "https://api.successfactors.com"}}, {"type": "request_reply", "name": "Send_To_SFTP", "id": "sftp_transfer", "config": {"endpoint_path": "/sftp/employee-data", "address": "sftp://sftp.example.com"}}, {"type": "groovy_script", "name": "Transform_Canonical_To_Kafka_Avro", "id": "transform_data", "config": {"script": "// Transform SuccessFactors data to Kafka Avro format\ndef inputMessage = message.getBody(java.lang.String)\ndef jsonSlurper = new groovy.json.JsonSlurper()\ndef data = jsonSlurper.parseText(inputMessage)\n\ndef result = [:]\nresult.batchProcessingDirectives = [:]\nresult.batchProcessingDirectives.accountID = [:]\nresult.batchProcessingDirectives.accountID.username = data[9]\n\nresult.batchContactList = []\ndef contactElement = [:]\ncontactElement.contact = []\ndef contact = [:]\ncontact.contactID = data[91]\ncontact.contactPointList = []\ndef contactPointList = [:]\ncontactPointList.contactPoint = []\ndef contactPoint = [:]\ncontactPoint.type = data[111]\ncontactPointList.contactPoint.add(contactPoint)\ncontact.contactPointList.add(contactPointList)\ncontactElement.contact.add(contact)\nresult.batchContactList.add(contactElement)\n\nresult.batchProcessingDirectives.batchProcessingOption = []\ndef option = [:]\noption.name = data[118]\nresult.batchProcessingDirectives.batchProcessingOption.add(option)\n\nreturn groovy.json.JsonOutput.toJson(result)"}}], "error_handling": {"exception_subprocess": [{"type": "enricher", "name": "Prepare_Error_Log", "id": "error_log_enricher", "trigger": "any_error", "config": {"content": "${exception.message}"}}, {"type": "request_reply", "name": "Send_Error_Notification", "id": "error_notification", "trigger": "any_error", "config": {"endpoint_path": "/notification/email", "address": "https://notification.example.com"}}]}, "branching": {"type": "exclusive", "branches": [{"condition": "success", "components": ["successfactors_request", "transform_data", "sftp_transfer"], "sequence": ["successfactors_request", "transform_data", "sftp_transfer"]}]}, "sequence": ["successfactors_request", "transform_data", "sftp_transfer"], "transformations": [{"name": "TransformCanonicalToKafkaAvro.groovy", "type": "groovy", "script": "// Transform SuccessFactors data to Kafka Avro format\ndef inputMessage = message.getBody(java.lang.String)\ndef jsonSlurper = new groovy.json.JsonSlurper()\ndef data = jsonSlurper.parseText(inputMessage)\n\ndef result = [:]\nresult.batchProcessingDirectives = [:]\nresult.batchProcessingDirectives.accountID = [:]\nresult.batchProcessingDirectives.accountID.username = data[9]\n\nresult.batchContactList = []\ndef contactElement = [:]\ncontactElement.contact = []\ndef contact = [:]\ncontact.contactID = data[91]\ncontact.contactPointList = []\ndef contactPointList = [:]\ncontactPointList.contactPoint = []\ndef contactPoint = [:]\ncontactPoint.type = data[111]\ncontactPointList.contactPoint.add(contactPoint)\ncontact.contactPointList.add(contactPointList)\ncontactElement.contact.add(contact)\nresult.batchContactList.add(contactElement)\n\nresult.batchProcessingDirectives.batchProcessingOption = []\ndef option = [:]\noption.name = data[118]\nresult.batchProcessingDirectives.batchProcessingOption.add(option)\n\nreturn groovy.json.JsonOutput.toJson(result)"}], "sequence_flows": [{"id": "SequenceFlow_Start", "source": "successfactors_request", "target": "sftp_transfer", "is_immediate": true, "xml_content": "<bpmn2:sequenceFlow id=\"SequenceFlow_Start\" sourceRef=\"successfactors_request\" targetRef=\"sftp_transfer\" isImmediate=\"true\"/>"}, {"id": "SequenceFlow_End", "source": "sftp_transfer", "target": "transform_data", "is_immediate": true, "xml_content": "<bpmn2:sequenceFlow id=\"SequenceFlow_End\" sourceRef=\"sftp_transfer\" targetRef=\"transform_data\" isImmediate=\"true\"/>"}]}]}