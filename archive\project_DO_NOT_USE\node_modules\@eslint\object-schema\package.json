{"name": "@eslint/object-schema", "version": "2.1.6", "description": "An object schema merger/validator", "type": "module", "main": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {"require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}}, "files": ["dist"], "publishConfig": {"access": "public"}, "directories": {"test": "tests"}, "scripts": {"build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "build": "rollup -c && tsc -p tsconfig.esm.json && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "test": "mocha tests/", "test:coverage": "c8 npm test"}, "repository": {"type": "git", "url": "git+https://github.com/eslint/rewrite.git"}, "keywords": ["object", "validation", "schema", "merge"], "author": "<PERSON>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "homepage": "https://github.com/eslint/rewrite#readme", "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "rollup-plugin-copy": "^3.5.0", "typescript": "^5.4.5"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}