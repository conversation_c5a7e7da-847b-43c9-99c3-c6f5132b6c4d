
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Integration Match Report</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }
                .container { max-width: 1200px; margin: 0 auto; }
                h1, h2, h3 { color: #2c3e50; }
                .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                .recommendations { margin-bottom: 20px; }
                .recommendation { padding: 10px; margin-bottom: 10px; border-radius: 5px; }
                .recommendation.warning { background-color: #fff3cd; border-left: 5px solid #ffc107; }
                .recommendation.success { background-color: #d4edda; border-left: 5px solid #28a745; }
                .recommendation.info { background-color: #d1ecf1; border-left: 5px solid #17a2b8; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
                th { background-color: #f2f2f2; }
                .quality-high { color: #28a745; font-weight: bold; }
                .quality-medium { color: #ffc107; font-weight: bold; }
                .quality-low { color: #dc3545; font-weight: bold; }
                .detail-view { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; display: none; }
                .score-bar { background-color: #e9ecef; height: 20px; border-radius: 5px; margin-bottom: 5px; }
                .score-fill { height: 100%; border-radius: 5px; }
                .term-match { background-color: #007bff; }
                .endpoint-match { background-color: #28a745; }
                .content-similarity { background-color: #ffc107; }
                .search-priority { background-color: #6c757d; }
                .combined-score { background-color: #dc3545; }
                .toggle-btn { background-color: #4CAF50; color: white; padding: 8px 12px; border: none; border-radius: 4px; cursor: pointer; }
                .tag { display: inline-block; background-color: #e9ecef; padding: 3px 8px; border-radius: 3px; margin-right: 5px; margin-bottom: 5px; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Integration Match Report</h1>
                <p>Generated on: 2025-06-23 17:18:24</p>

                <div class="summary">
                    <h2>Summary</h2>
                    <p><strong>Total Results:</strong> 26</p>
                    <p><strong>High-Quality Matches:</strong> 2</p>
                    <p><strong>Medium-Quality Matches:</strong> 13</p>
                    <p><strong>Recommendation:</strong> Found 2 high-quality matches. Review the top matches for potential integration.</p>
                </div>

                <div class="recommendations">
                    <h2>Recommendations</h2>
        
                    <div class="recommendation success">
                        <h3>Found 2 high-quality matches</h3>
                        <p>The top match 'SAP Ariba Integration with SAP Ariba APIs' is a good candidate for your implementation.</p>
                        <p><strong>Recommended Action:</strong> Review the details and consider downloading this integration package.</p>
                    </div>
            
                    <div class="recommendation info">
                        <h3>Multiple good matches found</h3>
                        <p>Consider comparing the top matches to find the best fit for your requirements.</p>
                        <p><strong>Recommended Action:</strong> Use the detailed view to compare features and compatibility.</p>
                    </div>
            
                    <div class="recommendation info">
                        <h3>Integration approach recommendation</h3>
                        <p>Found API/flow integration patterns that match your Mulesoft implementation.</p>
                        <p><strong>Recommended Action:</strong> Consider a direct API approach for migrating your Mulesoft implementation.</p>
                    </div>
            
                </div>

                <h2>Match Results</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Score</th>
                            <th>Quality</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
        
                        <tr>
                            <td>1</td>
                            <td>SAP Ariba Integration with SAP Ariba APIs</td>
                            <td>IntegrationFlow</td>
                            <td>22.15</td>
                            <td class="quality-high">High</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-1')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>2</td>
                            <td>Build custom Azure Blob Storage integration adapter</td>
                            <td>IntegrationFlow</td>
                            <td>21.45</td>
                            <td class="quality-high">High</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-2')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>3</td>
                            <td>Supplier Integration between SAP Integrated Business Planning and SAP Business Networks</td>
                            <td>IntegrationFlow</td>
                            <td>15.51</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-3')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>4</td>
                            <td>SAP SuccessFactors Employee Central Integration with Fidelity</td>
                            <td>IntegrationFlow</td>
                            <td>15.50</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-4')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>5</td>
                            <td>SAP SuccessFactors Employee Central Integration with SAP Commissions</td>
                            <td>IntegrationFlow</td>
                            <td>15.50</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-5')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>6</td>
                            <td>SAP SuccessFactors Employee Central Integration with SAP Concur</td>
                            <td>IntegrationFlow</td>
                            <td>15.50</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-6')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>7</td>
                            <td>B2B Integration Factory - Communication Receiver Flows (Custom)</td>
                            <td>IntegrationFlow</td>
                            <td>15.50</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-7')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>8</td>
                            <td>SAP Emarsys Integration - Starter Pack</td>
                            <td>IntegrationFlow</td>
                            <td>15.15</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-8')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>9</td>
                            <td>Message Transformation Utilities</td>
                            <td>Adapter</td>
                            <td>14.81</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-9')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>10</td>
                            <td>B2B Integration Factory - Interchange Assembly Flows</td>
                            <td>IntegrationFlow</td>
                            <td>12.35</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-10')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>11</td>
                            <td>SAP Analytics Cloud Integration with SAP SuccessFactors Position Write Back Outbound Flow</td>
                            <td>IntegrationFlow</td>
                            <td>12.00</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-11')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>12</td>
                            <td>Deploy Run Once Integration Artefact and Check MPL and Store If Success</td>
                            <td>IntegrationFlow</td>
                            <td>11.31</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-12')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>13</td>
                            <td>Build custom MongoDB integration adapter</td>
                            <td>IntegrationFlow</td>
                            <td>10.95</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-13')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>14</td>
                            <td>Command Message</td>
                            <td>IntegrationPattern</td>
                            <td>10.95</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-14')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>15</td>
                            <td>B2B Integration Factory - Interchange Extraction Flows</td>
                            <td>IntegrationFlow</td>
                            <td>10.25</td>
                            <td class="quality-medium">Medium</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-15')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>16</td>
                            <td>Deploy Integration Artefact and Get Endpoint</td>
                            <td>IntegrationFlow</td>
                            <td>9.22</td>
                            <td class="quality-low">Low</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-16')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>17</td>
                            <td>Build custom Rabbit MQ integration adapter</td>
                            <td>IntegrationFlow</td>
                            <td>8.85</td>
                            <td class="quality-low">Low</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-17')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>18</td>
                            <td>Upload Integration Artefact</td>
                            <td>IntegrationFlow</td>
                            <td>7.81</td>
                            <td class="quality-low">Low</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-18')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>19</td>
                            <td>Deploy Run Once Integration Artefact and Check MPL</td>
                            <td>IntegrationFlow</td>
                            <td>7.81</td>
                            <td class="quality-low">Low</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-19')">Details</button></td>
                        </tr>
            
                        <tr>
                            <td>20</td>
                            <td>Event Message</td>
                            <td>IntegrationPattern</td>
                            <td>6.76</td>
                            <td class="quality-low">Low</td>
                            <td><button class="toggle-btn" onclick="toggleDetail('detail-20')">Details</button></td>
                        </tr>
            
                    </tbody>
                </table>
        
                <div id="detail-1" class="detail-view">
                    <h3>SAP Ariba Integration with SAP Ariba APIs</h3>
                    <p><strong>Description:</strong> This integration flow allows you to connect an Ariba system to the SAP Ariba APIs.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">SAP Ariba or SAP Business Network</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">This</span><span class="tag">Ariba</span><span class="tag">APIs</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This integration</span><span class="tag">This</span><span class="tag">API</span><span class="tag">API</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 59.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 100%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.01</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 1.4387607161746945%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 22.15</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 44.30719380358087%"></div>
                    </div>
                </div>
            
                <div id="detail-2" class="detail-view">
                    <h3>Build custom Azure Blob Storage integration adapter</h3>
                    <p><strong>Description:</strong> Azure Blob Storage is a massively scalable and secure object storage for cloud-native workloads, archives, data lakes, high-performance computing and machine learning. The camel-Azure Blob Storage component stores and retrieves blobs from [Azure Storage Blob Service](https://azure.microsoft.com/services/storage/blobs/) using Azure APIs v12. This integration adapter enables an integration flow to connect to Azure Blob Storage collection.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">Integration Adapters</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Blob</span><span class="tag">Azure</span><span class="tag">This</span><span class="tag">Service</span><span class="tag">Storage</span><span class="tag">APIs</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This integration</span><span class="tag">This</span><span class="tag">API</span><span class="tag">HTTPS</span><span class="tag">HTTP</span><span class="tag">API</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 57.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 100%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.02</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 1.7947493726753903%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 21.45</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 42.908973746863374%"></div>
                    </div>
                </div>
            
                <div id="detail-3" class="detail-view">
                    <h3>Supplier Integration between SAP Integrated Business Planning and SAP Business Networks</h3>
                    <p><strong>Description:</strong> Replicate supplier related data between SAP Integrated Business Planning (SAP IBP) and SAP Business Networks. These iFlows help to exchange data between SAP Integrated Business Planning and SAP Business Networks from a Supplier prespective. The flows can either be triggered via an REST API or via Timer-based Iflow.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">SAP Ariba or SAP Business Network</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Supplier</span><span class="tag">REST</span><span class="tag">Replicate</span><span class="tag">Integrated</span><span class="tag">Iflow</span><span class="tag">Networks</span><span class="tag">Timer</span><span class="tag">Planning</span><span class="tag">These</span><span class="tag">Business</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">API</span><span class="tag">Rate</span><span class="tag">API</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 40.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 80%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.02</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 2.009637407493441%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 15.51</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 31.010048187037466%"></div>
                    </div>
                </div>
            
                <div id="detail-4" class="detail-view">
                    <h3>SAP SuccessFactors Employee Central Integration with Fidelity</h3>
                    <p><strong>Description:</strong> This integration package provides integration flow which allows you to read and send employee data from SAP SuccessFactors Employee Central to the Fidelity sever.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">SAP SuccessFactors Employee Central</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">This</span><span class="tag">SuccessFactors</span><span class="tag">Employee</span><span class="tag">Fidelity</span><span class="tag">Central</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This integration</span><span class="tag">This</span><span class="tag">Success</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 40.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 80%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.02</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 1.8337408520096121%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 15.50</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 31.00916870426005%"></div>
                    </div>
                </div>
            
                <div id="detail-5" class="detail-view">
                    <h3>SAP SuccessFactors Employee Central Integration with SAP Commissions</h3>
                    <p><strong>Description:</strong> This integration package provides integration flows to replicate employee data from SAP SuccessFactors Employee Central to SAP Commissions.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">SAP SuccessFactors Employee Central</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Commissions</span><span class="tag">This</span><span class="tag">SuccessFactors</span><span class="tag">Employee</span><span class="tag">Central</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This integration</span><span class="tag">This</span><span class="tag">Success</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 40.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 80%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.02</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 1.5507706323129378%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 15.50</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 31.007753853161564%"></div>
                    </div>
                </div>
            
                <div id="detail-6" class="detail-view">
                    <h3>SAP SuccessFactors Employee Central Integration with SAP Concur</h3>
                    <p><strong>Description:</strong> This integration flow fetches employee details from SAP SuccessFactors Employee Central and based on the events/data changes, determines the records to be sent to SAP Concur.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">SAP Concur</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Concur</span><span class="tag">This</span><span class="tag">SuccessFactors</span><span class="tag">Employee</span><span class="tag">Central</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This integration</span><span class="tag">This</span><span class="tag">Success</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 40.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 80%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.01</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 1.1851928821223077%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 15.50</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 31.00592596441061%"></div>
                    </div>
                </div>
            
                <div id="detail-7" class="detail-view">
                    <h3>B2B Integration Factory - Communication Receiver Flows (Custom)</h3>
                    <p><strong>Description:</strong> This integration package should cover all custom communication receiver flows, connectable via ProcessDirect at the receiver step of a TPA --> Business Transaction Activity.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">B2B Integration Factory</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Activity</span><span class="tag">ProcessDirect</span><span class="tag">This</span><span class="tag">Transaction</span><span class="tag">Business</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This integration</span><span class="tag">This</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 40.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 80%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.01</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 0.9785608057333578%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 15.50</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 31.004892804028668%"></div>
                    </div>
                </div>
            
                <div id="detail-8" class="detail-view">
                    <h3>SAP Emarsys Integration - Starter Pack</h3>
                    <p><strong>Description:</strong> This integration flow allows you to connect to the SAP Emarsys APIs</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">SAP BTP</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">APIs</span><span class="tag">This</span><span class="tag">Emarsys</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This integration</span><span class="tag">This</span><span class="tag">API</span><span class="tag">API</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 39.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 78%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.02</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 1.9752760193373322%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 15.15</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 30.309876380096682%"></div>
                    </div>
                </div>
            
                <div id="detail-9" class="detail-view">
                    <h3>Message Transformation Utilities</h3>
                    <p><strong>Description:</strong> This package contains artifacts that help transform message payloads between various formats, such as XML, plain text, CSV, and JSON. The overall aim is to deliver various functionalities that once existed in SAP Process Orchestration as Adapter Modules.</p>
                    <p><strong>Type:</strong> Adapter</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">SAP BTP</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Modules</span><span class="tag">Process</span><span class="tag">This</span><span class="tag">Orchestration</span><span class="tag">JSON</span><span class="tag">Adapter</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This</span><span class="tag">Transformation</span><span class="tag">JSON</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 38.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 76%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.02</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 2.438455882588567%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 14.81</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 29.61219227941294%"></div>
                    </div>
                </div>
            
                <div id="detail-10" class="detail-view">
                    <h3>B2B Integration Factory - Interchange Assembly Flows</h3>
                    <p><strong>Description:</strong> This package includes all integration flows, which are responsible for the final assembly of target (receiver) interchange/message payloads according the conventions of the considered type system.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">B2B Integration Factory</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">This</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 31.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 62%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.01</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 1.161948088877231%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 12.35</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 24.705809740444387%"></div>
                    </div>
                </div>
            
                <div id="detail-11" class="detail-view">
                    <h3>SAP Analytics Cloud Integration with SAP SuccessFactors Position Write Back Outbound Flow</h3>
                    <p><strong>Description:</strong> Integration between SAP Analytics Cloud HXM Planning and SAP SuccessFactors Positions.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">SAP Analytics Cloud</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Analytics</span><span class="tag">Integration</span><span class="tag">Cloud</span><span class="tag">Positions</span><span class="tag">SuccessFactors</span><span class="tag">Planning</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">Success</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 30.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 60%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.01</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 1.3759092876801362%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 12.00</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 24.0068795464384%"></div>
                    </div>
                </div>
            
                <div id="detail-12" class="detail-view">
                    <h3>Deploy Run Once Integration Artefact and Check MPL and Store If Success</h3>
                    <p><strong>Description:</strong> Deploy an integration flow, check its deployment status and the MPL (message processing log) status. In case of a successful message processing the job then downloads the integration flow artefact from the Cloud Integration tenant and commits it to the source code repository.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">CICD</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Deploy</span><span class="tag">Cloud</span><span class="tag">Integration</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">Success</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 28.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 56%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.04</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 3.515536954101692%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 11.31</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 22.617577684770506%"></div>
                    </div>
                </div>
            
                <div id="detail-13" class="detail-view">
                    <h3>Build custom MongoDB integration adapter</h3>
                    <p><strong>Description:</strong> MongoDB is a very popular NoSQL solution and the camel-mongodb component integrates Camel with MongoDB allowing you to interact with MongoDB collections both as a producer (performing operations on the collection) and as a consumer (consuming documents from a MongoDB collection). This integration adapter enables an integration flow to connect to MongoDb collection.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">Integration Adapters</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">MongoDb</span><span class="tag">This</span><span class="tag">MongoDB</span><span class="tag">NoSQL</span><span class="tag">Camel</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This integration</span><span class="tag">This</span><span class="tag">Rate</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 27.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 54%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.01</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 1.0777806925125029%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 10.95</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 21.905388903462562%"></div>
                    </div>
                </div>
            
                <div id="detail-14" class="detail-view">
                    <h3>Command Message</h3>
                    <p><strong>Description:</strong> This recipe lets you try out Command Message pattern for SOAP, OData and a BAPI.</p>
                    <p><strong>Type:</strong> IntegrationPattern</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">Integration Patterns</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">OData</span><span class="tag">Command</span><span class="tag">This</span><span class="tag">SOAP</span><span class="tag">Message</span><span class="tag">BAPI</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This</span><span class="tag">API</span><span class="tag">API</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 27.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 54%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.01</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 0.6880845409877393%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 10.95</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 21.903440422704936%"></div>
                    </div>
                </div>
            
                <div id="detail-15" class="detail-view">
                    <h3>B2B Integration Factory - Interchange Extraction Flows</h3>
                    <p><strong>Description:</strong> This package includes all additional integration flows for extracting the relevant parameters and key fields from the headers according to the rules of the different type systems.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">B2B Integration Factory</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">This</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 25.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 50%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.01</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 1.1810545027644064%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 10.25</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 20.50590527251382%"></div>
                    </div>
                </div>
            
                <div id="detail-16" class="detail-view">
                    <h3>Deploy Integration Artefact and Get Endpoint</h3>
                    <p><strong>Description:</strong> Deploy an existing integration flow on Cloud Integration runtime. Optionally you can also get the endpoint of the integration flow.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">CICD</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Deploy</span><span class="tag">Cloud</span><span class="tag">Optionally</span><span class="tag">Integration</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 22.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 44%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.09</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 9.138840734980786%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 9.22</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 18.445694203674904%"></div>
                    </div>
                </div>
            
                <div id="detail-17" class="detail-view">
                    <h3>Build custom Rabbit MQ integration adapter</h3>
                    <p><strong>Description:</strong> The rabbitmq: component allows you produce and consume messages from RabbitMQ instances. Using the RabbitMQ AMQP client, this component offers a pure RabbitMQ approach over the generic AMQP component. This integration adapter enables an integration flow to persist or read messages in a RabbitMQ queue.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">Integration Adapters</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">RabbitMQ</span><span class="tag">AMQP</span><span class="tag">Using</span><span class="tag">This</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This integration</span><span class="tag">This</span><span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 21.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 42%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.02</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 1.953841300696195%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 8.85</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 17.70976920650348%"></div>
                    </div>
                </div>
            
                <div id="detail-18" class="detail-view">
                    <h3>Upload Integration Artefact</h3>
                    <p><strong>Description:</strong> Checkout the latest version of the configured integration flow artefact from your source code repository and either update or create the artefact on the Cloud Integration tenant.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">CICD</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Cloud</span><span class="tag">Checkout</span><span class="tag">Integration</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 18.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 36%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.04</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 3.9186402462610417%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 7.81</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 15.619593201231305%"></div>
                    </div>
                </div>
            
                <div id="detail-19" class="detail-view">
                    <h3>Deploy Run Once Integration Artefact and Check MPL</h3>
                    <p><strong>Description:</strong> Deploy an integration flow, check its deployment status and the message processing log status of the message execution that gets automatically triggered after the deployment due to a scheduler configuration or the consumption of files from a (S)FTP server or messages from a JMS queue.</p>
                    <p><strong>Type:</strong> IntegrationFlow</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">CICD</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Deploy</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">Flow</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 18.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 36%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.03</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 2.5870997202320263%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 7.81</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 15.61293549860116%"></div>
                    </div>
                </div>
            
                <div id="detail-20" class="detail-view">
                    <h3>Event Message</h3>
                    <p><strong>Description:</strong> This recipe lets you send a product update using the Event Message pattern.</p>
                    <p><strong>Type:</strong> IntegrationPattern</p>
                    <p><strong>Version:</strong> </p>

                    <h4>Categories</h4>
                    <div>
            <span class="tag">Integration Patterns</span>
                    </div>

                    <h4>Tags</h4>
                    <div>
            <span class="tag">Message</span><span class="tag">Event</span><span class="tag">This</span>
                    </div>

                    <h4>Matching Terms</h4>
                    <div>
            <span class="tag">This</span>
                    </div>

                    <h4>Similarity Scores</h4>

                    <p>Term Match: 15.00</p>
                    <div class="score-bar">
                        <div class="score-fill term-match" style="width: 30%"></div>
                    </div>

                    <p>Endpoint Match: 0.00</p>
                    <div class="score-bar">
                        <div class="score-fill endpoint-match" style="width: 0%"></div>
                    </div>

                    <p>Content Similarity: 0.04</p>
                    <div class="score-bar">
                        <div class="score-fill content-similarity" style="width: 3.940725113366382%"></div>
                    </div>

                    <p>Search Priority: 10.00</p>
                    <div class="score-bar">
                        <div class="score-fill search-priority" style="width: 100%"></div>
                    </div>

                    <p><strong>Combined Score: 6.76</strong></p>
                    <div class="score-bar">
                        <div class="score-fill combined-score" style="width: 13.519703625566832%"></div>
                    </div>
                </div>
            
                <script>
                    function toggleDetail(id) {
                        var element = document.getElementById(id);
                        if (element.style.display === "block") {
                            element.style.display = "none";
                        } else {
                            element.style.display = "block";
                        }
                    }
                </script>
            </div>
        </body>
        </html>
        