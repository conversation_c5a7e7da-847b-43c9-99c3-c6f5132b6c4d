{"7718bfc4-3068-4c9f-8118-9ab49116de70": {"id": "7718bfc4-3068-4c9f-8118-9ab49116de70", "status": "completed", "created": "2025-04-23T16:38:42.273787", "last_updated": "2025-04-23T16:41:05.544145", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\7718bfc4-3068-4c9f-8118-9ab49116de70\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 93, "xml_files": 15, "properties_files": 0, "json_files": 2, "yaml_files": 1, "raml_files": 39, "dwl_files": 17, "other_files": 19}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 11, "subflows": 10, "configs": 2, "error_handlers": 2}, "files": {"markdown": "results\\7718bfc4-3068-4c9f-8118-9ab49116de70\\flow_documentation.md", "html": "results\\7718bfc4-3068-4c9f-8118-9ab49116de70\\flow_documentation_with_mermaid.html", "visualization": "results\\7718bfc4-3068-4c9f-8118-9ab49116de70\\flow_visualization.html"}}, "d1991b83-7173-41fe-a64a-4f0b4cbf9c82": {"id": "d1991b83-7173-41fe-a64a-4f0b4cbf9c82", "status": "completed", "created": "2025-04-23T17:37:30.469827", "last_updated": "2025-04-23T17:38:28.266795", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\d1991b83-7173-41fe-a64a-4f0b4cbf9c82\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\d1991b83-7173-41fe-a64a-4f0b4cbf9c82\\flow_documentation.md", "html": "results\\d1991b83-7173-41fe-a64a-4f0b4cbf9c82\\flow_documentation_with_mermaid.html", "visualization": "results\\d1991b83-7173-41fe-a64a-4f0b4cbf9c82\\flow_visualization.html"}}, "d06d2354-7ead-4884-a462-3fbf23c9138d": {"id": "d06d2354-7ead-4884-a462-3fbf23c9138d", "status": "completed", "created": "2025-04-23T17:38:27.852101", "last_updated": "2025-04-23T17:39:17.511143", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\d06d2354-7ead-4884-a462-3fbf23c9138d\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\d06d2354-7ead-4884-a462-3fbf23c9138d\\flow_documentation.md", "html": "results\\d06d2354-7ead-4884-a462-3fbf23c9138d\\flow_documentation_with_mermaid.html", "visualization": "results\\d06d2354-7ead-4884-a462-3fbf23c9138d\\flow_visualization.html"}}, "a12e05d1-f42b-4b54-8288-5a96cc6716ff": {"id": "a12e05d1-f42b-4b54-8288-5a96cc6716ff", "status": "completed", "created": "2025-04-23T21:03:50.477523", "last_updated": "2025-04-23T21:04:39.051490", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\a12e05d1-f42b-4b54-8288-5a96cc6716ff\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\a12e05d1-f42b-4b54-8288-5a96cc6716ff\\flow_documentation.md", "html": "results\\a12e05d1-f42b-4b54-8288-5a96cc6716ff\\flow_documentation_with_mermaid.html", "visualization": "results\\a12e05d1-f42b-4b54-8288-5a96cc6716ff\\flow_visualization.html"}}, "873013f7-6d72-4ff4-a489-f7f46da6951c": {"id": "873013f7-6d72-4ff4-a489-f7f46da6951c", "status": "completed", "created": "2025-04-23T21:18:43.810603", "last_updated": "2025-04-23T21:19:31.399612", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\873013f7-6d72-4ff4-a489-f7f46da6951c\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\873013f7-6d72-4ff4-a489-f7f46da6951c\\flow_documentation.md", "html": "results\\873013f7-6d72-4ff4-a489-f7f46da6951c\\flow_documentation_with_mermaid.html", "visualization": "results\\873013f7-6d72-4ff4-a489-f7f46da6951c\\flow_visualization.html"}}, "1ae9cddb-a246-4bbd-b97e-0bb94a2ecf4f": {"id": "1ae9cddb-a246-4bbd-b97e-0bb94a2ecf4f", "status": "completed", "created": "2025-04-23T21:39:18.163257", "last_updated": "2025-04-23T21:39:44.900137", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\1ae9cddb-a246-4bbd-b97e-0bb94a2ecf4f\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\1ae9cddb-a246-4bbd-b97e-0bb94a2ecf4f\\flow_documentation.md", "html": "results\\1ae9cddb-a246-4bbd-b97e-0bb94a2ecf4f\\flow_documentation_with_mermaid.html", "visualization": "results\\1ae9cddb-a246-4bbd-b97e-0bb94a2ecf4f\\flow_visualization.html"}}, "d65ca569-c891-4c8b-82ac-e4bd7cc9d4aa": {"id": "d65ca569-c891-4c8b-82ac-e4bd7cc9d4aa", "status": "completed", "created": "2025-04-23T21:41:25.749466", "last_updated": "2025-04-23T21:42:14.580099", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\d65ca569-c891-4c8b-82ac-e4bd7cc9d4aa\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\d65ca569-c891-4c8b-82ac-e4bd7cc9d4aa\\flow_documentation.md", "html": "results\\d65ca569-c891-4c8b-82ac-e4bd7cc9d4aa\\flow_documentation_with_mermaid.html", "visualization": "results\\d65ca569-c891-4c8b-82ac-e4bd7cc9d4aa\\flow_visualization.html"}}, "e5bffed8-ac0b-4174-b078-d6949990e248": {"id": "e5bffed8-ac0b-4174-b078-d6949990e248", "status": "completed", "created": "2025-04-25T13:16:11.848606", "last_updated": "2025-04-25T13:18:50.793508", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\e5bffed8-ac0b-4174-b078-d6949990e248\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 93, "xml_files": 15, "properties_files": 0, "json_files": 2, "yaml_files": 1, "raml_files": 39, "dwl_files": 17, "other_files": 19}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 11, "subflows": 10, "configs": 2, "error_handlers": 2}, "files": {"markdown": "results\\e5bffed8-ac0b-4174-b078-d6949990e248\\flow_documentation.md", "html": "results\\e5bffed8-ac0b-4174-b078-d6949990e248\\flow_documentation_with_mermaid.html", "visualization": "results\\e5bffed8-ac0b-4174-b078-d6949990e248\\flow_visualization.html"}}, "080519d2-49da-46da-9542-76f4b88ad044": {"id": "080519d2-49da-46da-9542-76f4b88ad044", "status": "completed", "created": "2025-04-25T15:00:53.388578", "last_updated": "2025-04-25T15:05:54.337958", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\080519d2-49da-46da-9542-76f4b88ad044\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 93, "xml_files": 15, "properties_files": 0, "json_files": 2, "yaml_files": 1, "raml_files": 39, "dwl_files": 17, "other_files": 19}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 11, "subflows": 10, "configs": 2, "error_handlers": 2}, "files": {"markdown": "results\\080519d2-49da-46da-9542-76f4b88ad044\\flow_documentation.md", "html": "results\\080519d2-49da-46da-9542-76f4b88ad044\\flow_documentation_with_mermaid.html", "visualization": "results\\080519d2-49da-46da-9542-76f4b88ad044\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\080519d2-49da-46da-9542-76f4b88ad044\\iflow_match\\integration_match_report.html", "summary": "results\\080519d2-49da-46da-9542-76f4b88ad044\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 29 potential matches"}}, "a2405a10-f64c-4ead-9762-5ac8ea6d90f3": {"id": "a2405a10-f64c-4ead-9762-5ac8ea6d90f3", "status": "completed", "created": "2025-04-25T15:25:04.415786", "last_updated": "2025-04-25T15:26:08.998722", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\a2405a10-f64c-4ead-9762-5ac8ea6d90f3\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\a2405a10-f64c-4ead-9762-5ac8ea6d90f3\\flow_documentation.md", "html": "results\\a2405a10-f64c-4ead-9762-5ac8ea6d90f3\\flow_documentation_with_mermaid.html", "visualization": "results\\a2405a10-f64c-4ead-9762-5ac8ea6d90f3\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\a2405a10-f64c-4ead-9762-5ac8ea6d90f3\\iflow_match\\integration_match_report.html", "summary": "results\\a2405a10-f64c-4ead-9762-5ac8ea6d90f3\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 32 potential matches"}}, "03104b47-89e5-4d35-bc3d-d5bfbb247960": {"id": "03104b47-89e5-4d35-bc3d-d5bfbb247960", "status": "processing", "created": "2025-04-25T15:30:10.300711", "last_updated": "2025-04-25T15:30:10.783777", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\03104b47-89e5-4d35-bc3d-d5bfbb247960\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "visualization", "processing_message": "Using enhanced documentation generator to include additional file types", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}}, "98cd5815-a863-49a5-813a-bfd97c0fb0ce": {"id": "98cd5815-a863-49a5-813a-bfd97c0fb0ce", "status": "completed", "created": "2025-04-25T15:31:41.384360", "last_updated": "2025-04-25T15:32:27.208994", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\98cd5815-a863-49a5-813a-bfd97c0fb0ce\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\98cd5815-a863-49a5-813a-bfd97c0fb0ce\\flow_documentation.md", "html": "results\\98cd5815-a863-49a5-813a-bfd97c0fb0ce\\flow_documentation_with_mermaid.html", "visualization": "results\\98cd5815-a863-49a5-813a-bfd97c0fb0ce\\flow_visualization.html"}}, "fdcadc7f-fc45-4960-9cf6-203f0cf090ff": {"id": "fdcadc7f-fc45-4960-9cf6-203f0cf090ff", "status": "completed", "created": "2025-04-25T15:35:27.573798", "last_updated": "2025-04-25T15:36:11.968432", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\fdcadc7f-fc45-4960-9cf6-203f0cf090ff\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\fdcadc7f-fc45-4960-9cf6-203f0cf090ff\\flow_documentation.md", "html": "results\\fdcadc7f-fc45-4960-9cf6-203f0cf090ff\\flow_documentation_with_mermaid.html", "visualization": "results\\fdcadc7f-fc45-4960-9cf6-203f0cf090ff\\flow_visualization.html"}}, "b4176e9a-e9e4-48c8-9132-89391fd94ced": {"id": "b4176e9a-e9e4-48c8-9132-89391fd94ced", "status": "completed", "created": "2025-04-25T15:37:01.161346", "last_updated": "2025-04-25T15:37:51.137262", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive\\Documents\\DheepLearningNew\\66_FINAL MULE_TO_IS\\mule_cf_deployment\\app\\uploads\\b4176e9a-e9e4-48c8-9132-89391fd94ced\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\b4176e9a-e9e4-48c8-9132-89391fd94ced\\flow_documentation.md", "html": "results\\b4176e9a-e9e4-48c8-9132-89391fd94ced\\flow_documentation_with_mermaid.html", "visualization": "results\\b4176e9a-e9e4-48c8-9132-89391fd94ced\\flow_visualization.html"}}, "498c95d0-d059-4a09-9a1d-50161e2acd17": {"id": "498c95d0-d059-4a09-9a1d-50161e2acd17", "status": "completed", "created": "2025-05-16T12:05:40.125297", "last_updated": "2025-05-16T12:06:46.082958", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\498c95d0-d059-4a09-9a1d-50161e2acd17\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\498c95d0-d059-4a09-9a1d-50161e2acd17\\flow_documentation.md", "html": "results\\498c95d0-d059-4a09-9a1d-50161e2acd17\\flow_documentation_with_mermaid.html", "visualization": "results\\498c95d0-d059-4a09-9a1d-50161e2acd17\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\498c95d0-d059-4a09-9a1d-50161e2acd17\\iflow_match\\integration_match_report.html", "summary": "results\\498c95d0-d059-4a09-9a1d-50161e2acd17\\iflow_match\\iflow_match_summary.json", "chart_0": "results\\498c95d0-d059-4a09-9a1d-50161e2acd17\\iflow_match\\charts\\top_matches.png", "chart_1": "results\\498c95d0-d059-4a09-9a1d-50161e2acd17\\iflow_match\\charts\\score_breakdown.png", "chart_2": "results\\498c95d0-d059-4a09-9a1d-50161e2acd17\\iflow_match\\charts\\quality_distribution.png"}, "iflow_match_result": {"message": "Found 32 potential matches"}}, "969dd93e-cab6-478b-b9a3-b100a4a2a8bb": {"id": "969dd93e-cab6-478b-b9a3-b100a4a2a8bb", "status": "completed", "created": "2025-05-16T12:09:52.820956", "last_updated": "2025-05-16T12:10:42.879480", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\969dd93e-cab6-478b-b9a3-b100a4a2a8bb\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\969dd93e-cab6-478b-b9a3-b100a4a2a8bb\\flow_documentation.md", "html": "results\\969dd93e-cab6-478b-b9a3-b100a4a2a8bb\\flow_documentation_with_mermaid.html", "visualization": "results\\969dd93e-cab6-478b-b9a3-b100a4a2a8bb\\flow_visualization.html"}}, "e03fdc80-6d4d-4b7c-b9ba-a45b5a802a76": {"id": "e03fdc80-6d4d-4b7c-b9ba-a45b5a802a76", "status": "completed", "created": "2025-05-16T12:28:50.722810", "last_updated": "2025-05-16T12:29:44.356001", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\e03fdc80-6d4d-4b7c-b9ba-a45b5a802a76\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\e03fdc80-6d4d-4b7c-b9ba-a45b5a802a76\\flow_documentation.md", "html": "results\\e03fdc80-6d4d-4b7c-b9ba-a45b5a802a76\\flow_documentation_with_mermaid.html", "visualization": "results\\e03fdc80-6d4d-4b7c-b9ba-a45b5a802a76\\flow_visualization.html"}}, "b2bc7020-7aa1-4830-8486-74538adab205": {"id": "b2bc7020-7aa1-4830-8486-74538adab205", "status": "completed", "created": "2025-05-16T12:32:45.783997", "last_updated": "2025-05-16T12:33:38.003341", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\b2bc7020-7aa1-4830-8486-74538adab205\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\b2bc7020-7aa1-4830-8486-74538adab205\\flow_documentation.md", "html": "results\\b2bc7020-7aa1-4830-8486-74538adab205\\flow_documentation_with_mermaid.html", "visualization": "results\\b2bc7020-7aa1-4830-8486-74538adab205\\flow_visualization.html"}}, "c1b928d9-5e82-491b-9793-5849daea1693": {"id": "c1b928d9-5e82-491b-9793-5849daea1693", "status": "completed", "created": "2025-05-16T12:46:11.012555", "last_updated": "2025-05-16T12:46:55.014056", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\c1b928d9-5e82-491b-9793-5849daea1693\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\c1b928d9-5e82-491b-9793-5849daea1693\\flow_documentation.md", "html": "results\\c1b928d9-5e82-491b-9793-5849daea1693\\flow_documentation_with_mermaid.html", "visualization": "results\\c1b928d9-5e82-491b-9793-5849daea1693\\flow_visualization.html"}}, "8379f09a-c289-4cea-a5d6-58d473d21b0b": {"id": "8379f09a-c289-4cea-a5d6-58d473d21b0b", "status": "completed", "created": "2025-05-16T12:55:38.839319", "last_updated": "2025-05-16T12:56:30.412659", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\8379f09a-c289-4cea-a5d6-58d473d21b0b\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\8379f09a-c289-4cea-a5d6-58d473d21b0b\\flow_documentation.md", "html": "results\\8379f09a-c289-4cea-a5d6-58d473d21b0b\\flow_documentation_with_mermaid.html", "visualization": "results\\8379f09a-c289-4cea-a5d6-58d473d21b0b\\flow_visualization.html"}}, "86635c4c-ed64-40b2-816f-5f978f125476": {"id": "86635c4c-ed64-40b2-816f-5f978f125476", "status": "completed", "created": "2025-05-16T13:01:18.524993", "last_updated": "2025-05-16T13:02:04.803712", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\86635c4c-ed64-40b2-816f-5f978f125476\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\86635c4c-ed64-40b2-816f-5f978f125476\\flow_documentation.md", "html": "results\\86635c4c-ed64-40b2-816f-5f978f125476\\flow_documentation_with_mermaid.html", "visualization": "results\\86635c4c-ed64-40b2-816f-5f978f125476\\flow_visualization.html"}}, "5b13939a-c949-4382-a732-d408f2247251": {"id": "5b13939a-c949-4382-a732-d408f2247251", "status": "completed", "created": "2025-05-16T13:13:55.926348", "last_updated": "2025-05-16T13:14:53.566396", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\5b13939a-c949-4382-a732-d408f2247251\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\5b13939a-c949-4382-a732-d408f2247251\\flow_documentation.md", "html": "results\\5b13939a-c949-4382-a732-d408f2247251\\flow_documentation_with_mermaid.html", "visualization": "results\\5b13939a-c949-4382-a732-d408f2247251\\flow_visualization.html"}}, "d1c07db0-54a4-4fb8-ab74-35c0a8c1f3c1": {"id": "d1c07db0-54a4-4fb8-ab74-35c0a8c1f3c1", "status": "completed", "created": "2025-05-16T13:15:53.326827", "last_updated": "2025-05-16T13:16:39.249648", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\d1c07db0-54a4-4fb8-ab74-35c0a8c1f3c1\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\d1c07db0-54a4-4fb8-ab74-35c0a8c1f3c1\\flow_documentation.md", "html": "results\\d1c07db0-54a4-4fb8-ab74-35c0a8c1f3c1\\flow_documentation_with_mermaid.html", "visualization": "results\\d1c07db0-54a4-4fb8-ab74-35c0a8c1f3c1\\flow_visualization.html"}}, "c253f92b-273a-4c76-8c6a-2056ee01ee38": {"id": "c253f92b-273a-4c76-8c6a-2056ee01ee38", "status": "completed", "created": "2025-05-16T13:28:08.497433", "last_updated": "2025-05-16T13:29:03.213135", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\c253f92b-273a-4c76-8c6a-2056ee01ee38\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\c253f92b-273a-4c76-8c6a-2056ee01ee38\\flow_documentation.md", "html": "results\\c253f92b-273a-4c76-8c6a-2056ee01ee38\\flow_documentation_with_mermaid.html", "visualization": "results\\c253f92b-273a-4c76-8c6a-2056ee01ee38\\flow_visualization.html"}}, "23a82d9d-7af8-4e2c-b20d-19f7f57e249c": {"id": "23a82d9d-7af8-4e2c-b20d-19f7f57e249c", "status": "completed", "created": "2025-05-16T13:39:36.824192", "last_updated": "2025-05-16T13:40:26.148509", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\23a82d9d-7af8-4e2c-b20d-19f7f57e249c\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\23a82d9d-7af8-4e2c-b20d-19f7f57e249c\\flow_documentation.md", "html": "results\\23a82d9d-7af8-4e2c-b20d-19f7f57e249c\\flow_documentation_with_mermaid.html", "visualization": "results\\23a82d9d-7af8-4e2c-b20d-19f7f57e249c\\flow_visualization.html"}}, "b02c4611-bf9d-43cb-bfee-a7014eca1c49": {"id": "b02c4611-bf9d-43cb-bfee-a7014eca1c49", "status": "completed", "created": "2025-05-16T13:53:21.849452", "last_updated": "2025-05-16T13:54:08.655749", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\b02c4611-bf9d-43cb-bfee-a7014eca1c49\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\b02c4611-bf9d-43cb-bfee-a7014eca1c49\\flow_documentation.md", "html": "results\\b02c4611-bf9d-43cb-bfee-a7014eca1c49\\flow_documentation_with_mermaid.html", "visualization": "results\\b02c4611-bf9d-43cb-bfee-a7014eca1c49\\flow_visualization.html"}}, "e1869907-2a18-4f1a-b097-f2d2d7119175": {"id": "e1869907-2a18-4f1a-b097-f2d2d7119175", "status": "completed", "created": "2025-05-16T13:58:40.969225", "last_updated": "2025-05-16T13:59:29.649957", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\e1869907-2a18-4f1a-b097-f2d2d7119175\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\e1869907-2a18-4f1a-b097-f2d2d7119175\\flow_documentation.md", "html": "results\\e1869907-2a18-4f1a-b097-f2d2d7119175\\flow_documentation_with_mermaid.html", "visualization": "results\\e1869907-2a18-4f1a-b097-f2d2d7119175\\flow_visualization.html"}}, "739c1488-cc3e-4ad9-a1cf-fd67e16dd024": {"id": "739c1488-cc3e-4ad9-a1cf-fd67e16dd024", "status": "completed", "created": "2025-05-16T14:02:11.564573", "last_updated": "2025-05-16T14:02:59.742205", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\739c1488-cc3e-4ad9-a1cf-fd67e16dd024\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\739c1488-cc3e-4ad9-a1cf-fd67e16dd024\\flow_documentation.md", "html": "results\\739c1488-cc3e-4ad9-a1cf-fd67e16dd024\\flow_documentation_with_mermaid.html", "visualization": "results\\739c1488-cc3e-4ad9-a1cf-fd67e16dd024\\flow_visualization.html"}}, "d1cb41df-2f5e-43f2-b6e1-df5117944434": {"id": "d1cb41df-2f5e-43f2-b6e1-df5117944434", "status": "completed", "created": "2025-05-16T14:06:23.145105", "last_updated": "2025-05-16T14:17:02.958886", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\d1cb41df-2f5e-43f2-b6e1-df5117944434\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\d1cb41df-2f5e-43f2-b6e1-df5117944434\\flow_documentation.md", "html": "results\\d1cb41df-2f5e-43f2-b6e1-df5117944434\\flow_documentation_with_mermaid.html", "visualization": "results\\d1cb41df-2f5e-43f2-b6e1-df5117944434\\flow_visualization.html"}, "iflow_match_status": "processing", "iflow_match_message": "Extracting terms from markdown and searching for matches..."}, "6e6d19a8-63ae-4f4d-a2ab-9daa3be62aa9": {"id": "6e6d19a8-63ae-4f4d-a2ab-9daa3be62aa9", "status": "completed", "created": "2025-05-16T14:19:01.959412", "last_updated": "2025-05-16T14:20:54.393197", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\6e6d19a8-63ae-4f4d-a2ab-9daa3be62aa9\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\6e6d19a8-63ae-4f4d-a2ab-9daa3be62aa9\\flow_documentation.md", "html": "results\\6e6d19a8-63ae-4f4d-a2ab-9daa3be62aa9\\flow_documentation_with_mermaid.html", "visualization": "results\\6e6d19a8-63ae-4f4d-a2ab-9daa3be62aa9\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\6e6d19a8-63ae-4f4d-a2ab-9daa3be62aa9\\iflow_match\\integration_match_report.html", "summary": "results\\6e6d19a8-63ae-4f4d-a2ab-9daa3be62aa9\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 32 potential matches"}}, "49b67982-8631-48e7-88ba-6437b57a439a": {"id": "49b67982-8631-48e7-88ba-6437b57a439a", "status": "completed", "created": "2025-05-16T16:46:37.049010", "last_updated": "2025-05-16T16:47:28.656178", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\49b67982-8631-48e7-88ba-6437b57a439a\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\49b67982-8631-48e7-88ba-6437b57a439a\\flow_documentation.md", "html": "results\\49b67982-8631-48e7-88ba-6437b57a439a\\flow_documentation_with_mermaid.html", "visualization": "results\\49b67982-8631-48e7-88ba-6437b57a439a\\flow_visualization.html"}}, "8f59e1ef-22fa-4a6e-ac80-2f408f671cc2": {"id": "8f59e1ef-22fa-4a6e-ac80-2f408f671cc2", "status": "completed", "created": "2025-05-16T17:02:56.785812", "last_updated": "2025-05-16T17:04:58.308358", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\8f59e1ef-22fa-4a6e-ac80-2f408f671cc2\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\8f59e1ef-22fa-4a6e-ac80-2f408f671cc2\\flow_documentation.md", "html": "results\\8f59e1ef-22fa-4a6e-ac80-2f408f671cc2\\flow_documentation_with_mermaid.html", "visualization": "results\\8f59e1ef-22fa-4a6e-ac80-2f408f671cc2\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\8f59e1ef-22fa-4a6e-ac80-2f408f671cc2\\iflow_match\\integration_match_report.html", "summary": "results\\8f59e1ef-22fa-4a6e-ac80-2f408f671cc2\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 32 potential matches"}}, "89407a5a-3d1d-4ceb-8a52-c614e34b1288": {"id": "89407a5a-3d1d-4ceb-8a52-c614e34b1288", "status": "completed", "created": "2025-05-16T18:27:43.927892", "last_updated": "2025-05-16T18:29:26.361984", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\89407a5a-3d1d-4ceb-8a52-c614e34b1288\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\89407a5a-3d1d-4ceb-8a52-c614e34b1288\\flow_documentation.md", "html": "results\\89407a5a-3d1d-4ceb-8a52-c614e34b1288\\flow_documentation_with_mermaid.html", "visualization": "results\\89407a5a-3d1d-4ceb-8a52-c614e34b1288\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\89407a5a-3d1d-4ceb-8a52-c614e34b1288\\iflow_match\\integration_match_report.html", "summary": "results\\89407a5a-3d1d-4ceb-8a52-c614e34b1288\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 32 potential matches"}}, "129537c6-22ef-44eb-8165-4cb258c8fa54": {"id": "129537c6-22ef-44eb-8165-4cb258c8fa54", "status": "completed", "created": "2025-05-16T18:43:01.034391", "last_updated": "2025-05-16T19:02:27.086499", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\129537c6-22ef-44eb-8165-4cb258c8fa54\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\129537c6-22ef-44eb-8165-4cb258c8fa54\\flow_documentation.md", "html": "results\\129537c6-22ef-44eb-8165-4cb258c8fa54\\flow_documentation_with_mermaid.html", "visualization": "results\\129537c6-22ef-44eb-8165-4cb258c8fa54\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\129537c6-22ef-44eb-8165-4cb258c8fa54\\iflow_match\\integration_match_report.html", "summary": "results\\129537c6-22ef-44eb-8165-4cb258c8fa54\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 32 potential matches"}}, "f29fea98-71b8-4198-a605-de0ff8ac527b": {"id": "f29fea98-71b8-4198-a605-de0ff8ac527b", "status": "completed", "created": "2025-05-16T19:26:39.221408", "last_updated": "2025-05-16T19:27:28.773057", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\f29fea98-71b8-4198-a605-de0ff8ac527b\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\f29fea98-71b8-4198-a605-de0ff8ac527b\\flow_documentation.md", "html": "results\\f29fea98-71b8-4198-a605-de0ff8ac527b\\flow_documentation_with_mermaid.html", "visualization": "results\\f29fea98-71b8-4198-a605-de0ff8ac527b\\flow_visualization.html"}}, "a214ab54-e992-47d1-928d-5f1dfecaedbd": {"id": "a214ab54-e992-47d1-928d-5f1dfecaedbd", "status": "completed", "created": "2025-05-16T19:30:42.828687", "last_updated": "2025-05-16T19:40:38.312721", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\a214ab54-e992-47d1-928d-5f1dfecaedbd\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\a214ab54-e992-47d1-928d-5f1dfecaedbd\\flow_documentation.md", "html": "results\\a214ab54-e992-47d1-928d-5f1dfecaedbd\\flow_documentation_with_mermaid.html", "visualization": "results\\a214ab54-e992-47d1-928d-5f1dfecaedbd\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\a214ab54-e992-47d1-928d-5f1dfecaedbd\\iflow_match\\integration_match_report.html", "summary": "results\\a214ab54-e992-47d1-928d-5f1dfecaedbd\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 32 potential matches"}}, "0d3fef21-6eac-4ac9-877c-fe6dfd4164e5": {"id": "0d3fef21-6eac-4ac9-877c-fe6dfd4164e5", "status": "completed", "created": "2025-05-16T19:56:40.667882", "last_updated": "2025-05-16T19:58:45.438787", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\0d3fef21-6eac-4ac9-877c-fe6dfd4164e5\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\0d3fef21-6eac-4ac9-877c-fe6dfd4164e5\\flow_documentation.md", "html": "results\\0d3fef21-6eac-4ac9-877c-fe6dfd4164e5\\flow_documentation_with_mermaid.html", "visualization": "results\\0d3fef21-6eac-4ac9-877c-fe6dfd4164e5\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\0d3fef21-6eac-4ac9-877c-fe6dfd4164e5\\iflow_match\\integration_match_report.html", "summary": "results\\0d3fef21-6eac-4ac9-877c-fe6dfd4164e5\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 32 potential matches"}}, "404f9136-56da-4aed-8f76-5791e0fd9274": {"id": "404f9136-56da-4aed-8f76-5791e0fd9274", "status": "completed", "created": "2025-05-16T20:02:36.434941", "last_updated": "2025-05-16T20:03:44.758731", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\404f9136-56da-4aed-8f76-5791e0fd9274\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\404f9136-56da-4aed-8f76-5791e0fd9274\\flow_documentation.md", "html": "results\\404f9136-56da-4aed-8f76-5791e0fd9274\\flow_documentation_with_mermaid.html", "visualization": "results\\404f9136-56da-4aed-8f76-5791e0fd9274\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\404f9136-56da-4aed-8f76-5791e0fd9274\\iflow_match\\integration_match_report.html", "summary": "results\\404f9136-56da-4aed-8f76-5791e0fd9274\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 32 potential matches"}}, "38a30f0c-5908-4b12-876c-ed96064a2577": {"id": "38a30f0c-5908-4b12-876c-ed96064a2577", "status": "completed", "created": "2025-05-16T20:32:30.303483", "last_updated": "2025-05-16T20:33:16.873025", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\38a30f0c-5908-4b12-876c-ed96064a2577\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\38a30f0c-5908-4b12-876c-ed96064a2577\\flow_documentation.md", "html": "results\\38a30f0c-5908-4b12-876c-ed96064a2577\\flow_documentation_with_mermaid.html", "visualization": "results\\38a30f0c-5908-4b12-876c-ed96064a2577\\flow_visualization.html"}}, "07af665a-ee9e-4e1e-beaf-ea2529c53095": {"id": "07af665a-ee9e-4e1e-beaf-ea2529c53095", "status": "completed", "created": "2025-05-16T20:36:55.615956", "last_updated": "2025-05-16T20:38:00.860788", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\07af665a-ee9e-4e1e-beaf-ea2529c53095\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\07af665a-ee9e-4e1e-beaf-ea2529c53095\\flow_documentation.md", "html": "results\\07af665a-ee9e-4e1e-beaf-ea2529c53095\\flow_documentation_with_mermaid.html", "visualization": "results\\07af665a-ee9e-4e1e-beaf-ea2529c53095\\flow_visualization.html"}}, "6d06076a-6b97-4977-9400-fb470b0c7f95": {"id": "6d06076a-6b97-4977-9400-fb470b0c7f95", "status": "completed", "created": "2025-05-16T21:28:23.504988", "last_updated": "2025-05-16T21:29:32.960815", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\6d06076a-6b97-4977-9400-fb470b0c7f95\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\6d06076a-6b97-4977-9400-fb470b0c7f95\\flow_documentation.md", "html": "results\\6d06076a-6b97-4977-9400-fb470b0c7f95\\flow_documentation_with_mermaid.html", "visualization": "results\\6d06076a-6b97-4977-9400-fb470b0c7f95\\flow_visualization.html"}}, "e2bdbb17-e503-4a64-be83-7f7b4c2e5643": {"id": "e2bdbb17-e503-4a64-be83-7f7b4c2e5643", "status": "completed", "created": "2025-05-19T09:40:32.930825", "last_updated": "2025-05-19T09:41:42.986794", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\e2bdbb17-e503-4a64-be83-7f7b4c2e5643\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\e2bdbb17-e503-4a64-be83-7f7b4c2e5643\\flow_documentation.md", "html": "results\\e2bdbb17-e503-4a64-be83-7f7b4c2e5643\\flow_documentation_with_mermaid.html", "visualization": "results\\e2bdbb17-e503-4a64-be83-7f7b4c2e5643\\flow_visualization.html"}}, "21d2868a-25b6-419d-b9a0-e89915214486": {"id": "21d2868a-25b6-419d-b9a0-e89915214486", "status": "completed", "created": "2025-05-19T09:49:51.274815", "last_updated": "2025-05-19T10:02:23.507283", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\21d2868a-25b6-419d-b9a0-e89915214486\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\21d2868a-25b6-419d-b9a0-e89915214486\\flow_documentation.md", "html": "results\\21d2868a-25b6-419d-b9a0-e89915214486\\flow_documentation_with_mermaid.html", "visualization": "results\\21d2868a-25b6-419d-b9a0-e89915214486\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\21d2868a-25b6-419d-b9a0-e89915214486\\iflow_match\\integration_match_report.html", "summary": "results\\21d2868a-25b6-419d-b9a0-e89915214486\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 31 potential matches"}}, "767241d9-5762-43a2-8824-a0e79deb8694": {"id": "767241d9-5762-43a2-8824-a0e79deb8694", "status": "completed", "created": "2025-05-19T11:50:11.610622", "last_updated": "2025-05-19T11:51:48.401327", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\767241d9-5762-43a2-8824-a0e79deb8694\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\767241d9-5762-43a2-8824-a0e79deb8694\\flow_documentation.md", "html": "results\\767241d9-5762-43a2-8824-a0e79deb8694\\flow_documentation_with_mermaid.html", "visualization": "results\\767241d9-5762-43a2-8824-a0e79deb8694\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\767241d9-5762-43a2-8824-a0e79deb8694\\iflow_match\\integration_match_report.html", "summary": "results\\767241d9-5762-43a2-8824-a0e79deb8694\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 22 potential matches"}}, "62b441ed-63fb-4299-bd69-60379a614367": {"id": "62b441ed-63fb-4299-bd69-60379a614367", "status": "processing", "created": "2025-05-19T12:13:06.569321", "last_updated": "2025-05-19T12:13:08.800871", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\62b441ed-63fb-4299-bd69-60379a614367\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "visualization", "processing_message": "Using enhanced documentation generator to include additional file types", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}}, "bb1c5c20-dea7-4fcd-a87a-e04228a0f059": {"id": "bb1c5c20-dea7-4fcd-a87a-e04228a0f059", "status": "completed", "created": "2025-05-19T12:18:53.229080", "last_updated": "2025-05-19T12:20:07.886262", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\bb1c5c20-dea7-4fcd-a87a-e04228a0f059\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\bb1c5c20-dea7-4fcd-a87a-e04228a0f059\\flow_documentation.md", "html": "results\\bb1c5c20-dea7-4fcd-a87a-e04228a0f059\\flow_documentation_with_mermaid.html", "visualization": "results\\bb1c5c20-dea7-4fcd-a87a-e04228a0f059\\flow_visualization.html"}}, "a608dfe9-5eb0-413a-abbf-52a3c9028d67": {"id": "a608dfe9-5eb0-413a-abbf-52a3c9028d67", "status": "completed", "created": "2025-05-19T12:19:13.600157", "last_updated": "2025-05-19T12:20:18.932593", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\a608dfe9-5eb0-413a-abbf-52a3c9028d67\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\a608dfe9-5eb0-413a-abbf-52a3c9028d67\\flow_documentation.md", "html": "results\\a608dfe9-5eb0-413a-abbf-52a3c9028d67\\flow_documentation_with_mermaid.html", "visualization": "results\\a608dfe9-5eb0-413a-abbf-52a3c9028d67\\flow_visualization.html"}}, "89fa2fd5-1231-42e3-b117-b729c6a1a200": {"id": "89fa2fd5-1231-42e3-b117-b729c6a1a200", "status": "completed", "created": "2025-05-19T12:19:47.723202", "last_updated": "2025-05-19T12:20:49.530219", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\89fa2fd5-1231-42e3-b117-b729c6a1a200\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\89fa2fd5-1231-42e3-b117-b729c6a1a200\\flow_documentation.md", "html": "results\\89fa2fd5-1231-42e3-b117-b729c6a1a200\\flow_documentation_with_mermaid.html", "visualization": "results\\89fa2fd5-1231-42e3-b117-b729c6a1a200\\flow_visualization.html"}}, "8e1036d2-d7b3-44ec-a976-39eb9161d060": {"id": "8e1036d2-d7b3-44ec-a976-39eb9161d060", "status": "completed", "created": "2025-05-19T12:23:55.656832", "last_updated": "2025-05-19T12:25:09.717395", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\8e1036d2-d7b3-44ec-a976-39eb9161d060\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\8e1036d2-d7b3-44ec-a976-39eb9161d060\\flow_documentation.md", "html": "results\\8e1036d2-d7b3-44ec-a976-39eb9161d060\\flow_documentation_with_mermaid.html", "visualization": "results\\8e1036d2-d7b3-44ec-a976-39eb9161d060\\flow_visualization.html"}}, "3f98db71-5550-4a18-b570-c04b309e5a0a": {"id": "3f98db71-5550-4a18-b570-c04b309e5a0a", "status": "completed", "created": "2025-05-19T12:24:14.939289", "last_updated": "2025-05-19T12:25:10.181948", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\3f98db71-5550-4a18-b570-c04b309e5a0a\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\3f98db71-5550-4a18-b570-c04b309e5a0a\\flow_documentation.md", "html": "results\\3f98db71-5550-4a18-b570-c04b309e5a0a\\flow_documentation_with_mermaid.html", "visualization": "results\\3f98db71-5550-4a18-b570-c04b309e5a0a\\flow_visualization.html"}}, "a1a5bede-b56a-43ba-8ea6-7c63ab3f3b32": {"id": "a1a5bede-b56a-43ba-8ea6-7c63ab3f3b32", "status": "completed", "created": "2025-05-19T12:31:03.944592", "last_updated": "2025-05-19T12:32:09.481303", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\a1a5bede-b56a-43ba-8ea6-7c63ab3f3b32\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\a1a5bede-b56a-43ba-8ea6-7c63ab3f3b32\\flow_documentation.md", "html": "results\\a1a5bede-b56a-43ba-8ea6-7c63ab3f3b32\\flow_documentation_with_mermaid.html", "visualization": "results\\a1a5bede-b56a-43ba-8ea6-7c63ab3f3b32\\flow_visualization.html"}}, "1e211f6b-5b16-461a-b0ef-daaa4e97ae9a": {"id": "1e211f6b-5b16-461a-b0ef-daaa4e97ae9a", "status": "completed", "created": "2025-05-19T12:42:03.052564", "last_updated": "2025-05-19T12:43:03.073174", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\1e211f6b-5b16-461a-b0ef-daaa4e97ae9a\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\1e211f6b-5b16-461a-b0ef-daaa4e97ae9a\\flow_documentation.md", "html": "results\\1e211f6b-5b16-461a-b0ef-daaa4e97ae9a\\flow_documentation_with_mermaid.html", "visualization": "results\\1e211f6b-5b16-461a-b0ef-daaa4e97ae9a\\flow_visualization.html"}}, "f55b25f2-ff5f-4c35-ba3f-dd4c65b79d71": {"id": "f55b25f2-ff5f-4c35-ba3f-dd4c65b79d71", "status": "completed", "created": "2025-05-19T14:03:06.217369", "last_updated": "2025-05-19T14:09:48.059814", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\f55b25f2-ff5f-4c35-ba3f-dd4c65b79d71\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\f55b25f2-ff5f-4c35-ba3f-dd4c65b79d71\\flow_documentation.md", "html": "results\\f55b25f2-ff5f-4c35-ba3f-dd4c65b79d71\\flow_documentation_with_mermaid.html", "visualization": "results\\f55b25f2-ff5f-4c35-ba3f-dd4c65b79d71\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\f55b25f2-ff5f-4c35-ba3f-dd4c65b79d71\\iflow_match\\integration_match_report.html", "summary": "results\\f55b25f2-ff5f-4c35-ba3f-dd4c65b79d71\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 33 potential matches"}}, "52f83d8c-a230-4cee-b279-bf732bfc4385": {"id": "52f83d8c-a230-4cee-b279-bf732bfc4385", "status": "completed", "created": "2025-05-19T15:15:49.082703", "last_updated": "2025-05-19T15:16:57.976564", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\52f83d8c-a230-4cee-b279-bf732bfc4385\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\52f83d8c-a230-4cee-b279-bf732bfc4385\\flow_documentation.md", "html": "results\\52f83d8c-a230-4cee-b279-bf732bfc4385\\flow_documentation_with_mermaid.html", "visualization": "results\\52f83d8c-a230-4cee-b279-bf732bfc4385\\flow_visualization.html"}}, "7b3e66cf-2d0b-4fa0-b71a-76fe260ab09f": {"id": "7b3e66cf-2d0b-4fa0-b71a-76fe260ab09f", "status": "completed", "created": "2025-05-19T18:45:08.544162", "last_updated": "2025-05-19T18:46:04.710417", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\7b3e66cf-2d0b-4fa0-b71a-76fe260ab09f\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\7b3e66cf-2d0b-4fa0-b71a-76fe260ab09f\\flow_documentation.md", "html": "results\\7b3e66cf-2d0b-4fa0-b71a-76fe260ab09f\\flow_documentation_with_mermaid.html", "visualization": "results\\7b3e66cf-2d0b-4fa0-b71a-76fe260ab09f\\flow_visualization.html"}}, "ab80535f-83dd-4c22-b565-77890e3b73ee": {"id": "ab80535f-83dd-4c22-b565-77890e3b73ee", "status": "completed", "created": "2025-05-19T18:51:45.579610", "last_updated": "2025-05-19T18:52:38.195576", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\ab80535f-83dd-4c22-b565-77890e3b73ee\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\ab80535f-83dd-4c22-b565-77890e3b73ee\\flow_documentation.md", "html": "results\\ab80535f-83dd-4c22-b565-77890e3b73ee\\flow_documentation_with_mermaid.html", "visualization": "results\\ab80535f-83dd-4c22-b565-77890e3b73ee\\flow_visualization.html"}}, "7d7cb19f-b8b5-4c95-9c18-3b6f85d1462c": {"id": "7d7cb19f-b8b5-4c95-9c18-3b6f85d1462c", "status": "completed", "created": "2025-05-19T19:00:51.291805", "last_updated": "2025-05-19T19:02:10.985193", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\7d7cb19f-b8b5-4c95-9c18-3b6f85d1462c\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\7d7cb19f-b8b5-4c95-9c18-3b6f85d1462c\\flow_documentation.md", "html": "results\\7d7cb19f-b8b5-4c95-9c18-3b6f85d1462c\\flow_documentation_with_mermaid.html", "visualization": "results\\7d7cb19f-b8b5-4c95-9c18-3b6f85d1462c\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\7d7cb19f-b8b5-4c95-9c18-3b6f85d1462c\\iflow_match\\integration_match_report.html", "summary": "results\\7d7cb19f-b8b5-4c95-9c18-3b6f85d1462c\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 30 potential matches"}}, "d2b4386e-8008-4dd1-8549-99a63f8d326a": {"id": "d2b4386e-8008-4dd1-8549-99a63f8d326a", "status": "completed", "created": "2025-05-19T19:10:29.776368", "last_updated": "2025-05-19T19:13:16.261119", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\d2b4386e-8008-4dd1-8549-99a63f8d326a\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\d2b4386e-8008-4dd1-8549-99a63f8d326a\\flow_documentation.md", "html": "results\\d2b4386e-8008-4dd1-8549-99a63f8d326a\\flow_documentation_with_mermaid.html", "visualization": "results\\d2b4386e-8008-4dd1-8549-99a63f8d326a\\flow_visualization.html"}, "iflow_match_status": "processing", "iflow_match_message": "Extracting terms from markdown and searching for matches..."}, "cdb88605-5925-4254-be59-4ad3ceba7174": {"id": "cdb88605-5925-4254-be59-4ad3ceba7174", "status": "completed", "created": "2025-05-19T19:15:35.322034", "last_updated": "2025-05-19T19:16:43.207095", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\cdb88605-5925-4254-be59-4ad3ceba7174\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\cdb88605-5925-4254-be59-4ad3ceba7174\\flow_documentation.md", "html": "results\\cdb88605-5925-4254-be59-4ad3ceba7174\\flow_documentation_with_mermaid.html", "visualization": "results\\cdb88605-5925-4254-be59-4ad3ceba7174\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\cdb88605-5925-4254-be59-4ad3ceba7174\\iflow_match\\integration_match_report.html", "summary": "results\\cdb88605-5925-4254-be59-4ad3ceba7174\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 32 potential matches"}}, "cfc9de59-82a4-4380-814d-4b90697ee67c": {"id": "cfc9de59-82a4-4380-814d-4b90697ee67c", "status": "completed", "created": "2025-05-19T19:27:20.229248", "last_updated": "2025-05-19T19:31:43.595060", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\cfc9de59-82a4-4380-814d-4b90697ee67c\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 71, "xml_files": 15, "properties_files": 0, "json_files": 2, "yaml_files": 1, "raml_files": 35, "dwl_files": 17, "other_files": 1}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 11, "subflows": 10, "configs": 2, "error_handlers": 2}, "files": {"markdown": "results\\cfc9de59-82a4-4380-814d-4b90697ee67c\\flow_documentation.md", "html": "results\\cfc9de59-82a4-4380-814d-4b90697ee67c\\flow_documentation_with_mermaid.html", "visualization": "results\\cfc9de59-82a4-4380-814d-4b90697ee67c\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\cfc9de59-82a4-4380-814d-4b90697ee67c\\iflow_match\\integration_match_report.html", "summary": "results\\cfc9de59-82a4-4380-814d-4b90697ee67c\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 28 potential matches"}}, "e0f6ed5e-3272-450c-b2db-c23f2bfda575": {"id": "e0f6ed5e-3272-450c-b2db-c23f2bfda575", "status": "completed", "created": "2025-05-19T20:35:01.438712", "last_updated": "2025-05-19T20:35:56.227909", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\e0f6ed5e-3272-450c-b2db-c23f2bfda575\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 14, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 4}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\e0f6ed5e-3272-450c-b2db-c23f2bfda575\\flow_documentation.md", "html": "results\\e0f6ed5e-3272-450c-b2db-c23f2bfda575\\flow_documentation_with_mermaid.html", "visualization": "results\\e0f6ed5e-3272-450c-b2db-c23f2bfda575\\flow_visualization.html"}}, "86564cda-9e5e-4c0a-b04f-06bb6d99314c": {"id": "86564cda-9e5e-4c0a-b04f-06bb6d99314c", "status": "completed", "created": "2025-05-19T21:05:27.629405", "last_updated": "2025-05-19T21:06:27.795127", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\86564cda-9e5e-4c0a-b04f-06bb6d99314c\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\86564cda-9e5e-4c0a-b04f-06bb6d99314c\\flow_documentation.md", "html": "results\\86564cda-9e5e-4c0a-b04f-06bb6d99314c\\flow_documentation_with_mermaid.html", "visualization": "results\\86564cda-9e5e-4c0a-b04f-06bb6d99314c\\flow_visualization.html"}}, "be99636e-b93e-4e50-a3f8-f953a296b2e8": {"id": "be99636e-b93e-4e50-a3f8-f953a296b2e8", "status": "completed", "created": "2025-05-23T20:14:39.401174", "last_updated": "2025-05-23T20:16:16.649166", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\be99636e-b93e-4e50-a3f8-f953a296b2e8\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\be99636e-b93e-4e50-a3f8-f953a296b2e8\\flow_documentation.md", "html": "results\\be99636e-b93e-4e50-a3f8-f953a296b2e8\\flow_documentation_with_mermaid.html", "visualization": "results\\be99636e-b93e-4e50-a3f8-f953a296b2e8\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\be99636e-b93e-4e50-a3f8-f953a296b2e8\\iflow_match\\integration_match_report.html", "summary": "results\\be99636e-b93e-4e50-a3f8-f953a296b2e8\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 27 potential matches"}}, "588fedd0-2084-42df-afd9-48b576bc82c8": {"id": "588fedd0-2084-42df-afd9-48b576bc82c8", "status": "completed", "created": "2025-05-23T20:45:49.361933", "last_updated": "2025-05-23T20:46:57.585991", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\588fedd0-2084-42df-afd9-48b576bc82c8\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\588fedd0-2084-42df-afd9-48b576bc82c8\\flow_documentation.md", "html": "results\\588fedd0-2084-42df-afd9-48b576bc82c8\\flow_documentation_with_mermaid.html", "visualization": "results\\588fedd0-2084-42df-afd9-48b576bc82c8\\flow_visualization.html"}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\588fedd0-2084-42df-afd9-48b576bc82c8\\iflow_match\\integration_match_report.html", "summary": "results\\588fedd0-2084-42df-afd9-48b576bc82c8\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 31 potential matches"}}, "cf3168dc-4d1a-4e5b-8b07-41931e18e48d": {"id": "cf3168dc-4d1a-4e5b-8b07-41931e18e48d", "status": "completed", "created": "2025-06-12T00:07:26.817665", "last_updated": "2025-06-12T00:08:31.839704", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\cf3168dc-4d1a-4e5b-8b07-41931e18e48d\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\cf3168dc-4d1a-4e5b-8b07-41931e18e48d\\flow_documentation.md", "html": "results\\cf3168dc-4d1a-4e5b-8b07-41931e18e48d\\flow_documentation_with_mermaid.html", "visualization": "results\\cf3168dc-4d1a-4e5b-8b07-41931e18e48d\\flow_visualization.html"}}, "94ab2505-4819-4249-95b1-c7d0ef92cb1d": {"id": "94ab2505-4819-4249-95b1-c7d0ef92cb1d", "status": "completed", "created": "2025-06-12T00:16:35.465041", "last_updated": "2025-06-12T00:17:35.108987", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\94ab2505-4819-4249-95b1-c7d0ef92cb1d\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\94ab2505-4819-4249-95b1-c7d0ef92cb1d\\flow_documentation.md", "html": "results\\94ab2505-4819-4249-95b1-c7d0ef92cb1d\\flow_documentation_with_mermaid.html", "visualization": "results\\94ab2505-4819-4249-95b1-c7d0ef92cb1d\\flow_visualization.html"}}, "9e8a2004-d797-4827-9202-300fdd675152": {"id": "9e8a2004-d797-4827-9202-300fdd675152", "status": "completed", "created": "2025-06-12T00:25:59.089437", "last_updated": "2025-06-12T00:27:08.479832", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\9e8a2004-d797-4827-9202-300fdd675152\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\9e8a2004-d797-4827-9202-300fdd675152\\flow_documentation.md", "html": "results\\9e8a2004-d797-4827-9202-300fdd675152\\flow_documentation_with_mermaid.html", "visualization": "results\\9e8a2004-d797-4827-9202-300fdd675152\\flow_visualization.html"}}, "62d50ae3-0504-4870-978d-9798fe32f35f": {"id": "62d50ae3-0504-4870-978d-9798fe32f35f", "status": "completed", "created": "2025-06-12T00:32:04.296179", "last_updated": "2025-06-12T00:33:06.855751", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\62d50ae3-0504-4870-978d-9798fe32f35f\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\62d50ae3-0504-4870-978d-9798fe32f35f\\flow_documentation.md", "html": "results\\62d50ae3-0504-4870-978d-9798fe32f35f\\flow_documentation_with_mermaid.html", "visualization": "results\\62d50ae3-0504-4870-978d-9798fe32f35f\\flow_visualization.html"}}, "addd7b19-9325-4d31-9859-38f5939caad9": {"id": "addd7b19-9325-4d31-9859-38f5939caad9", "status": "completed", "created": "2025-06-12T16:03:37.498769", "last_updated": "2025-06-12T16:04:39.134459", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\addd7b19-9325-4d31-9859-38f5939caad9\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\addd7b19-9325-4d31-9859-38f5939caad9\\flow_documentation.md", "html": "results\\addd7b19-9325-4d31-9859-38f5939caad9\\flow_documentation_with_mermaid.html", "visualization": "results\\addd7b19-9325-4d31-9859-38f5939caad9\\flow_visualization.html"}}, "f704d5fe-23b0-449f-aeac-b0cbfc8e3e45": {"id": "f704d5fe-23b0-449f-aeac-b0cbfc8e3e45", "status": "completed", "created": "2025-06-12T16:15:34.273651", "last_updated": "2025-06-12T16:16:30.292708", "enhance": true, "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\mule_cf_deployment\\app\\uploads\\f704d5fe-23b0-449f-aeac-b0cbfc8e3e45\\extracted\\combined_mule_resources", "zip_file": true, "file_info": {"total_files": 16, "xml_files": 6, "properties_files": 0, "json_files": 0, "yaml_files": 3, "raml_files": 1, "dwl_files": 0, "other_files": 6}, "processing_step": "completed", "processing_message": "Documentation generation completed successfully!", "parsed_details": {"flows": 3, "subflows": 1, "configs": 3, "error_handlers": 1}, "files": {"markdown": "results\\f704d5fe-23b0-449f-aeac-b0cbfc8e3e45\\flow_documentation.md", "html": "results\\f704d5fe-23b0-449f-aeac-b0cbfc8e3e45\\flow_documentation_with_mermaid.html", "visualization": "results\\f704d5fe-23b0-449f-aeac-b0cbfc8e3e45\\flow_visualization.html"}}, "bbf15239-0a67-49b7-ba9a-3fddadc707bf": {"id": "bbf15239-0a67-49b7-ba9a-3fddadc707bf", "status": "failed", "created": "2025-06-16T17:09:01.144507", "last_updated": "2025-06-16T17:09:01.261573", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\bbf15239-0a67-49b7-ba9a-3fddadc707bf\\extracted", "zip_file": true, "processing_step": "error", "processing_message": "Dell Boomi processing failed: [Errno 2] No such file or directory: '# Dell Boomi Integration Documentation\\n\\n**Generated on:** 2025-06-16 17:09:01\\n\\n## Summary\\n\\n- **Total Files Processed:** 2\\n- **Processes:** 0\\n- **Maps:** 1\\n- **Connectors:** 1\\n\\n## Data Mappings\\n\\n### 1. Subscription Completed JSON to SF Opportunity CREATE Request XML\\n\\n| From | To | Type |\\n|------|----|----- |\\n| 3 | Opportunity/Description | profile |\\n| 3 | Opportunity/Name | profile |\\n| 3 | Opportunity/CloseDate | profile |\\n\\n## Connectors\\n\\n### 1. SF Opportunity CREATE Operation\\n\\n- **Type:** salesforce\\n- **Object:** Opportunity\\n- **Action:** create\\n'", "file_info": {"total_files": 3, "processed_files": 2, "processes": 0, "maps": 1, "connectors": 1, "errors": 0}, "error": "[Errno 2] No such file or directory: '# Dell Boomi Integration Documentation\\n\\n**Generated on:** 2025-06-16 17:09:01\\n\\n## Summary\\n\\n- **Total Files Processed:** 2\\n- **Processes:** 0\\n- **Maps:** 1\\n- **Connectors:** 1\\n\\n## Data Mappings\\n\\n### 1. Subscription Completed JSON to SF Opportunity CREATE Request XML\\n\\n| From | To | Type |\\n|------|----|----- |\\n| 3 | Opportunity/Description | profile |\\n| 3 | Opportunity/Name | profile |\\n| 3 | Opportunity/CloseDate | profile |\\n\\n## Connectors\\n\\n### 1. SF Opportunity CREATE Operation\\n\\n- **Type:** salesforce\\n- **Object:** Opportunity\\n- **Action:** create\\n'"}, "4a81fa23-20d6-4d86-912e-5b513c07065e": {"id": "4a81fa23-20d6-4d86-912e-5b513c07065e", "status": "failed", "created": "2025-06-16T17:24:21.359814", "last_updated": "2025-06-16T17:24:21.520494", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\4a81fa23-20d6-4d86-912e-5b513c07065e", "zip_file": false, "processing_step": "error", "processing_message": "Dell Boomi processing failed: [Errno 2] No such file or directory: '# Dell Boomi Integration Documentation\\n\\n**Generated on:** 2025-06-16 17:24:21\\n\\n## Summary\\n\\n- **Total Files Processed:** 0\\n- **Processes:** 0\\n- **Maps:** 0\\n- **Connectors:** 0\\n'", "file_info": {"total_files": 1, "processed_files": 0, "processes": 0, "maps": 0, "connectors": 0, "errors": 0}, "error": "[Errno 2] No such file or directory: '# Dell Boomi Integration Documentation\\n\\n**Generated on:** 2025-06-16 17:24:21\\n\\n## Summary\\n\\n- **Total Files Processed:** 0\\n- **Processes:** 0\\n- **Maps:** 0\\n- **Connectors:** 0\\n'"}, "17a75684-c239-45e3-879f-f6e8f4a0170c": {"id": "17a75684-c239-45e3-879f-f6e8f4a0170c", "status": "completed", "created": "2025-06-16T17:27:09.653262", "last_updated": "2025-06-16T17:27:10.199104", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\17a75684-c239-45e3-879f-f6e8f4a0170c", "zip_file": false, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully", "file_info": {"total_files": 1, "processed_files": 0, "processes": 0, "maps": 0, "connectors": 0, "errors": 0}, "files": {"markdown": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\17a75684-c239-45e3-879f-f6e8f4a0170c\\boomi_documentation.md", "html": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\17a75684-c239-45e3-879f-f6e8f4a0170c\\boomi_documentation.html"}, "parsed_details": {"total_files": 1, "processed_files": 0, "processes": [], "maps": [], "connectors": [], "errors": []}}, "433397cf-c3d2-4dbd-8658-d82dde9eb83c": {"id": "433397cf-c3d2-4dbd-8658-d82dde9eb83c", "status": "completed", "created": "2025-06-16T17:28:30.273038", "last_updated": "2025-06-16T17:28:30.375752", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\433397cf-c3d2-4dbd-8658-d82dde9eb83c\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully", "file_info": {"total_files": 3, "processed_files": 2, "processes": 0, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\433397cf-c3d2-4dbd-8658-d82dde9eb83c\\boomi_documentation.md", "html": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\433397cf-c3d2-4dbd-8658-d82dde9eb83c\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 2, "processes": [], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\433397cf-c3d2-4dbd-8658-d82dde9eb83c\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\433397cf-c3d2-4dbd-8658-d82dde9eb83c\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "971e2ab7-d8c4-4607-a30e-a1788b422323": {"id": "971e2ab7-d8c4-4607-a30e-a1788b422323", "status": "completed", "created": "2025-06-16T17:35:19.911841", "last_updated": "2025-06-16T17:35:20.026745", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\971e2ab7-d8c4-4607-a30e-a1788b422323\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully", "file_info": {"total_files": 3, "processed_files": 2, "processes": 0, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\971e2ab7-d8c4-4607-a30e-a1788b422323\\boomi_documentation.md", "html": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\971e2ab7-d8c4-4607-a30e-a1788b422323\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 2, "processes": [], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\971e2ab7-d8c4-4607-a30e-a1788b422323\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\971e2ab7-d8c4-4607-a30e-a1788b422323\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "88d00fe3-5e1f-408e-a3a0-7c4399bad5dd": {"id": "88d00fe3-5e1f-408e-a3a0-7c4399bad5dd", "status": "completed", "created": "2025-06-16T17:36:54.499384", "last_updated": "2025-06-16T17:36:54.610558", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\88d00fe3-5e1f-408e-a3a0-7c4399bad5dd\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully", "file_info": {"total_files": 3, "processed_files": 2, "processes": 0, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\88d00fe3-5e1f-408e-a3a0-7c4399bad5dd\\boomi_documentation.md", "html": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\88d00fe3-5e1f-408e-a3a0-7c4399bad5dd\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 2, "processes": [], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\88d00fe3-5e1f-408e-a3a0-7c4399bad5dd\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\88d00fe3-5e1f-408e-a3a0-7c4399bad5dd\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "50a3965f-aa4e-42da-b495-3e81f3a4a23b": {"id": "50a3965f-aa4e-42da-b495-3e81f3a4a23b", "status": "completed", "created": "2025-06-16T17:42:15.111001", "last_updated": "2025-06-16T17:42:15.283814", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\50a3965f-aa4e-42da-b495-3e81f3a4a23b\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully", "file_info": {"total_files": 3, "processed_files": 2, "processes": 0, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\50a3965f-aa4e-42da-b495-3e81f3a4a23b\\boomi_documentation.md", "html": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\50a3965f-aa4e-42da-b495-3e81f3a4a23b\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 2, "processes": [], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\50a3965f-aa4e-42da-b495-3e81f3a4a23b\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\50a3965f-aa4e-42da-b495-3e81f3a4a23b\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "de7b3264-d724-4487-8e8b-26f4e7da6210": {"id": "de7b3264-d724-4487-8e8b-26f4e7da6210", "status": "completed", "created": "2025-06-16T17:45:30.163846", "last_updated": "2025-06-16T17:45:30.282463", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\de7b3264-d724-4487-8e8b-26f4e7da6210\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully", "file_info": {"total_files": 3, "processed_files": 2, "processes": 0, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\de7b3264-d724-4487-8e8b-26f4e7da6210\\boomi_documentation.md", "html": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\de7b3264-d724-4487-8e8b-26f4e7da6210\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 2, "processes": [], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\de7b3264-d724-4487-8e8b-26f4e7da6210\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\de7b3264-d724-4487-8e8b-26f4e7da6210\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "8d0216a2-95bd-4ce3-9961-7488af3cfb0a": {"id": "8d0216a2-95bd-4ce3-9961-7488af3cfb0a", "status": "completed", "created": "2025-06-16T17:49:15.527335", "last_updated": "2025-06-16T17:49:15.674210", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\8d0216a2-95bd-4ce3-9961-7488af3cfb0a\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully", "file_info": {"total_files": 3, "processed_files": 2, "processes": 0, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\8d0216a2-95bd-4ce3-9961-7488af3cfb0a\\boomi_documentation.md", "html": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\8d0216a2-95bd-4ce3-9961-7488af3cfb0a\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 2, "processes": [], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\8d0216a2-95bd-4ce3-9961-7488af3cfb0a\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\8d0216a2-95bd-4ce3-9961-7488af3cfb0a\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "2e62ae3c-8a16-4043-906a-5dc0fea90641": {"id": "2e62ae3c-8a16-4043-906a-5dc0fea90641", "status": "completed", "created": "2025-06-16T17:52:06.179651", "last_updated": "2025-06-16T17:52:06.308387", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\2e62ae3c-8a16-4043-906a-5dc0fea90641\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully", "file_info": {"total_files": 3, "processed_files": 2, "processes": 0, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\2e62ae3c-8a16-4043-906a-5dc0fea90641\\boomi_documentation.md", "html": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\2e62ae3c-8a16-4043-906a-5dc0fea90641\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 2, "processes": [], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\2e62ae3c-8a16-4043-906a-5dc0fea90641\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\2e62ae3c-8a16-4043-906a-5dc0fea90641\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "75dabde0-4da4-4d89-ade3-3a324edd186c": {"id": "75dabde0-4da4-4d89-ade3-3a324edd186c", "status": "completed", "created": "2025-06-16T17:54:25.298856", "last_updated": "2025-06-16T17:54:25.418762", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\75dabde0-4da4-4d89-ade3-3a324edd186c\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully", "file_info": {"total_files": 3, "processed_files": 2, "processes": 0, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\75dabde0-4da4-4d89-ade3-3a324edd186c\\boomi_documentation.md", "html": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\results\\75dabde0-4da4-4d89-ade3-3a324edd186c\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 2, "processes": [], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\75dabde0-4da4-4d89-ade3-3a324edd186c\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\75dabde0-4da4-4d89-ade3-3a324edd186c\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "1ad7109c-1dbc-47d5-a4c8-9a640040f4c3": {"id": "1ad7109c-1dbc-47d5-a4c8-9a640040f4c3", "status": "completed", "created": "2025-06-16T18:01:09.390734", "last_updated": "2025-06-16T18:01:51.219184", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\1ad7109c-1dbc-47d5-a4c8-9a640040f4c3\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\1ad7109c-1dbc-47d5-a4c8-9a640040f4c3\\boomi_documentation.md", "html": "results\\1ad7109c-1dbc-47d5-a4c8-9a640040f4c3\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\1ad7109c-1dbc-47d5-a4c8-9a640040f4c3\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\1ad7109c-1dbc-47d5-a4c8-9a640040f4c3\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\1ad7109c-1dbc-47d5-a4c8-9a640040f4c3\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "24c3490d-850b-4cfc-be18-fb0551d90bae": {"id": "24c3490d-850b-4cfc-be18-fb0551d90bae", "status": "completed", "created": "2025-06-16T18:10:20.495407", "last_updated": "2025-06-16T18:12:54.005902", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\24c3490d-850b-4cfc-be18-fb0551d90bae\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\24c3490d-850b-4cfc-be18-fb0551d90bae\\boomi_documentation.md", "html": "results\\24c3490d-850b-4cfc-be18-fb0551d90bae\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\24c3490d-850b-4cfc-be18-fb0551d90bae\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\24c3490d-850b-4cfc-be18-fb0551d90bae\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\24c3490d-850b-4cfc-be18-fb0551d90bae\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\24c3490d-850b-4cfc-be18-fb0551d90bae\\iflow_match\\integration_match_report.html", "summary": "results\\24c3490d-850b-4cfc-be18-fb0551d90bae\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 21 potential matches"}}, "22730709-d2f0-46f3-be29-150c322ed014": {"id": "22730709-d2f0-46f3-be29-150c322ed014", "status": "completed", "created": "2025-06-16T18:15:51.402713", "last_updated": "2025-06-16T18:16:40.720551", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\22730709-d2f0-46f3-be29-150c322ed014\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\22730709-d2f0-46f3-be29-150c322ed014\\boomi_documentation.md", "html": "results\\22730709-d2f0-46f3-be29-150c322ed014\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\22730709-d2f0-46f3-be29-150c322ed014\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\22730709-d2f0-46f3-be29-150c322ed014\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\22730709-d2f0-46f3-be29-150c322ed014\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "61c48e10-ef09-4195-80a3-ec36ee2c985c": {"id": "61c48e10-ef09-4195-80a3-ec36ee2c985c", "status": "completed", "created": "2025-06-16T18:17:46.882027", "last_updated": "2025-06-16T18:18:29.076653", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\61c48e10-ef09-4195-80a3-ec36ee2c985c\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\61c48e10-ef09-4195-80a3-ec36ee2c985c\\boomi_documentation.md", "html": "results\\61c48e10-ef09-4195-80a3-ec36ee2c985c\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\61c48e10-ef09-4195-80a3-ec36ee2c985c\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\61c48e10-ef09-4195-80a3-ec36ee2c985c\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\61c48e10-ef09-4195-80a3-ec36ee2c985c\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "0c854fed-301b-4254-954b-2531b9a5032f": {"id": "0c854fed-301b-4254-954b-2531b9a5032f", "status": "completed", "created": "2025-06-16T21:59:37.348018", "last_updated": "2025-06-16T22:00:20.929437", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0c854fed-301b-4254-954b-2531b9a5032f\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\0c854fed-301b-4254-954b-2531b9a5032f\\boomi_documentation.md", "html": "results\\0c854fed-301b-4254-954b-2531b9a5032f\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0c854fed-301b-4254-954b-2531b9a5032f\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0c854fed-301b-4254-954b-2531b9a5032f\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0c854fed-301b-4254-954b-2531b9a5032f\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "88b00ce6-a5d2-42f5-9d50-e99ea433df72": {"id": "88b00ce6-a5d2-42f5-9d50-e99ea433df72", "status": "completed", "created": "2025-06-16T22:29:23.914019", "last_updated": "2025-06-16T22:30:09.008475", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\88b00ce6-a5d2-42f5-9d50-e99ea433df72\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\88b00ce6-a5d2-42f5-9d50-e99ea433df72\\boomi_documentation.md", "html": "results\\88b00ce6-a5d2-42f5-9d50-e99ea433df72\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\88b00ce6-a5d2-42f5-9d50-e99ea433df72\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\88b00ce6-a5d2-42f5-9d50-e99ea433df72\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\88b00ce6-a5d2-42f5-9d50-e99ea433df72\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "7e93292c-e9e6-4f1f-8b5c-93bdf491dc7d": {"id": "7e93292c-e9e6-4f1f-8b5c-93bdf491dc7d", "status": "completed", "created": "2025-06-17T11:24:28.812205", "last_updated": "2025-06-17T11:25:13.219895", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\7e93292c-e9e6-4f1f-8b5c-93bdf491dc7d\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\7e93292c-e9e6-4f1f-8b5c-93bdf491dc7d\\boomi_documentation.md", "html": "results\\7e93292c-e9e6-4f1f-8b5c-93bdf491dc7d\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\7e93292c-e9e6-4f1f-8b5c-93bdf491dc7d\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\7e93292c-e9e6-4f1f-8b5c-93bdf491dc7d\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\7e93292c-e9e6-4f1f-8b5c-93bdf491dc7d\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "0f0c6b76-4049-470f-a35c-910e5fb7bb1d": {"id": "0f0c6b76-4049-470f-a35c-910e5fb7bb1d", "status": "completed", "created": "2025-06-17T11:29:16.941661", "last_updated": "2025-06-17T11:30:04.917589", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0f0c6b76-4049-470f-a35c-910e5fb7bb1d\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\0f0c6b76-4049-470f-a35c-910e5fb7bb1d\\boomi_documentation.md", "html": "results\\0f0c6b76-4049-470f-a35c-910e5fb7bb1d\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0f0c6b76-4049-470f-a35c-910e5fb7bb1d\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0f0c6b76-4049-470f-a35c-910e5fb7bb1d\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0f0c6b76-4049-470f-a35c-910e5fb7bb1d\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "0b35c86c-9058-4287-8db8-fd9006780fb7": {"id": "0b35c86c-9058-4287-8db8-fd9006780fb7", "status": "completed", "created": "2025-06-17T11:39:38.240407", "last_updated": "2025-06-17T11:40:48.061772", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0b35c86c-9058-4287-8db8-fd9006780fb7\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\0b35c86c-9058-4287-8db8-fd9006780fb7\\boomi_documentation.md", "html": "results\\0b35c86c-9058-4287-8db8-fd9006780fb7\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0b35c86c-9058-4287-8db8-fd9006780fb7\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0b35c86c-9058-4287-8db8-fd9006780fb7\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\0b35c86c-9058-4287-8db8-fd9006780fb7\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "4be854cb-cd0c-4418-8790-dc9bbed40780": {"id": "4be854cb-cd0c-4418-8790-dc9bbed40780", "status": "completed", "created": "2025-06-17T12:56:46.136414", "last_updated": "2025-06-17T12:57:42.355972", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\4be854cb-cd0c-4418-8790-dc9bbed40780\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\4be854cb-cd0c-4418-8790-dc9bbed40780\\boomi_documentation.md", "html": "results\\4be854cb-cd0c-4418-8790-dc9bbed40780\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\4be854cb-cd0c-4418-8790-dc9bbed40780\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\4be854cb-cd0c-4418-8790-dc9bbed40780\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\4be854cb-cd0c-4418-8790-dc9bbed40780\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "e70f7fc5-36d2-48c6-95dc-375224941100": {"id": "e70f7fc5-36d2-48c6-95dc-375224941100", "status": "completed", "created": "2025-06-17T13:04:05.142012", "last_updated": "2025-06-17T13:04:51.613560", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e70f7fc5-36d2-48c6-95dc-375224941100\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\e70f7fc5-36d2-48c6-95dc-375224941100\\boomi_documentation.md", "html": "results\\e70f7fc5-36d2-48c6-95dc-375224941100\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e70f7fc5-36d2-48c6-95dc-375224941100\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e70f7fc5-36d2-48c6-95dc-375224941100\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e70f7fc5-36d2-48c6-95dc-375224941100\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "063df8a9-e8a3-482b-951f-f4eb9f969567": {"id": "063df8a9-e8a3-482b-951f-f4eb9f969567", "status": "completed", "created": "2025-06-17T13:26:58.192558", "last_updated": "2025-06-17T13:27:54.292616", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\063df8a9-e8a3-482b-951f-f4eb9f969567\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\063df8a9-e8a3-482b-951f-f4eb9f969567\\boomi_documentation.md", "html": "results\\063df8a9-e8a3-482b-951f-f4eb9f969567\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\063df8a9-e8a3-482b-951f-f4eb9f969567\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\063df8a9-e8a3-482b-951f-f4eb9f969567\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\063df8a9-e8a3-482b-951f-f4eb9f969567\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "b3962ca7-202c-44ed-ba0c-0459d653ee35": {"id": "b3962ca7-202c-44ed-ba0c-0459d653ee35", "status": "completed", "created": "2025-06-17T14:51:00.895177", "last_updated": "2025-06-17T14:51:50.325638", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\b3962ca7-202c-44ed-ba0c-0459d653ee35\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\b3962ca7-202c-44ed-ba0c-0459d653ee35\\boomi_documentation.md", "html": "results\\b3962ca7-202c-44ed-ba0c-0459d653ee35\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\b3962ca7-202c-44ed-ba0c-0459d653ee35\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\b3962ca7-202c-44ed-ba0c-0459d653ee35\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\b3962ca7-202c-44ed-ba0c-0459d653ee35\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "2c2206de-6528-4190-9e60-eee5eac045c2": {"id": "2c2206de-6528-4190-9e60-eee5eac045c2", "status": "completed", "created": "2025-06-17T22:06:38.005061", "last_updated": "2025-06-17T22:07:28.384513", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\2c2206de-6528-4190-9e60-eee5eac045c2\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\2c2206de-6528-4190-9e60-eee5eac045c2\\boomi_documentation.md", "html": "results\\2c2206de-6528-4190-9e60-eee5eac045c2\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\2c2206de-6528-4190-9e60-eee5eac045c2\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\2c2206de-6528-4190-9e60-eee5eac045c2\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\2c2206de-6528-4190-9e60-eee5eac045c2\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "5a2a2efa-39e7-46f7-865e-0918295f052a": {"id": "5a2a2efa-39e7-46f7-865e-0918295f052a", "status": "completed", "created": "2025-06-17T22:13:11.131200", "last_updated": "2025-06-17T22:14:00.809530", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\5a2a2efa-39e7-46f7-865e-0918295f052a\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\5a2a2efa-39e7-46f7-865e-0918295f052a\\boomi_documentation.md", "html": "results\\5a2a2efa-39e7-46f7-865e-0918295f052a\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\5a2a2efa-39e7-46f7-865e-0918295f052a\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\5a2a2efa-39e7-46f7-865e-0918295f052a\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\5a2a2efa-39e7-46f7-865e-0918295f052a\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "1bf097b1-6ece-4dbe-985a-2c831e8505c1": {"id": "1bf097b1-6ece-4dbe-985a-2c831e8505c1", "status": "completed", "created": "2025-06-18T16:23:30.437209", "last_updated": "2025-06-18T16:25:47.095739", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\1bf097b1-6ece-4dbe-985a-2c831e8505c1\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 8, "processed_files": 2, "processes": 1, "maps": 1, "connectors": 0, "errors": 0}, "files": {"markdown": "results\\1bf097b1-6ece-4dbe-985a-2c831e8505c1\\boomi_documentation.md", "html": "results\\1bf097b1-6ece-4dbe-985a-2c831e8505c1\\boomi_documentation.html"}, "parsed_details": {"total_files": 8, "processed_files": 2, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\1bf097b1-6ece-4dbe-985a-2c831e8505c1\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Implement Guide SAP SuccessFactors to SFTP.xml", "component": {"id": null, "name": null, "type": null, "version": null, "created_by": null, "created_date": null, "modified_by": null, "modified_date": null, "folder_path": null, "description": "\n        Connect SAP SuccessFactors to SFTP with Error Handling is a robust integration solution within the Boomi platform, focusing on integrating data from SuccessFactors to an SFTP server while incorporating comprehensive error handling. Enhance your business operations by ensuring the accurate and timely transfer of employee data with efficient error notifications.\n    "}, "process": {"allow_simultaneous": null, "enable_user_log": null, "process_log_on_error_only": null, "workload": null, "shapes": [], "connections": []}, "integration_patterns": []}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\1bf097b1-6ece-4dbe-985a-2c831e8505c1\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Kafka Connection.xml", "component": {"id": "3a314f53-92c3-4a55-8dcf-6c3ad766acb4", "name": "Canonical To Kafka Avro", "type": "transform.map", "version": "1", "created_by": "<PERSON><PERSON><EMAIL>", "created_date": "2025-06-17T12:54:55Z", "modified_by": "<PERSON><PERSON><EMAIL>", "modified_date": "2025-06-17T12:54:55Z", "folder_path": "ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08", "description": ""}, "map": {"from_profile": "a0733742-31f8-4c23-a8a4-08382ffb75ce", "to_profile": "d93e0b39-c94d-4947-9b49-5d9939473947", "mappings": [{"from_key": "9", "from_type": "profile", "to_key": "144", "to_name_path": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username", "to_type": "profile"}, {"from_key": "91", "from_type": "profile", "to_key": "160", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID", "to_type": "profile"}, {"from_key": "111", "from_type": "profile", "to_key": "174", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type", "to_type": "profile"}, {"from_key": "118", "from_type": "profile", "to_key": "150", "to_name_path": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name", "to_type": "profile"}]}}], "connectors": [], "errors": []}, "iflow_match_status": "completed", "iflow_match_message": "SAP Integration Suite equivalent search completed successfully!", "iflow_match_files": {"report": "results\\1bf097b1-6ece-4dbe-985a-2c831e8505c1\\iflow_match\\integration_match_report.html", "summary": "results\\1bf097b1-6ece-4dbe-985a-2c831e8505c1\\iflow_match\\iflow_match_summary.json"}, "iflow_match_result": {"message": "Found 25 potential matches"}}, "b677f872-6b70-45ff-9b45-9bb75b75977f": {"id": "b677f872-6b70-45ff-9b45-9bb75b75977f", "status": "completed", "created": "2025-06-18T17:55:50.585616", "last_updated": "2025-06-18T17:56:40.409611", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\b677f872-6b70-45ff-9b45-9bb75b75977f\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 3, "processed_files": 3, "processes": 1, "maps": 1, "connectors": 1, "errors": 0}, "files": {"markdown": "results\\b677f872-6b70-45ff-9b45-9bb75b75977f\\boomi_documentation.md", "html": "results\\b677f872-6b70-45ff-9b45-9bb75b75977f\\boomi_documentation.html"}, "parsed_details": {"total_files": 3, "processed_files": 3, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\b677f872-6b70-45ff-9b45-9bb75b75977f\\extracted\\Flow.xml", "component": {"id": "45b5ab92-a1f2-4a6e-8635-7ddf9b49ac37", "name": "Create Salesforce Opportunities from Stripe Subscriptions", "type": "process", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "process": {"allow_simultaneous": "true", "enable_user_log": "false", "process_log_on_error_only": "false", "workload": "general", "shapes": [{"name": "shape1", "type": "start", "image": "start", "user_label": "", "position": {"x": 48.0, "y": 46.0}, "configuration": {"connector_action": {"action_type": "Listen", "connector_type": "wss", "connection_id": null, "operation_id": "0856f0fa-065f-4cbe-80fd-62c8bc7d60f8"}}, "dragpoints": [{"name": "shape1.dragpoint1", "to_shape": "shape6", "position": {"x": 224.0, "y": 56.0}}]}, {"name": "shape3", "type": "connectoraction", "image": "connectoraction_icon", "user_label": "", "position": {"x": 624.0, "y": 48.0}, "configuration": {"connector_action": {"action_type": "Send", "connector_type": "salesforce", "connection_id": "36fc72b2-5fa9-4c74-99df-5434918a140e", "operation_id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19"}}, "dragpoints": [{"name": "shape3.dragpoint1", "to_shape": "shape5", "position": {"x": 800.0, "y": 56.0}}]}, {"name": "shape4", "type": "map", "image": "map_icon", "user_label": "", "position": {"x": 432.0, "y": 48.0}, "configuration": {"map": {"map_id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268"}}, "dragpoints": [{"name": "shape4.dragpoint1", "to_shape": "shape3", "position": {"x": 608.0, "y": 56.0}}]}, {"name": "shape5", "type": "stop", "image": "stop_icon", "user_label": "", "position": {"x": 816.0, "y": 48.0}, "configuration": {}, "dragpoints": []}, {"name": "shape6", "type": "documentproperties", "image": "documentproperties_icon", "user_label": "Set Dynamic Properties", "position": {"x": 240.0, "y": 48.0}, "configuration": {"document_properties": [{"name": "Dynamic Document Property - DDP_CustomerName", "property_id": "dynamicdocument.DDP_CustomerName", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_Subscription", "property_id": "dynamicdocument.DDP_Subscription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_SalesforceDescription", "property_id": "dynamicdocument.DDP_SalesforceDescription", "default_value": "", "persist": false, "is_dynamic_credential": false}, {"name": "Dynamic Document Property - DDP_CloseDate", "property_id": "dynamicdocument.DDP_CloseDate", "default_value": "", "persist": false, "is_dynamic_credential": false}]}, "dragpoints": [{"name": "shape6.dragpoint1", "to_shape": "shape4", "position": {"x": 416.0, "y": 56.0}}]}], "connections": [{"from_shape": "shape1", "to_shape": "shape6", "from_position": {"x": 224.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape3", "to_shape": "shape5", "from_position": {"x": 800.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape4", "to_shape": "shape3", "from_position": {"x": 608.0, "y": 56.0}, "connection_type": "flow"}, {"from_shape": "shape6", "to_shape": "shape4", "from_position": {"x": 416.0, "y": 56.0}, "connection_type": "flow"}]}, "integration_patterns": ["event_listener", "data_sender", "dynamic_properties"]}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\b677f872-6b70-45ff-9b45-9bb75b75977f\\extracted\\comp1.xml", "component": {"id": "cbb5a23f-5904-4fb9-a7dc-9e7062d2b268", "name": "Subscription Completed JSON to SF Opportunity CREATE Request XML", "type": "transform.map", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:10Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:10Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "map": {"from_profile": "6e791dfd-6dad-469a-9bd7-5339e240cca2", "to_profile": "efd69737-e49c-4a38-bb32-abb39383ed5f", "mappings": [{"from_key": "3", "from_type": "function", "to_key": "5", "to_name_path": "Opportunity/Description", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "4", "to_name_path": "Opportunity/Name", "to_type": "profile"}, {"from_key": "3", "from_type": "function", "to_key": "10", "to_name_path": "Opportunity/CloseDate", "to_type": "profile"}]}}], "connectors": [{"type": "connector", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\b677f872-6b70-45ff-9b45-9bb75b75977f\\extracted\\comp2.xml", "component": {"id": "22d84e3e-6a7f-4c9d-9757-67177bc97a19", "name": "SF Opportunity CREATE Operation", "type": "connector-action", "version": "1", "created_by": "<EMAIL>", "created_date": "2025-06-05T11:22:11Z", "modified_by": "<EMAIL>", "modified_date": "2025-06-05T11:22:11Z", "folder_path": "IT Resonance/Create Salesforce Opportunities from Stripe Subscriptions_2025-06-05-11:22:02", "description": "For more details, see here: https://community.boomi.com/s/article/Create-Salesforce-Opportunities-from-Stripe-Subscriptions"}, "connector": {"type": "salesforce", "object_action": "create", "object_name": "Opportunity", "batch_size": "10", "use_bulk_api": false}}], "errors": []}}, "e6d0c653-b74d-42fb-8b59-89395fbbae87": {"id": "e6d0c653-b74d-42fb-8b59-89395fbbae87", "status": "completed", "created": "2025-06-19T15:14:24.521696", "last_updated": "2025-06-19T15:15:29.883942", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e6d0c653-b74d-42fb-8b59-89395fbbae87\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 8, "processed_files": 2, "processes": 1, "maps": 1, "connectors": 0, "errors": 0}, "files": {"markdown": "results\\e6d0c653-b74d-42fb-8b59-89395fbbae87\\boomi_documentation.md", "html": "results\\e6d0c653-b74d-42fb-8b59-89395fbbae87\\boomi_documentation.html"}, "parsed_details": {"total_files": 8, "processed_files": 2, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e6d0c653-b74d-42fb-8b59-89395fbbae87\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Implement Guide SAP SuccessFactors to SFTP.xml", "component": {"id": null, "name": null, "type": null, "version": null, "created_by": null, "created_date": null, "modified_by": null, "modified_date": null, "folder_path": null, "description": "\n        Connect SAP SuccessFactors to SFTP with Error Handling is a robust integration solution within the Boomi platform, focusing on integrating data from SuccessFactors to an SFTP server while incorporating comprehensive error handling. Enhance your business operations by ensuring the accurate and timely transfer of employee data with efficient error notifications.\n    "}, "process": {"allow_simultaneous": null, "enable_user_log": null, "process_log_on_error_only": null, "workload": null, "shapes": [], "connections": []}, "integration_patterns": []}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e6d0c653-b74d-42fb-8b59-89395fbbae87\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Kafka Connection.xml", "component": {"id": "3a314f53-92c3-4a55-8dcf-6c3ad766acb4", "name": "Canonical To Kafka Avro", "type": "transform.map", "version": "1", "created_by": "<PERSON><PERSON><EMAIL>", "created_date": "2025-06-17T12:54:55Z", "modified_by": "<PERSON><PERSON><EMAIL>", "modified_date": "2025-06-17T12:54:55Z", "folder_path": "ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08", "description": ""}, "map": {"from_profile": "a0733742-31f8-4c23-a8a4-08382ffb75ce", "to_profile": "d93e0b39-c94d-4947-9b49-5d9939473947", "mappings": [{"from_key": "9", "from_type": "profile", "to_key": "144", "to_name_path": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username", "to_type": "profile"}, {"from_key": "91", "from_type": "profile", "to_key": "160", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID", "to_type": "profile"}, {"from_key": "111", "from_type": "profile", "to_key": "174", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type", "to_type": "profile"}, {"from_key": "118", "from_type": "profile", "to_key": "150", "to_name_path": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name", "to_type": "profile"}]}}], "connectors": [], "errors": []}}, "a6f5fc38-a951-46f7-8131-44318bedcd93": {"id": "a6f5fc38-a951-46f7-8131-44318bedcd93", "status": "failed", "created": "2025-06-19T19:07:00.229462", "last_updated": "2025-06-19T19:07:02.727420", "enhance": true, "platform": "mulesoft", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\a6f5fc38-a951-46f7-8131-44318bedcd93\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56", "zip_file": true, "file_info": {"total_files": 10, "xml_files": 8, "properties_files": 0, "json_files": 0, "yaml_files": 1, "raml_files": 0, "dwl_files": 0, "other_files": 1}, "processing_step": "mule_parsing", "processing_message": "MuleSoft parsing complete, generating documentation...", "parsed_details": {"flows": 0, "subflows": 0, "configs": 0, "error_handlers": 0}, "error": "No valid MuleSoft components found in the uploaded files"}, "c5e48c6e-742f-4fe8-9ba3-1f2062ed754c": {"id": "c5e48c6e-742f-4fe8-9ba3-1f2062ed754c", "status": "completed", "created": "2025-06-19T19:07:49.945099", "last_updated": "2025-06-19T19:08:53.174715", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\c5e48c6e-742f-4fe8-9ba3-1f2062ed754c\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 8, "processed_files": 2, "processes": 1, "maps": 1, "connectors": 0, "errors": 0}, "files": {"markdown": "results\\c5e48c6e-742f-4fe8-9ba3-1f2062ed754c\\boomi_documentation.md", "html": "results\\c5e48c6e-742f-4fe8-9ba3-1f2062ed754c\\boomi_documentation.html"}, "parsed_details": {"total_files": 8, "processed_files": 2, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\c5e48c6e-742f-4fe8-9ba3-1f2062ed754c\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Implement Guide SAP SuccessFactors to SFTP.xml", "component": {"id": null, "name": null, "type": null, "version": null, "created_by": null, "created_date": null, "modified_by": null, "modified_date": null, "folder_path": null, "description": "\n        Connect SAP SuccessFactors to SFTP with Error Handling is a robust integration solution within the Boomi platform, focusing on integrating data from SuccessFactors to an SFTP server while incorporating comprehensive error handling. Enhance your business operations by ensuring the accurate and timely transfer of employee data with efficient error notifications.\n    "}, "process": {"allow_simultaneous": null, "enable_user_log": null, "process_log_on_error_only": null, "workload": null, "shapes": [], "connections": []}, "integration_patterns": []}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\c5e48c6e-742f-4fe8-9ba3-1f2062ed754c\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Kafka Connection.xml", "component": {"id": "3a314f53-92c3-4a55-8dcf-6c3ad766acb4", "name": "Canonical To Kafka Avro", "type": "transform.map", "version": "1", "created_by": "<PERSON><PERSON><EMAIL>", "created_date": "2025-06-17T12:54:55Z", "modified_by": "<PERSON><PERSON><EMAIL>", "modified_date": "2025-06-17T12:54:55Z", "folder_path": "ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08", "description": ""}, "map": {"from_profile": "a0733742-31f8-4c23-a8a4-08382ffb75ce", "to_profile": "d93e0b39-c94d-4947-9b49-5d9939473947", "mappings": [{"from_key": "9", "from_type": "profile", "to_key": "144", "to_name_path": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username", "to_type": "profile"}, {"from_key": "91", "from_type": "profile", "to_key": "160", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID", "to_type": "profile"}, {"from_key": "111", "from_type": "profile", "to_key": "174", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type", "to_type": "profile"}, {"from_key": "118", "from_type": "profile", "to_key": "150", "to_name_path": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name", "to_type": "profile"}]}}], "connectors": [], "errors": []}}, "e27846d6-8895-4482-bf21-1e94e50dfce1": {"id": "e27846d6-8895-4482-bf21-1e94e50dfce1", "status": "completed", "created": "2025-06-19T19:31:44.608876", "last_updated": "2025-06-19T19:32:35.879553", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e27846d6-8895-4482-bf21-1e94e50dfce1\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 8, "processed_files": 2, "processes": 1, "maps": 1, "connectors": 0, "errors": 0}, "files": {"markdown": "results\\e27846d6-8895-4482-bf21-1e94e50dfce1\\boomi_documentation.md", "html": "results\\e27846d6-8895-4482-bf21-1e94e50dfce1\\boomi_documentation.html"}, "parsed_details": {"total_files": 8, "processed_files": 2, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e27846d6-8895-4482-bf21-1e94e50dfce1\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Implement Guide SAP SuccessFactors to SFTP.xml", "component": {"id": null, "name": null, "type": null, "version": null, "created_by": null, "created_date": null, "modified_by": null, "modified_date": null, "folder_path": null, "description": "\n        Connect SAP SuccessFactors to SFTP with Error Handling is a robust integration solution within the Boomi platform, focusing on integrating data from SuccessFactors to an SFTP server while incorporating comprehensive error handling. Enhance your business operations by ensuring the accurate and timely transfer of employee data with efficient error notifications.\n    "}, "process": {"allow_simultaneous": null, "enable_user_log": null, "process_log_on_error_only": null, "workload": null, "shapes": [], "connections": []}, "integration_patterns": []}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e27846d6-8895-4482-bf21-1e94e50dfce1\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Kafka Connection.xml", "component": {"id": "3a314f53-92c3-4a55-8dcf-6c3ad766acb4", "name": "Canonical To Kafka Avro", "type": "transform.map", "version": "1", "created_by": "<PERSON><PERSON><EMAIL>", "created_date": "2025-06-17T12:54:55Z", "modified_by": "<PERSON><PERSON><EMAIL>", "modified_date": "2025-06-17T12:54:55Z", "folder_path": "ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08", "description": ""}, "map": {"from_profile": "a0733742-31f8-4c23-a8a4-08382ffb75ce", "to_profile": "d93e0b39-c94d-4947-9b49-5d9939473947", "mappings": [{"from_key": "9", "from_type": "profile", "to_key": "144", "to_name_path": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username", "to_type": "profile"}, {"from_key": "91", "from_type": "profile", "to_key": "160", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID", "to_type": "profile"}, {"from_key": "111", "from_type": "profile", "to_key": "174", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type", "to_type": "profile"}, {"from_key": "118", "from_type": "profile", "to_key": "150", "to_name_path": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name", "to_type": "profile"}]}}], "connectors": [], "errors": []}}, "a31d4192-228b-464e-8a01-8ec775842cff": {"id": "a31d4192-228b-464e-8a01-8ec775842cff", "status": "completed", "created": "2025-06-19T19:47:19.205802", "last_updated": "2025-06-19T19:48:04.370072", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\a31d4192-228b-464e-8a01-8ec775842cff\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 8, "processed_files": 2, "processes": 1, "maps": 1, "connectors": 0, "errors": 0}, "files": {"markdown": "results\\a31d4192-228b-464e-8a01-8ec775842cff\\boomi_documentation.md", "html": "results\\a31d4192-228b-464e-8a01-8ec775842cff\\boomi_documentation.html"}, "parsed_details": {"total_files": 8, "processed_files": 2, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\a31d4192-228b-464e-8a01-8ec775842cff\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Implement Guide SAP SuccessFactors to SFTP.xml", "component": {"id": null, "name": null, "type": null, "version": null, "created_by": null, "created_date": null, "modified_by": null, "modified_date": null, "folder_path": null, "description": "\n        Connect SAP SuccessFactors to SFTP with Error Handling is a robust integration solution within the Boomi platform, focusing on integrating data from SuccessFactors to an SFTP server while incorporating comprehensive error handling. Enhance your business operations by ensuring the accurate and timely transfer of employee data with efficient error notifications.\n    "}, "process": {"allow_simultaneous": null, "enable_user_log": null, "process_log_on_error_only": null, "workload": null, "shapes": [], "connections": []}, "integration_patterns": []}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\a31d4192-228b-464e-8a01-8ec775842cff\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Kafka Connection.xml", "component": {"id": "3a314f53-92c3-4a55-8dcf-6c3ad766acb4", "name": "Canonical To Kafka Avro", "type": "transform.map", "version": "1", "created_by": "<PERSON><PERSON><EMAIL>", "created_date": "2025-06-17T12:54:55Z", "modified_by": "<PERSON><PERSON><EMAIL>", "modified_date": "2025-06-17T12:54:55Z", "folder_path": "ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08", "description": ""}, "map": {"from_profile": "a0733742-31f8-4c23-a8a4-08382ffb75ce", "to_profile": "d93e0b39-c94d-4947-9b49-5d9939473947", "mappings": [{"from_key": "9", "from_type": "profile", "to_key": "144", "to_name_path": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username", "to_type": "profile"}, {"from_key": "91", "from_type": "profile", "to_key": "160", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID", "to_type": "profile"}, {"from_key": "111", "from_type": "profile", "to_key": "174", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type", "to_type": "profile"}, {"from_key": "118", "from_type": "profile", "to_key": "150", "to_name_path": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name", "to_type": "profile"}]}}], "connectors": [], "errors": []}}, "cda4bbf1-c146-4665-84c4-1c99b7c46f5d": {"id": "cda4bbf1-c146-4665-84c4-1c99b7c46f5d", "status": "completed", "created": "2025-06-23T18:07:23.962010", "last_updated": "2025-06-23T18:08:16.355630", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\cda4bbf1-c146-4665-84c4-1c99b7c46f5d\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 8, "processed_files": 2, "processes": 1, "maps": 1, "connectors": 0, "errors": 0}, "files": {"markdown": "results\\cda4bbf1-c146-4665-84c4-1c99b7c46f5d\\boomi_documentation.md", "html": "results\\cda4bbf1-c146-4665-84c4-1c99b7c46f5d\\boomi_documentation.html"}, "parsed_details": {"total_files": 8, "processed_files": 2, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\cda4bbf1-c146-4665-84c4-1c99b7c46f5d\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Implement Guide SAP SuccessFactors to SFTP.xml", "component": {"id": null, "name": null, "type": null, "version": null, "created_by": null, "created_date": null, "modified_by": null, "modified_date": null, "folder_path": null, "description": "\n        Connect SAP SuccessFactors to SFTP with Error Handling is a robust integration solution within the Boomi platform, focusing on integrating data from SuccessFactors to an SFTP server while incorporating comprehensive error handling. Enhance your business operations by ensuring the accurate and timely transfer of employee data with efficient error notifications.\n    "}, "process": {"allow_simultaneous": null, "enable_user_log": null, "process_log_on_error_only": null, "workload": null, "shapes": [], "connections": []}, "integration_patterns": []}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\cda4bbf1-c146-4665-84c4-1c99b7c46f5d\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Kafka Connection.xml", "component": {"id": "3a314f53-92c3-4a55-8dcf-6c3ad766acb4", "name": "Canonical To Kafka Avro", "type": "transform.map", "version": "1", "created_by": "<PERSON><PERSON><EMAIL>", "created_date": "2025-06-17T12:54:55Z", "modified_by": "<PERSON><PERSON><EMAIL>", "modified_date": "2025-06-17T12:54:55Z", "folder_path": "ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08", "description": ""}, "map": {"from_profile": "a0733742-31f8-4c23-a8a4-08382ffb75ce", "to_profile": "d93e0b39-c94d-4947-9b49-5d9939473947", "mappings": [{"from_key": "9", "from_type": "profile", "to_key": "144", "to_name_path": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username", "to_type": "profile"}, {"from_key": "91", "from_type": "profile", "to_key": "160", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID", "to_type": "profile"}, {"from_key": "111", "from_type": "profile", "to_key": "174", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type", "to_type": "profile"}, {"from_key": "118", "from_type": "profile", "to_key": "150", "to_name_path": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name", "to_type": "profile"}]}}], "connectors": [], "errors": []}}, "959bb6b9-156e-44d4-b755-82543ff456e8": {"id": "959bb6b9-156e-44d4-b755-82543ff456e8", "status": "completed", "created": "2025-06-23T18:48:41.497928", "last_updated": "2025-06-23T18:49:43.255330", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\959bb6b9-156e-44d4-b755-82543ff456e8\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 8, "processed_files": 2, "processes": 1, "maps": 1, "connectors": 0, "errors": 0}, "files": {"markdown": "results\\959bb6b9-156e-44d4-b755-82543ff456e8\\boomi_documentation.md", "html": "results\\959bb6b9-156e-44d4-b755-82543ff456e8\\boomi_documentation.html"}, "parsed_details": {"total_files": 8, "processed_files": 2, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\959bb6b9-156e-44d4-b755-82543ff456e8\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Implement Guide SAP SuccessFactors to SFTP.xml", "component": {"id": null, "name": null, "type": null, "version": null, "created_by": null, "created_date": null, "modified_by": null, "modified_date": null, "folder_path": null, "description": "\n        Connect SAP SuccessFactors to SFTP with Error Handling is a robust integration solution within the Boomi platform, focusing on integrating data from SuccessFactors to an SFTP server while incorporating comprehensive error handling. Enhance your business operations by ensuring the accurate and timely transfer of employee data with efficient error notifications.\n    "}, "process": {"allow_simultaneous": null, "enable_user_log": null, "process_log_on_error_only": null, "workload": null, "shapes": [], "connections": []}, "integration_patterns": []}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\959bb6b9-156e-44d4-b755-82543ff456e8\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Kafka Connection.xml", "component": {"id": "3a314f53-92c3-4a55-8dcf-6c3ad766acb4", "name": "Canonical To Kafka Avro", "type": "transform.map", "version": "1", "created_by": "<PERSON><PERSON><EMAIL>", "created_date": "2025-06-17T12:54:55Z", "modified_by": "<PERSON><PERSON><EMAIL>", "modified_date": "2025-06-17T12:54:55Z", "folder_path": "ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08", "description": ""}, "map": {"from_profile": "a0733742-31f8-4c23-a8a4-08382ffb75ce", "to_profile": "d93e0b39-c94d-4947-9b49-5d9939473947", "mappings": [{"from_key": "9", "from_type": "profile", "to_key": "144", "to_name_path": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username", "to_type": "profile"}, {"from_key": "91", "from_type": "profile", "to_key": "160", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID", "to_type": "profile"}, {"from_key": "111", "from_type": "profile", "to_key": "174", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type", "to_type": "profile"}, {"from_key": "118", "from_type": "profile", "to_key": "150", "to_name_path": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name", "to_type": "profile"}]}}], "connectors": [], "errors": []}}, "e6dc6ab7-45fa-4c9d-ba9d-4c5e3adfcd14": {"id": "e6dc6ab7-45fa-4c9d-ba9d-4c5e3adfcd14", "status": "completed", "created": "2025-06-23T20:56:22.499250", "last_updated": "2025-06-23T20:57:21.944611", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e6dc6ab7-45fa-4c9d-ba9d-4c5e3adfcd14\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 8, "processed_files": 2, "processes": 1, "maps": 1, "connectors": 0, "errors": 0}, "files": {"markdown": "results\\e6dc6ab7-45fa-4c9d-ba9d-4c5e3adfcd14\\boomi_documentation.md", "html": "results\\e6dc6ab7-45fa-4c9d-ba9d-4c5e3adfcd14\\boomi_documentation.html"}, "parsed_details": {"total_files": 8, "processed_files": 2, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e6dc6ab7-45fa-4c9d-ba9d-4c5e3adfcd14\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Implement Guide SAP SuccessFactors to SFTP.xml", "component": {"id": null, "name": null, "type": null, "version": null, "created_by": null, "created_date": null, "modified_by": null, "modified_date": null, "folder_path": null, "description": "\n        Connect SAP SuccessFactors to SFTP with Error Handling is a robust integration solution within the Boomi platform, focusing on integrating data from SuccessFactors to an SFTP server while incorporating comprehensive error handling. Enhance your business operations by ensuring the accurate and timely transfer of employee data with efficient error notifications.\n    "}, "process": {"allow_simultaneous": null, "enable_user_log": null, "process_log_on_error_only": null, "workload": null, "shapes": [], "connections": []}, "integration_patterns": []}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\e6dc6ab7-45fa-4c9d-ba9d-4c5e3adfcd14\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Kafka Connection.xml", "component": {"id": "3a314f53-92c3-4a55-8dcf-6c3ad766acb4", "name": "Canonical To Kafka Avro", "type": "transform.map", "version": "1", "created_by": "<PERSON><PERSON><EMAIL>", "created_date": "2025-06-17T12:54:55Z", "modified_by": "<PERSON><PERSON><EMAIL>", "modified_date": "2025-06-17T12:54:55Z", "folder_path": "ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08", "description": ""}, "map": {"from_profile": "a0733742-31f8-4c23-a8a4-08382ffb75ce", "to_profile": "d93e0b39-c94d-4947-9b49-5d9939473947", "mappings": [{"from_key": "9", "from_type": "profile", "to_key": "144", "to_name_path": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username", "to_type": "profile"}, {"from_key": "91", "from_type": "profile", "to_key": "160", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID", "to_type": "profile"}, {"from_key": "111", "from_type": "profile", "to_key": "174", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type", "to_type": "profile"}, {"from_key": "118", "from_type": "profile", "to_key": "150", "to_name_path": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name", "to_type": "profile"}]}}], "connectors": [], "errors": []}}, "63cee775-a0d9-4fe2-aa45-e4fbfa47faac": {"id": "63cee775-a0d9-4fe2-aa45-e4fbfa47faac", "status": "completed", "created": "2025-06-23T21:12:57.196574", "last_updated": "2025-06-23T21:13:50.306787", "enhance": true, "platform": "boomi", "input_directory": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\63cee775-a0d9-4fe2-aa45-e4fbfa47faac\\extracted", "zip_file": true, "processing_step": "completed", "processing_message": "Dell Boomi documentation generation completed successfully with AI enhancement", "file_info": {"total_files": 8, "processed_files": 2, "processes": 1, "maps": 1, "connectors": 0, "errors": 0}, "files": {"markdown": "results\\63cee775-a0d9-4fe2-aa45-e4fbfa47faac\\boomi_documentation.md", "html": "results\\63cee775-a0d9-4fe2-aa45-e4fbfa47faac\\boomi_documentation.html"}, "parsed_details": {"total_files": 8, "processed_files": 2, "processes": [{"type": "process", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\63cee775-a0d9-4fe2-aa45-e4fbfa47faac\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Implement Guide SAP SuccessFactors to SFTP.xml", "component": {"id": null, "name": null, "type": null, "version": null, "created_by": null, "created_date": null, "modified_by": null, "modified_date": null, "folder_path": null, "description": "\n        Connect SAP SuccessFactors to SFTP with Error Handling is a robust integration solution within the Boomi platform, focusing on integrating data from SuccessFactors to an SFTP server while incorporating comprehensive error handling. Enhance your business operations by ensuring the accurate and timely transfer of employee data with efficient error notifications.\n    "}, "process": {"allow_simultaneous": null, "enable_user_log": null, "process_log_on_error_only": null, "workload": null, "shapes": [], "connections": []}, "integration_patterns": []}], "maps": [{"type": "map", "file_path": "C:\\Users\\<USER>\\OneDrive - IT Resonance\\Documents\\DheepLearningITR\\mule_cf_deployment\\app\\uploads\\63cee775-a0d9-4fe2-aa45-e4fbfa47faac\\extracted\\agent_11824139-988f-46c8-84db-01a083aa2c56\\Kafka Connection.xml", "component": {"id": "3a314f53-92c3-4a55-8dcf-6c3ad766acb4", "name": "Canonical To Kafka Avro", "type": "transform.map", "version": "1", "created_by": "<PERSON><PERSON><EMAIL>", "created_date": "2025-06-17T12:54:55Z", "modified_by": "<PERSON><PERSON><EMAIL>", "modified_date": "2025-06-17T12:54:55Z", "folder_path": "ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08", "description": ""}, "map": {"from_profile": "a0733742-31f8-4c23-a8a4-08382ffb75ce", "to_profile": "d93e0b39-c94d-4947-9b49-5d9939473947", "mappings": [{"from_key": "9", "from_type": "profile", "to_key": "144", "to_name_path": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username", "to_type": "profile"}, {"from_key": "91", "from_type": "profile", "to_key": "160", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID", "to_type": "profile"}, {"from_key": "111", "from_type": "profile", "to_key": "174", "to_name_path": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type", "to_type": "profile"}, {"from_key": "118", "from_type": "profile", "to_key": "150", "to_name_path": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name", "to_type": "profile"}]}}], "connectors": [], "errors": []}}}