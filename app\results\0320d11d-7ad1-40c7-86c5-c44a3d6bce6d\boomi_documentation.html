<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation with Mermaid</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #1565c0;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            background: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
        }
        pre.mermaid {
            text-align: center;
            background: white;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .note {
            background: #e3f2fd;
            padding: 10px;
            border-left: 4px solid #1565c0;
            margin: 10px 0;
        }
        .insights {
            background: #f3e5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .best-practices {
            background: #e8f5e9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .security {
            background: #ffebee;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1 id="create-salesforce-opportunities-from-stripe-subscriptions-integration">Create Salesforce Opportunities from Stripe Subscriptions Integration</h1>
<h2 id="table-of-contents">Table of Contents</h2>
<ul>
<li><a href="#create-salesforce-opportunities-from-stripe-subscriptions-integration">Create Salesforce Opportunities from Stripe Subscriptions Integration</a></li>
<li><a href="#table-of-contents">Table of Contents</a></li>
<li><a href="#api-overview">API Overview</a></li>
<li><a href="#endpoints">Endpoints</a></li>
<li><a href="#current-dell-boomi-flow-logic">Current Dell Boomi Flow Logic</a><ul>
<li><a href="#process-flow-overview">Process Flow Overview</a></li>
<li><a href="#step-by-step-flow-description">Step-by-Step Flow Description</a></li>
</ul>
</li>
<li><a href="#dataweave-transformations-explained">DataWeave Transformations Explained</a></li>
<li><a href="#sap-integration-suite-implementation">SAP Integration Suite Implementation</a><ul>
<li><a href="#component-mapping">Component Mapping</a></li>
<li><a href="#integration-flow-visualization">Integration Flow Visualization</a></li>
<li><a href="#configuration-details">Configuration Details</a></li>
</ul>
</li>
<li><a href="#environment-configuration">Environment Configuration</a></li>
<li><a href="#api-reference">API Reference</a></li>
</ul>
<h2 id="api-overview">API Overview</h2>
<p>This integration creates Salesforce Opportunities based on Stripe Subscription data. The integration listens for Stripe subscription events, processes the subscription data, and creates corresponding opportunity records in Salesforce.</p>
<ul>
<li><strong>Base URL</strong>: Determined by the Stripe webhook configuration</li>
<li><strong>Authentication</strong>: Likely uses API keys for both Stripe and Salesforce connections</li>
<li><strong>Rate Limiting</strong>: Subject to Stripe and Salesforce API rate limits</li>
<li><strong>General Response Format</strong>: JSON responses from both Stripe and Salesforce APIs</li>
</ul>
<h2 id="endpoints">Endpoints</h2>
<h3 id="stripe-webhook-endpoint">Stripe Webhook Endpoint</h3>
<ul>
<li><strong>HTTP Method</strong>: POST</li>
<li><strong>Purpose</strong>: Receives subscription events from Stripe</li>
<li><strong>Request Parameters</strong>: None</li>
<li><strong>Request Body</strong>: Stripe webhook event payload containing subscription data</li>
<li><strong>Response Format</strong>: 200 OK for successful processing</li>
<li><strong>Error Handling</strong>: Error responses with appropriate HTTP status codes</li>
</ul>
<h3 id="salesforce-opportunity-creation-endpoint">Salesforce Opportunity Creation Endpoint</h3>
<ul>
<li><strong>HTTP Method</strong>: POST</li>
<li><strong>Path</strong>: /services/data/v[VERSION]/sobjects/Opportunity</li>
<li><strong>Purpose</strong>: Creates new opportunity records in Salesforce</li>
<li><strong>Request Parameters</strong>: None</li>
<li><strong>Request Body</strong>: JSON payload with opportunity details</li>
<li><strong>Response Format</strong>: JSON response with success/failure information</li>
<li><strong>Error Handling</strong>: Error responses with Salesforce error codes</li>
</ul>
<h2 id="current-dell-boomi-flow-logic">Current Dell Boomi Flow Logic</h2>
<h3 id="process-flow-overview">Process Flow Overview</h3>
<p>The Dell Boomi process "Create Salesforce Opportunities from Stripe Subscriptions" follows these main steps:
1. Start event (shape1)
2. Set Dynamic Properties (shape6)
3. Transform data (shape4)
4. Process transformed data (shape3)
5. End event (shape5)</p>
<h3 id="step-by-step-flow-description">Step-by-Step Flow Description</h3>
<ol>
<li><strong>Start Event (shape1)</strong>:</li>
<li>
<p>Triggers the integration process, likely configured to listen for Stripe webhook events</p>
</li>
<li>
<p><strong>Set Dynamic Properties (shape6)</strong>:</p>
</li>
<li>Sets runtime properties needed for the integration</li>
<li>
<p>May include Salesforce credentials, API endpoints, or other configuration parameters</p>
</li>
<li>
<p><strong>Transform Data (shape4)</strong>:</p>
</li>
<li>Transforms the incoming Stripe subscription data into the format required for Salesforce Opportunities</li>
<li>Maps fields from Stripe subscription to Salesforce Opportunity fields</li>
<li>
<p>Likely includes logic to handle different subscription statuses or types</p>
</li>
<li>
<p><strong>Process Data (shape3)</strong>:</p>
</li>
<li>Processes the transformed data</li>
<li>Likely sends the transformed data to Salesforce to create Opportunity records</li>
<li>
<p>May include error handling and retry logic</p>
</li>
<li>
<p><strong>End Event (shape5)</strong>:</p>
</li>
<li>Completes the integration process</li>
<li>May return success/failure status</li>
</ol>
<h2 id="dataweave-transformations-explained">DataWeave Transformations Explained</h2>
<p>Since the specific DataWeave transformations are not provided in the source documentation, we can infer that the transformation in shape4 likely performs the following:</p>
<ol>
<li><strong>Input Format</strong>: Stripe Subscription JSON data</li>
<li><strong>Output Format</strong>: Salesforce Opportunity JSON data</li>
<li><strong>Mapping Logic</strong>:</li>
<li>Maps Stripe subscription ID to a Salesforce reference field</li>
<li>Maps subscription amount to Opportunity amount</li>
<li>Maps subscription start date to Opportunity close date</li>
<li>Maps customer information to Opportunity account information</li>
<li>Sets appropriate Opportunity stage based on subscription status</li>
</ol>
<h2 id="sap-integration-suite-implementation">SAP Integration Suite Implementation</h2>
<h3 id="component-mapping">Component Mapping</h3>
<table>
<thead>
<tr>
<th>Dell Boomi Component</th>
<th>SAP Integration Suite Equivalent</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td>Start Event (shape1)</td>
<td>Start Message Event</td>
<td>Configured to receive webhook events from Stripe</td>
</tr>
<tr>
<td>Set Dynamic Properties (shape6)</td>
<td>Content Modifier</td>
<td>Used to set properties and variables for the integration flow</td>
</tr>
<tr>
<td>Transform (shape4)</td>
<td>Message Mapping</td>
<td>Maps Stripe subscription data to Salesforce Opportunity format</td>
</tr>
<tr>
<td>Process Data (shape3)</td>
<td>Request Reply or HTTPS Adapter</td>
<td>Sends data to Salesforce API</td>
</tr>
<tr>
<td>End Event (shape5)</td>
<td>End Message Event</td>
<td>Completes the integration flow</td>
</tr>
</tbody>
</table>
<h3 id="integration-flow-visualization">Integration Flow Visualization</h3>
<pre class="mermaid">
flowchart TD
    %% Define node styles
    classDef messageEvent fill:#87CEEB,stroke:#333,stroke-width:2px
    classDef contentModifier fill:#98FB98,stroke:#333,stroke-width:2px
    classDef mapping fill:#DDA0DD,stroke:#333,stroke-width:2px
    classDef adapter fill:#FFB6C1,stroke:#333,stroke-width:2px
    classDef exception fill:#FFA07A,stroke:#333,stroke-width:2px

    %% Main Flow
    Start((Start)) --> SetDynamicProps[Set Dynamic Properties]
    SetDynamicProps --> TransformData[Transform Stripe to Salesforce]
    TransformData --> SendToSalesforce[Send to Salesforce API]
    SendToSalesforce --> End((End))

    %% Error Handling
    SendToSalesforce -->|Error| ErrorHandler[(Error Handler)]
    ErrorHandler --> LogError[Log Error]
    LogError --> ErrorEnd((Error End))

    %% Add styling
    class Start,End,ErrorEnd messageEvent
    class SetDynamicProps contentModifier
    class TransformData mapping
    class SendToSalesforce adapter
    class ErrorHandler,LogError exception
</pre>
<h3 id="configuration-details">Configuration Details</h3>
<ol>
<li><strong>Start Message Event</strong>:</li>
<li>Type: Webhook Receiver</li>
<li>Protocol: HTTPS</li>
<li>Authentication: API Key or OAuth</li>
<li>
<p>Webhook URL: To be configured in Stripe</p>
</li>
<li>
<p><strong>Content Modifier (Set Dynamic Properties)</strong>:</p>
</li>
<li>
<p>Properties to set:</p>
<ul>
<li>Salesforce API URL</li>
<li>Salesforce API Version</li>
<li>Authentication credentials</li>
<li>Error handling parameters</li>
</ul>
</li>
<li>
<p><strong>Message Mapping (Transform Data)</strong>:</p>
</li>
<li>Source Format: JSON (Stripe Subscription)</li>
<li>Target Format: JSON (Salesforce Opportunity)</li>
<li>
<p>Mapping Fields:</p>
<ul>
<li>Stripe subscription ID → Salesforce external ID</li>
<li>Subscription amount → Opportunity amount</li>
<li>Subscription period → Opportunity close date</li>
<li>Customer details → Account reference</li>
<li>Subscription status → Opportunity stage</li>
</ul>
</li>
<li>
<p><strong>Request Reply or HTTPS Adapter (Send to Salesforce)</strong>:</p>
</li>
<li>Method: POST</li>
<li>URL: Salesforce API endpoint for Opportunity creation</li>
<li>Headers:<ul>
<li>Content-Type: application/json</li>
<li>Authorization: Bearer {token}</li>
</ul>
</li>
<li>
<p>Response Handling: Process success/error responses</p>
</li>
<li>
<p><strong>Error Handler</strong>:</p>
</li>
<li>Capture error details</li>
<li>Log errors</li>
<li>Optional retry logic for transient errors</li>
</ol>
<h2 id="environment-configuration">Environment Configuration</h2>
<p>Based on the source documentation, the following configuration details are required:</p>
<ol>
<li><strong>Stripe Configuration</strong>:</li>
<li>Webhook endpoint URL</li>
<li>API key for authentication</li>
<li>
<p>Event types to subscribe to (subscription.created, subscription.updated, etc.)</p>
</li>
<li>
<p><strong>Salesforce Configuration</strong>:</p>
</li>
<li>API endpoint URL</li>
<li>API version</li>
<li>OAuth credentials or username/password</li>
<li>
<p>Connected App configuration</p>
</li>
<li>
<p><strong>Security Settings</strong>:</p>
</li>
<li>TLS/SSL certificates for secure communication</li>
<li>API key storage in secure credential stores</li>
<li>
<p>IP restrictions for API access</p>
</li>
<li>
<p><strong>Deployment Considerations</strong>:</p>
</li>
<li>High availability setup for production</li>
<li>Monitoring for integration health</li>
<li>
<p>Alert configuration for failed integrations</p>
</li>
<li>
<p><strong>Required Resources</strong>:</p>
</li>
<li>Memory: Dependent on expected transaction volume</li>
<li>CPU: Dependent on transformation complexity</li>
<li>Disk Space: Minimal, primarily for logging</li>
</ol>
<h2 id="api-reference">API Reference</h2>
<h3 id="stripe-api">Stripe API</h3>
<ol>
<li><strong>Subscription Webhook Events</strong>:</li>
<li>Event Types:<ul>
<li><code>subscription.created</code></li>
<li><code>subscription.updated</code></li>
<li><code>subscription.deleted</code></li>
</ul>
</li>
<li>Payload Structure: Contains subscription object with details</li>
<li>
<p>Documentation Reference: https://stripe.com/docs/api/subscriptions</p>
</li>
<li>
<p><strong>Subscription Object Properties</strong>:</p>
</li>
<li><code>id</code>: Unique identifier for the subscription</li>
<li><code>customer</code>: ID of the customer</li>
<li><code>status</code>: Current status of the subscription</li>
<li><code>current_period_start</code>: Start of the current billing period</li>
<li><code>current_period_end</code>: End of the current billing period</li>
<li><code>items</code>: Array of subscription items</li>
<li><code>metadata</code>: Key-value pairs for additional information</li>
</ol>
<h3 id="salesforce-api">Salesforce API</h3>
<ol>
<li><strong>Opportunity Creation</strong>:</li>
<li>Method: POST</li>
<li>Endpoint: <code>/services/data/v[VERSION]/sobjects/Opportunity</code></li>
<li>Required Fields:<ul>
<li><code>Name</code>: Name of the opportunity</li>
<li><code>StageName</code>: Stage in the sales process</li>
<li><code>CloseDate</code>: Expected close date</li>
<li><code>AccountId</code>: ID of the associated account</li>
</ul>
</li>
<li>
<p>Optional Fields:</p>
<ul>
<li><code>Amount</code>: Monetary value of the opportunity</li>
<li><code>Type</code>: Type of opportunity</li>
<li><code>Description</code>: Additional details</li>
<li>Custom fields for Stripe reference</li>
</ul>
</li>
<li>
<p><strong>Error Codes</strong>:</p>
</li>
<li>201: Created successfully</li>
<li>400: Bad request (invalid data)</li>
<li>401: Unauthorized (authentication failure)</li>
<li>403: Forbidden (insufficient permissions)</li>
<li>404: Not found</li>
<li>
<p>500: Internal server error</p>
</li>
<li>
<p><strong>Rate Limiting</strong>:</p>
</li>
<li>Salesforce enforces API request limits per 24-hour period</li>
<li>Limits vary by edition and license type</li>
<li>Response headers include remaining limits</li>
</ol>
    
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>