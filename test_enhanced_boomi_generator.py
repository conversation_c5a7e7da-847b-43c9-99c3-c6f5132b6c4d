#!/usr/bin/env python3
"""
Test the enhanced Boomi documentation generator to ensure it extracts detailed information.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, 'app')

def test_enhanced_boomi_generator():
    """Test that the enhanced generator extracts detailed Boomi information."""
    
    try:
        from boomi_flow_documentation import BoomiFlowDocumentationGenerator
        
        # Create generator instance
        generator = BoomiFlowDocumentationGenerator()
        
        print("🧪 Testing Enhanced Boomi Documentation Generator")
        print("=" * 60)
        
        # Test with the Stripe to Salesforce XML file
        test_file = "StripeToSFOpp/Create-SFOpp-from-Stripe.xml"
        
        if not os.path.exists(test_file):
            print(f"❌ Test file not found: {test_file}")
            return False
        
        print(f"📄 Processing test file: {test_file}")
        
        # Process the XML file
        result = generator._process_xml_file(test_file)
        
        if not result:
            print("❌ Failed to process XML file")
            return False
        
        print(f"✅ Successfully processed XML file")
        print(f"📊 Component type: {result['type']}")
        
        if result['type'] == 'process':
            process_info = result['process']
            shapes = process_info['shapes']
            
            print(f"📊 Found {len(shapes)} shapes in process")
            
            # Check for detailed analysis in Document Properties
            doc_props_found = False
            http_calls_found = False
            calculations_found = False
            
            for shape in shapes:
                shape_name = shape.get('name', 'Unknown')
                shape_type = shape.get('type', 'Unknown')
                config = shape.get('configuration', {})
                
                print(f"   - {shape_name} ({shape_type})")
                
                if shape_type == 'documentproperties':
                    doc_props_found = True
                    doc_props = config.get('document_properties', [])
                    
                    print(f"     📋 Found {len(doc_props)} document properties")
                    
                    for prop in doc_props:
                        prop_name = prop.get('name', 'Unknown')
                        business_logic = prop.get('business_logic', {})
                        
                        print(f"       - {prop_name}: {business_logic.get('type', 'simple')}")
                        
                        if business_logic.get('http_calls'):
                            http_calls_found = True
                            print(f"         🌐 HTTP calls: {len(business_logic['http_calls'])}")
                        
                        if business_logic.get('calculations'):
                            calculations_found = True
                            print(f"         🧮 Calculations: {len(business_logic['calculations'])}")
            
            # Generate documentation
            print("\n📝 Generating documentation...")
            processing_results = {
                'processed_files': 1,
                'processes': [result],
                'maps': [],
                'connectors': [],
                'errors': []
            }
            
            documentation = generator.generate_documentation(processing_results)
            
            # Check documentation content
            doc_checks = [
                ("Document Properties analysis", "Document Properties (Dynamic Property Setting)" in documentation),
                ("HTTP API calls mentioned", "External API Calls" in documentation),
                ("Business logic details", "Business Purpose" in documentation),
                ("Detailed step analysis", "Detailed Process Flow Analysis" in documentation),
                ("External dependencies", "external" in documentation.lower()),
                ("Calculations mentioned", "calculation" in documentation.lower())
            ]
            
            print("\n📊 Documentation Quality Checks:")
            all_passed = True
            
            for check_name, check_result in doc_checks:
                status = "✅" if check_result else "❌"
                print(f"   {status} {check_name}")
                if not check_result:
                    all_passed = False
            
            # Summary
            print(f"\n📈 Enhancement Results:")
            print(f"   Document Properties Found: {'✅' if doc_props_found else '❌'}")
            print(f"   HTTP Calls Detected: {'✅' if http_calls_found else '❌'}")
            print(f"   Calculations Detected: {'✅' if calculations_found else '❌'}")
            print(f"   Documentation Quality: {'✅ PASSED' if all_passed else '❌ NEEDS IMPROVEMENT'}")
            
            # Save sample documentation for review
            with open('test_enhanced_documentation_sample.md', 'w', encoding='utf-8') as f:
                f.write(documentation)
            print(f"\n📄 Sample documentation saved to: test_enhanced_documentation_sample.md")
            
            return doc_props_found and all_passed
            
        else:
            print(f"⚠️ Expected process type, got: {result['type']}")
            return False
            
    except Exception as e:
        print(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔧 Testing Enhanced Boomi Documentation Generator")
    print("=" * 70)
    
    success = test_enhanced_boomi_generator()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Enhanced Boomi documentation generator test PASSED!")
        print("✅ The generator now extracts:")
        print("   - Document Properties with HTTP calls and business logic")
        print("   - Detailed connector action analysis")
        print("   - Map transformations with function steps")
        print("   - External system dependencies")
        print("   - Business rules and calculations")
        print("\n🚀 This should generate much more accurate base documentation!")
        return 0
    else:
        print("⚠️ Enhanced Boomi documentation generator test FAILED!")
        print("❌ Some enhancements are not working correctly")
        return 1

if __name__ == "__main__":
    exit(main())
