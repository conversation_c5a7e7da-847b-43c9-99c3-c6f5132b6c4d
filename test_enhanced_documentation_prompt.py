#!/usr/bin/env python3
"""
Test the enhanced documentation prompt to ensure it captures all Boomi-specific details.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, 'app')

def test_enhanced_prompt():
    """Test that the enhanced prompt includes all Boomi-specific requirements."""
    
    try:
        from documentation_enhancer import DocumentationEnhancer
        
        # Create enhancer instance
        enhancer = DocumentationEnhancer()
        
        # Create a sample base documentation (simulating what would come from Boomi XML analysis)
        sample_boomi_doc = """
        # Dell Boomi Integration Documentation
        
        ## Summary
        - **Total Files Processed:** 5
        - **Processes:** 1
        - **Maps:** 1
        - **Connectors:** 3
        
        ## Process Flow
        1. Start Event (shape1) - Web Services Server Listen
        2. Document Properties (shape6) - Set Dynamic Properties with HTTP calls
        3. Map Component (shape4) - Transform data
        4. Connector Action (shape3) - Salesforce Send
        5. Stop Event (shape5) - End process
        
        ## Document Properties Details
        - DDP_CustomerName: HTTP GET call to retrieve customer name
        - DDP_Subscription: HTTP GET call to retrieve product details
        - DDP_SalesforceDescription: Concatenation logic
        - DDP_CloseDate: Date calculation (3 months ago)
        
        ## Map Component
        - Maps DDP_CustomerName to Opportunity.Name
        - Maps DDP_SalesforceDescription to Opportunity.Description
        - Maps DDP_CloseDate to Opportunity.CloseDate
        - Default: StageName = "Pipeline"
        """
        
        print("🧪 Testing Enhanced Documentation Prompt")
        print("=" * 60)
        
        # Get the enhanced prompt (this will create the prompt but not call the LLM)
        # We'll extract the prompt by temporarily modifying the enhance_documentation method
        original_method = enhancer.enhance_documentation
        captured_prompt = None
        
        def capture_prompt_method(base_doc):
            nonlocal captured_prompt
            # This is the same logic as in the original method, but we capture the prompt
            captured_prompt = f"""You are a Dell Boomi and SAP Integration Suite specialist. Based on the following technical
    documentation, create comprehensive documentation that includes API details, flow logic,
    and detailed SAP Integration Suite visualization. Use SAP Integration Suite components and connections for
    the visualization.

    IMPORTANT BOOMI-SPECIFIC ANALYSIS REQUIREMENTS:
    1. Do NOT make assumptions about adapters or systems not explicitly mentioned in the source documentation.
    2. Use ONLY the components and connections present in the original Boomi flow.
    3. When describing the SAP Integration Suite implementation, maintain the same integration pattern.
    4. If a connection type is unclear, mark it as a configuration decision.
    5. PRESERVE ALL TECHNICAL EXPRESSIONS EXACTLY AS WRITTEN, especially:
    - All OData query parameters like $filter, $select, $expand
    - All DataWeave transformations including variables, functions, and operators
    - All conditions, regex patterns, and logical expressions
    - DO NOT simplify, summarize, or rewrite any technical expressions

    CRITICAL BOOMI COMPONENT ANALYSIS:
    6. **Document Properties (shape type="documentproperties")**: These are COMPLEX components that:
       - Make HTTP/API calls to external systems (analyze connectorparameter elements)
       - Perform data lookups and enrichment
       - Calculate dynamic values using date functions, concatenation, etc.
       - Set runtime properties for use in subsequent steps
       - NEVER describe these as simple "set properties" - analyze the actual logic!
    
    7. **Connector Actions**: Analyze the specific connector types and operations:
       - HTTP connectors: Extract URLs, methods, authentication details
       - Salesforce connectors: Document object types, operations (create/update/query)
       - Database connectors: Document SQL operations and connection details
       - Web Services: Document SOAP/REST operations and endpoints
    
    8. **Map Components**: Analyze the detailed field mappings:
       - Source and target profiles/schemas
       - Function steps and their logic (date calculations, concatenations, lookups)
       - Default values and constants
       - Complex mapping logic and transformations
    
    9. **Process Flow Dependencies**: Document external API dependencies:
       - HTTP calls to retrieve additional data (customer info, product details, etc.)
       - Authentication requirements for external systems
       - Data enrichment patterns and lookup operations
    
    10. **Business Logic Extraction**: Identify and document:
        - Date calculation logic (relative dates, formatting)
        - String concatenation and formatting patterns
        - Conditional logic and branching
        - Error handling and retry mechanisms

    HANDLING LARGE DOCUMENTATION:
    1. First analyze the documentation size and structure. If you determine the content will exceed 20,000 tokens in your response, you should:
       - Focus on the most essential components and flows in your detailed analysis
       - Ensure all flows are at least mentioned, even if some details are summarized
       - Prioritize accuracy over comprehensive detail for very large applications
    2. Your final output must include a complete and accurate mapping of all components, even if you need to be more concise in your explanations.

    Here is the source documentation:

    {base_doc}"""
            return base_doc  # Return original to avoid calling LLM
        
        # Temporarily replace the method
        enhancer.enhance_documentation = capture_prompt_method
        
        # Call the method to capture the prompt
        enhancer.enhance_documentation(sample_boomi_doc)
        
        # Restore original method
        enhancer.enhance_documentation = original_method
        
        if captured_prompt:
            print("✅ Enhanced prompt captured successfully")
            print(f"📄 Prompt length: {len(captured_prompt)} characters")
            
            # Check for Boomi-specific requirements
            boomi_requirements = [
                "Dell Boomi and SAP Integration Suite specialist",
                "CRITICAL BOOMI COMPONENT ANALYSIS",
                "Document Properties (shape type=\"documentproperties\")",
                "Make HTTP/API calls to external systems",
                "analyze connectorparameter elements",
                "Calculate dynamic values using date functions",
                "NEVER describe these as simple \"set properties\"",
                "Connector Actions",
                "HTTP connectors: Extract URLs, methods, authentication",
                "Salesforce connectors: Document object types",
                "Map Components",
                "Function steps and their logic",
                "Process Flow Dependencies",
                "HTTP calls to retrieve additional data",
                "Business Logic Extraction",
                "Date calculation logic",
                "String concatenation and formatting patterns"
            ]
            
            found_requirements = []
            missing_requirements = []
            
            for req in boomi_requirements:
                if req in captured_prompt:
                    found_requirements.append(req)
                else:
                    missing_requirements.append(req)
            
            print(f"\n📊 Boomi Requirements Analysis:")
            print(f"   Found: {len(found_requirements)}/{len(boomi_requirements)} requirements")
            
            if missing_requirements:
                print(f"\n❌ Missing requirements:")
                for missing in missing_requirements:
                    print(f"   - {missing}")
                return False
            else:
                print(f"\n✅ All Boomi-specific requirements found!")
            
            # Check for enhanced sections
            enhanced_sections = [
                "Current Dell Boomi Flow Logic",
                "Document Properties Logic",
                "Map Transformations", 
                "Connector Operations",
                "Boomi Core Components",
                "Document Properties (with HTTP calls)",
                "External System Dependencies",
                "Connector Configurations"
            ]
            
            found_sections = []
            for section in enhanced_sections:
                if section in captured_prompt:
                    found_sections.append(section)
            
            print(f"\n📊 Enhanced Sections Analysis:")
            print(f"   Found: {len(found_sections)}/{len(enhanced_sections)} enhanced sections")
            
            if len(found_sections) == len(enhanced_sections):
                print(f"✅ All enhanced sections present!")
                return True
            else:
                print(f"⚠️ Some enhanced sections missing")
                return False
                
        else:
            print("❌ Failed to capture prompt")
            return False
            
    except Exception as e:
        print(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔧 Testing Enhanced Documentation Prompt for Boomi")
    print("=" * 70)
    
    success = test_enhanced_prompt()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Enhanced documentation prompt test PASSED!")
        print("✅ The prompt now includes:")
        print("   - Boomi-specific component analysis")
        print("   - Document Properties complexity recognition")
        print("   - HTTP/API call documentation requirements")
        print("   - Business logic extraction guidelines")
        print("   - Enhanced configuration details")
        print("   - External system dependency analysis")
        print("\n🚀 This should generate much more accurate documentation!")
        return 0
    else:
        print("⚠️ Enhanced documentation prompt test FAILED!")
        print("❌ Some requirements are missing")
        return 1

if __name__ == "__main__":
    exit(main())
