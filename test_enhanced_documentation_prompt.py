#!/usr/bin/env python3
"""
Test the enhanced documentation prompt to ensure it captures all Boomi-specific details.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, 'app')

def test_enhanced_prompt():
    """Test that the enhanced prompt includes all Boomi-specific requirements."""
    
    try:
        from documentation_enhancer import DocumentationEnhancer
        
        # Create enhancer instance
        enhancer = DocumentationEnhancer()
        
        # Create a sample base documentation (simulating what would come from Boomi XML analysis)
        sample_boomi_doc = """
        # Dell Boomi Integration Documentation
        
        ## Summary
        - **Total Files Processed:** 5
        - **Processes:** 1
        - **Maps:** 1
        - **Connectors:** 3
        
        ## Process Flow
        1. Start Event (shape1) - Web Services Server Listen
        2. Document Properties (shape6) - Set Dynamic Properties with HTTP calls
        3. Map Component (shape4) - Transform data
        4. Connector Action (shape3) - Salesforce Send
        5. Stop Event (shape5) - End process
        
        ## Document Properties Details
        - DDP_CustomerName: HTTP GET call to retrieve customer name
        - DDP_Subscription: HTTP GET call to retrieve product details
        - DDP_SalesforceDescription: Concatenation logic
        - DDP_CloseDate: Date calculation (3 months ago)
        
        ## Map Component
        - Maps DDP_CustomerName to Opportunity.Name
        - Maps DDP_SalesforceDescription to Opportunity.Description
        - Maps DDP_CloseDate to Opportunity.CloseDate
        - Default: StageName = "Pipeline"
        """
        
        print("🧪 Testing Enhanced Documentation Prompt")
        print("=" * 60)
        
        # Get the enhanced prompt (this will create the prompt but not call the LLM)
        # We'll extract the prompt by temporarily modifying the enhance_documentation method
        original_method = enhancer.enhance_documentation
        captured_prompt = None
        
        def capture_prompt_method(base_doc):
            nonlocal captured_prompt
            # Read the actual prompt from the file to get the complete version
            with open('app/documentation_enhancer.py', 'r', encoding='utf-8') as f:
                file_content = f.read()

            # Extract the prompt from the file (it's the f-string starting with "You are a Dell Boomi")
            start_marker = 'prompt = f"""You are a Dell Boomi'
            end_marker = 'Make sure the final document has:'

            start_idx = file_content.find(start_marker)
            end_idx = file_content.find(end_marker)

            if start_idx != -1 and end_idx != -1:
                # Extract the prompt section and add the ending
                prompt_section = file_content[start_idx:end_idx + len(end_marker)]
                # Find the end of the prompt (the closing triple quotes)
                end_quotes = prompt_section.find('"""', prompt_section.find(end_marker))
                if end_quotes != -1:
                    prompt_section = prompt_section[:end_quotes]

                # Remove the 'prompt = f"""' part and get just the prompt content
                prompt_content = prompt_section[len('prompt = f"""'):]
                captured_prompt = prompt_content.format(base_documentation=base_doc)
            else:
                captured_prompt = "Could not extract prompt from file"

            return base_doc  # Return original to avoid calling LLM
        
        # Temporarily replace the method
        enhancer.enhance_documentation = capture_prompt_method
        
        # Call the method to capture the prompt
        enhancer.enhance_documentation(sample_boomi_doc)
        
        # Restore original method
        enhancer.enhance_documentation = original_method
        
        if captured_prompt:
            print("✅ Enhanced prompt captured successfully")
            print(f"📄 Prompt length: {len(captured_prompt)} characters")
            
            # Check for Boomi-specific requirements
            boomi_requirements = [
                "Dell Boomi and SAP Integration Suite specialist",
                "CRITICAL BOOMI COMPONENT ANALYSIS",
                "Document Properties (shape type=\"documentproperties\")",
                "Make HTTP/API calls to external systems",
                "analyze connectorparameter elements",
                "Calculate dynamic values using date functions",
                "NEVER describe these as simple \"set properties\"",
                "Connector Actions",
                "HTTP connectors: Extract URLs, methods, authentication",
                "Salesforce connectors: Document object types",
                "Map Components",
                "Function steps and their logic",
                "Process Flow Dependencies",
                "HTTP calls to retrieve additional data",
                "Business Logic Extraction",
                "Date calculation logic",
                "String concatenation and formatting patterns"
            ]
            
            found_requirements = []
            missing_requirements = []
            
            for req in boomi_requirements:
                if req in captured_prompt:
                    found_requirements.append(req)
                else:
                    missing_requirements.append(req)
            
            print(f"\n📊 Boomi Requirements Analysis:")
            print(f"   Found: {len(found_requirements)}/{len(boomi_requirements)} requirements")
            
            if missing_requirements:
                print(f"\n❌ Missing requirements:")
                for missing in missing_requirements:
                    print(f"   - {missing}")
                return False
            else:
                print(f"\n✅ All Boomi-specific requirements found!")
            
            # Check for enhanced sections
            enhanced_sections = [
                "Current Dell Boomi Flow Logic",
                "Document Properties Logic",
                "Map Transformations", 
                "Connector Operations",
                "Boomi Core Components",
                "Document Properties (with HTTP calls)",
                "External System Dependencies",
                "Connector Configurations"
            ]
            
            found_sections = []
            for section in enhanced_sections:
                if section in captured_prompt:
                    found_sections.append(section)
            
            print(f"\n📊 Enhanced Sections Analysis:")
            print(f"   Found: {len(found_sections)}/{len(enhanced_sections)} enhanced sections")
            
            if len(found_sections) == len(enhanced_sections):
                print(f"✅ All enhanced sections present!")
                return True
            else:
                print(f"⚠️ Some enhanced sections missing")
                return False
                
        else:
            print("❌ Failed to capture prompt")
            return False
            
    except Exception as e:
        print(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔧 Testing Enhanced Documentation Prompt for Boomi")
    print("=" * 70)
    
    success = test_enhanced_prompt()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Enhanced documentation prompt test PASSED!")
        print("✅ The prompt now includes:")
        print("   - Boomi-specific component analysis")
        print("   - Document Properties complexity recognition")
        print("   - HTTP/API call documentation requirements")
        print("   - Business logic extraction guidelines")
        print("   - Enhanced configuration details")
        print("   - External system dependency analysis")
        print("\n🚀 This should generate much more accurate documentation!")
        return 0
    else:
        print("⚠️ Enhanced documentation prompt test FAILED!")
        print("❌ Some requirements are missing")
        return 1

if __name__ == "__main__":
    exit(main())
