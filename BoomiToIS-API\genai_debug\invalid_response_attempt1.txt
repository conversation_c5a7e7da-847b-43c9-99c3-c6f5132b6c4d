{"process_name": "SAP SuccessFactors to SFTP Integration", "description": "Integration that extracts employee profile data from SAP SuccessFactors, transforms it to the required format, and delivers it to an SFTP server with comprehensive error handling", "endpoints": [{"method": "GET", "path": "/SuccessFactors/Employee", "purpose": "Retrieves employee profile data from SuccessFactors, transforms it, and sends it to SFTP", "components": [{"type": "odata", "name": "Get_SuccessFactors_Employee_Data", "id": "odata_sf_employee", "config": {"address": "${SF_API_BASE_URL}", "resource_path": "odata/v2/User", "operation": "Query(GET)", "query_options": "$select=username,contactID,contactPointType,batchProcessingOptionName"}}, {"type": "groovy_script", "name": "Validate_Response", "id": "validate_response", "config": {"script": "ValidateSuccessFactorsResponse.groovy"}}, {"type": "groovy_script", "name": "Transform_Canonical_To_Kafka_Avro", "id": "transform_to_kafka_avro", "config": {"script": "CanonicalToKafkaAvro.groovy"}}, {"type": "request_reply", "name": "Write_To_SFTP", "id": "sftp_write", "config": {"endpoint_path": "${SFTP_TARGET_DIR}"}}], "error_handling": {"exception_subprocess": [{"type": "enricher", "name": "Prepare_Error_Log", "id": "error_log_enricher", "trigger": "any_error", "config": {"content": "Error occurred during SuccessFactors to SFTP integration: ${exception.message}"}}, {"type": "request_reply", "name": "Send_Email_Notification", "id": "email_notification", "trigger": "any_error", "config": {"endpoint_path": "/mail"}}]}, "branching": {"type": "exclusive", "branches": [{"condition": "Valid Response", "components": ["transform_to_kafka_avro", "sftp_write"], "sequence": ["transform_to_kafka_avro", "sftp_write"]}, {"condition": "Invalid Response", "components": ["error_log_enricher", "email_notification"], "sequence": ["error_log_enricher", "email_notification"]}]}, "sequence": ["odata_sf_employee", "validate_response", "transform_to_kafka_avro", "sftp_write"], "transformations": [{"name": "CanonicalToKafkaAvro.groovy", "type": "groovy", "script": "import com.sap.gateway.ip.core.customdev.util.Message;\n\ndef Message processData(Message message) {\n    def body = message.getBody(java.lang.String);\n    def jsonSlurper = new groovy.json.JsonSlurper();\n    def data = jsonSlurper.parseText(body);\n    \n    def result = [:];\n    result.batchProcessingDirectives = [:];\n    result.batchProcessingDirectives.accountID = [:];\n    result.batchProcessingDirectives.accountID.username = data.d.results[0].username;\n    \n    result.batchContactList = [[]];\n    result.batchContactList[0].contact = [[]];\n    result.batchContactList[0].contact[0].contactID = data.d.results[0].contactID;\n    \n    result.batchContactList[0].contact[0].contactPointList = [[]];\n    result.batchContactList[0].contact[0].contactPointList[0].contactPoint = [[]];\n    result.batchContactList[0].contact[0].contactPointList[0].contactPoint[0].type = data.d.results[0].contactPointType;\n    \n    result.batchProcessingDirectives.batchProcessingOption = [[]];\n    result.batchProcessingDirectives.batchProcessingOption[0].name = data.d.results[0].batchProcessingOptionName;\n    \n    message.setBody(groovy.json.JsonOutput.toJson(result));\n    return message;\n}"}, {"name": "ValidateSuccessFactorsResponse.groovy", "type": "groovy", "script": "import com.sap.gateway.ip.core.customdev.util.Message;\n\ndef Message processData(Message message) {\n    def body = message.getBody(java.lang.String);\n    def jsonSlurper = new groovy.json.JsonSlurper();\n    def data = jsonSlurper.parseText(body);\n    \n    // Check if response contains data\n    if (data.d && data.d.results && data.d.results.size() > 0) {\n        message.setProperty(\"valid_response\", \"true\");\n    } else {\n        message.setProperty(\"valid_response\", \"false\");\n        throw new Exception(\"Invalid or empty response from SuccessFactors\");\n    }\n    \n    return message;\n}"}]}]}