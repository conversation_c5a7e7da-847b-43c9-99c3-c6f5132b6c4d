{"process_name": "Name of the Boomi Process", "description": "Description of the Boomi Process conversion", "endpoints": [{"method": "HTTP method (GET, POST, etc.) - derived from Boomi connectors", "path": "Path of the endpoint - derived from Boomi process flow", "purpose": "Purpose of the process - derived from Boomi process documentation", "components": [{"type": "Component type - MUST be one of: enricher, request_reply, json_to_xml_converter, groovy_script, odata, odata_receiver (DO NOT use start_event or end_event). Map from Boomi components: Boomi Map→message_mapping, Boomi Connector→request_reply, Boomi Document Properties→enricher, Boomi Decision→router", "name": "Component name - should be descriptive and indicate Boomi origin (e.g., 'Map_CustomerData_from_Boomi')", "id": "Component ID - must be unique across all components", "config": {"endpoint_path": "For request_reply components, the path of the endpoint", "content": "For content_modifier components, the content to set", "script": "For groovy_script components, the name of the script file", "address": "For odata components, the URL of the OData service", "resource_path": "For odata components, the entity set or resource path to query", "operation": "For odata components, the operation to perform (Query(GET), Create(POST), etc.)", "query_options": "For odata components, query options like select, filter, etc."}}], "error_handling": {"exception_subprocess": [{"type": "Component type for error handling (enricher, groovy_script, request_reply for notifications)", "name": "Error handling component name", "id": "Unique ID for error component", "trigger": "What triggers this error handler (validation_error, connection_error, etc.)", "config": {}}]}, "branching": {"type": "parallel or exclusive - based on Boomi branch behavior", "branches": [{"condition": "Condition for this branch (if exclusive)", "components": ["List of component IDs for this branch"], "sequence": ["Order of components in this branch"]}]}, "sequence": ["List of component IDs in the order they should be connected", "For example: ['Start_Event_2','JSONtoXMLConverter_1', 'ContentModifier_1', 'RequestReply_1','End_Event_2']"], "transformations": [{"name": "Transformation name (e.g., 'TransformProductData.groovy')", "type": "groovy", "script": "Actual Groovy script content"}]}]}