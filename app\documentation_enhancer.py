"""Documentation enhancer using LLM services."""
import os
import sys
import logging
import json
from typing import Optional
from dotenv import load_dotenv

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Try to import OpenAI and Anthropic, but don't fail if not installed
try:
    import openai
except ImportError:
    openai = None
    logger.warning("OpenAI package not installed. OpenAI-based enhancement will not be available.")

try:
    import anthropic
    import httpx
except ImportError:
    anthropic = None
    logger.warning("Anthropic package not installed. Claude-based enhancement will not be available.")

class DocumentationEnhancer:
    def __init__(self, selected_service='openai'):
        """Initialize documentation enhancer with specified LLM service.

        Args:
            selected_service: Service to use ('openai' or 'anthropic')
        """
        self.selected_service = selected_service

        # Load API keys
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")

        # Initialize clients if possible
        self.openai_client = None
        self.anthropic_client = None

        if openai and self.openai_api_key:
            try:
                self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
                logger.info("OpenAI client initialized successfully.")
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI client: {str(e)}")

        if anthropic and self.anthropic_api_key:
            try:
                # Create a custom HTTP client with extended timeout (600 seconds instead of 300)
                http_client = httpx.Client(timeout=600.0)

                # Initialize Anthropic client with a custom http_client to avoid proxies issue
                self.anthropic_client = anthropic.Anthropic(
                    api_key=self.anthropic_api_key,
                    http_client=http_client
                )
                logger.info("Anthropic client initialized successfully.")
            except Exception as e:
                logger.error(f"Failed to initialize Anthropic client: {str(e)}")

    def enhance_documentation(self, base_documentation: str) -> str:
        """Enhance documentation using the configured LLM service.

        Args:
            base_documentation: Base documentation to enhance

        Returns:
            Enhanced documentation or original if enhancement fails
        """
        # Modified to use a variable to track enhancement success
        enhancement_successful = False

        prompt = f"""You are a Dell Boomi and SAP Integration Suite specialist. Based on the following technical
    documentation, create comprehensive documentation that includes API details, flow logic,
    and detailed SAP Integration Suite visualization. Use SAP Integration Suite components and connections for
    the visualization.

    IMPORTANT BOOMI-SPECIFIC ANALYSIS REQUIREMENTS:
    1. Do NOT make assumptions about adapters or systems not explicitly mentioned in the source documentation.
    2. Use ONLY the components and connections present in the original Boomi flow.
    3. When describing the SAP Integration Suite implementation, maintain the same integration pattern.
    4. If a connection type is unclear, mark it as a configuration decision.
    5. PRESERVE ALL TECHNICAL EXPRESSIONS EXACTLY AS WRITTEN, especially:
    - All OData query parameters like $filter, $select, $expand
    - All DataWeave transformations including variables, functions, and operators
    - All conditions, regex patterns, and logical expressions
    - DO NOT simplify, summarize, or rewrite any technical expressions

    CRITICAL BOOMI COMPONENT ANALYSIS:
    6. **Document Properties (shape type="documentproperties")**: These are COMPLEX components that:
       - Make HTTP/API calls to external systems (analyze connectorparameter elements)
       - Perform data lookups and enrichment
       - Calculate dynamic values using date functions, concatenation, etc.
       - Set runtime properties for use in subsequent steps
       - NEVER describe these as simple "set properties" - analyze the actual logic!

    7. **Connector Actions**: Analyze the specific connector types and operations:
       - HTTP connectors: Extract URLs, methods, authentication details
       - Salesforce connectors: Document object types, operations (create/update/query)
       - Database connectors: Document SQL operations and connection details
       - Web Services: Document SOAP/REST operations and endpoints

    8. **Map Components**: Analyze the detailed field mappings:
       - Source and target profiles/schemas
       - Function steps and their logic (date calculations, concatenations, lookups)
       - Default values and constants
       - Complex mapping logic and transformations

    9. **Process Flow Dependencies**: Document external API dependencies:
       - HTTP calls to retrieve additional data (customer info, product details, etc.)
       - Authentication requirements for external systems
       - Data enrichment patterns and lookup operations

    10. **Business Logic Extraction**: Identify and document:
        - Date calculation logic (relative dates, formatting)
        - String concatenation and formatting patterns
        - Conditional logic and branching
        - Error handling and retry mechanisms

    HANDLING LARGE DOCUMENTATION:
    1. First analyze the documentation size and structure. If you determine the content will exceed 20,000 tokens in your response, you should:
       - Focus on the most essential components and flows in your detailed analysis
       - Ensure all flows are at least mentioned, even if some details are summarized
       - Prioritize accuracy over comprehensive detail for very large applications
    2. Your final output must include a complete and accurate mapping of all components, even if you need to be more concise in your explanations.

    Here is the source documentation:

    {base_documentation}

    Please structure your response in Markdown with these sections:

    # [Descriptive Title Based on the API/Integration Purpose]

    ## Table of Contents
    Create a detailed table of contents with hyperlinks to all sections and subsections in the document. Use Markdown link syntax like [Section Name](#section-name) to create clickable links to each section. Include ALL sections and subsections.

    ## API Overview
    - Comprehensive description of what this API does and its business purpose
    - Base URL/endpoint pattern
    - Authentication mechanisms
    - Rate limiting information (if available)
    - General response format

    ## Endpoints
    For each endpoint, provide a detailed breakdown:
    - HTTP Method and full path
    - Purpose of the endpoint
    - Request parameters (path, query, headers) with detailed descriptions
    - Request body structure (if applicable) with field descriptions and data types
    - Response format and status codes with detailed descriptions
    - Example request/response if available
    - Error handling for this endpoint

    ## Current Dell Boomi Flow Logic

    ### Process Flow Overview
    Provide a high-level description of the integration process and its business purpose.

    ### Step-by-Step Flow Description
    For each shape in the process flow, provide detailed analysis:

    1. **Start Event (shape type="start")**:
       - What triggers the flow (webhook, timer, file, etc.)
       - Connector type and configuration details
       - Authentication and security settings

    2. **Document Properties (shape type="documentproperties")**:
       - CRITICAL: Analyze each documentproperty element in detail
       - Document HTTP connector calls and their purposes
       - Extract API endpoints, methods, and parameters
       - Explain data lookup and enrichment logic
       - Document date calculations and string manipulations
       - Show the business logic behind property calculations

    3. **Map Components (shape type="map")**:
       - Source and target data structures
       - Field-by-field mapping details
       - Function steps and their logic
       - Default values and constants
       - Complex transformations and calculations

    4. **Connector Actions (shape type="connectoraction")**:
       - Connector type (Salesforce, HTTP, Database, etc.)
       - Operation details (create, update, query, etc.)
       - Object types and field mappings
       - Authentication and connection settings

    5. **End Events and Error Handling**:
       - Process completion logic
       - Error handling mechanisms
       - Retry and notification patterns

    6. PAY SPECIAL ATTENTION to these technical details:
    - Include ALL HTTP API calls with exact URLs and parameters
    - Document ALL external system dependencies
    - Show ALL date calculation formulas and logic
    - Preserve ALL field mapping expressions exactly as found
    - Document ALL authentication and security configurations

    ## Boomi Transformations and Business Logic Explained

    ### Document Properties Logic
    For each Document Property component, provide detailed analysis:
    1. **Purpose**: Explain what business logic this property implements
    2. **Data Sources**: Document all external API calls and data lookups
    3. **Calculations**: Explain date calculations, string concatenations, and formulas
    4. **Dependencies**: List all external systems and APIs being called
    5. **Example Values**: Show sample inputs and outputs

    ### Map Transformations
    For each Map component in the flow:
    1. **Source Format**: Document the input data structure and format
    2. **Target Format**: Document the output data structure and format
    3. **Field Mappings**: Show detailed field-to-field mappings
    4. **Function Steps**: Explain any calculation or transformation functions
    5. **Default Values**: Document all default values and constants
    6. **Business Rules**: Explain the business logic behind the mappings

    ### Connector Operations
    For each Connector Action:
    1. **Operation Type**: Explain the specific operation (create, query, update, etc.)
    2. **Data Format**: Document request and response formats
    3. **Field Mappings**: Show how data is mapped to/from the external system
    4. **Error Handling**: Document error scenarios and handling logic

    ## SAP Integration Suite Implementation
    ### Component Mapping
    Map each Dell Boomi component to its SAP Integration Suite equivalent using this comprehensive mapping:

    **Boomi Core Components:**
    - Start Event (Web Services Server) → HTTPS Adapter (Receiver) or Timer Start Event
    - Start Event (File Listener) → File Adapter (Receiver) or Timer Start Event
    - Document Properties → Content Modifier (with Groovy Script for complex logic)
    - Map Component → Message Mapping or Groovy Script
    - Connector Action (HTTP) → HTTPS Adapter (Sender) or Request Reply
    - Connector Action (Salesforce) → Salesforce Adapter
    - Connector Action (Database) → JDBC Adapter
    - Stop Event → End Message Event
    - Decision Shape → Router (Exclusive Gateway)
    - Try/Catch → Exception Subprocess

    **Boomi Data Processing Components:**
    - Document Properties (with HTTP calls) → Content Modifier + Request Reply pattern
    - Document Properties (simple) → Content Modifier (Set Property)
    - Map with Function Steps → Message Mapping with complex transformations
    - XSLT Map → XSLT Mapping
    - Business Rules → Router with conditions
    - Data Process → Groovy Script or Content Modifier

    **Boomi Connector Components:**
    - Web Services Server → HTTPS Adapter (Receiver)
    - HTTP Client → HTTPS Adapter (Sender) or Request Reply
    - Salesforce Connector → Salesforce Adapter
    - Database Connector → JDBC Adapter
    - File Connector → File Adapter
    - FTP/SFTP Connector → SFTP Adapter
    - Email Connector → Mail Adapter

    **Gateway Components:**
    - Parallel Processing → Sequential Multicast or Parallel Multicast
    - Join → Join (Parallel Gateway)
    - Exclusive Choice → Router (Exclusive Gateway)

    **Splitter Components:**
    - EDI Splitter → EDI Splitter
    - IDoc Splitter → IDoc Splitter
    - General Splitter → General Splitter

    **Storage Components:**
    - Database Select → Select (DB Storage)
    - Database Write → Write (DB Storage)
    - Database Get → Get (DB Storage)
    - Persist → Persist
    - ID Mapping → ID Mapping

    **Converter Components:**
    - JSON to XML → JSON to XML Converter
    - XML to CSV → XML to CSV Converter
    - CSV to XML → CSV to XML Converter
    - XML to JSON → XML to JSON Converter
    - Base64 Encode → Base64 Encoder
    - Base64 Decode → Base64 Decoder

    **Event Components:**
    - Timer/Scheduler → Timer Start Event
    - Error End → Error End Event
    - Process Call → Process Call

    **Aggregation Components:**
    - Aggregator → Aggregator
    - Gather → Gather

    **EDI Components:**
    - EDI Extractor → EDI Extractor
    - EDI Validator → EDI Validator

    **Adapter Components:**
    - OData Connector → OData Adapter
    - SFTP Connector → SFTP Adapter
    - SuccessFactors Connector → SuccessFactors Adapter
    - Salesforce Connector → Salesforce Adapter

    For each component mapping:
    - List each source component and its direct equivalent
    - Preserve the same connection types and patterns
    - Note any components that need configuration decisions
    - Document any potential gaps or differences in functionality

    ### Integration Flow Visualization

    IMPORTANT VISUALIZATION INSTRUCTIONS:
    1. Analyze the complexity of the flows first. If there are multiple complex flows with many endpoints, create separate diagrams. If the flows are simple or closely related, combine them into a single comprehensive diagram.
    2. Each diagram should have a clear, descriptive heading (e.g., "## Order Processing Flow Diagram" or "## Customer Data Integration Flow")
    3. For each diagram, provide a brief introduction explaining what the diagram represents
    4. Use your judgment to determine if multiple diagrams are needed - prefer fewer, more comprehensive diagrams when possible
    5. If you create multiple diagrams, ensure they are logically grouped and clearly labeled
    6. FOLLOW THE EXAMPLE DIAGRAM STRUCTURE PROVIDED BELOW - it shows the correct syntax and formatting

    Create a Mermaid diagram that accurately represents the flows, components, and connections found in the original MuleSoft application. The diagram should follow this format:
    **IMPORTANT**: Your model output must be *only* the Mermaid code block (```mermaid …```) with no shell prompts or extra text.
    Use real line breaks in labels (or `<br/>`), not `\n` literals.

    STUDY THE EXAMPLE DIAGRAM BELOW CAREFULLY - it shows the proper way to structure your diagram with correct syntax for nodes, connections, subgraphs, and styling.

    ```mermaid
    flowchart TD
    %% Define node styles
    classDef httpAdapter fill:#87CEEB,stroke:#333,stroke-width:2px
    classDef contentModifier fill:#98FB98,stroke:#333,stroke-width:2px
    classDef router fill:#FFB6C1,stroke:#333,stroke-width:2px
    classDef mapping fill:#DDA0DD,stroke:#333,stroke-width:2px
    classDef exception fill:#FFA07A,stroke:#333,stroke-width:2px
    classDef processCall fill:#F0E68C,stroke:#333,stroke-width:2px

    %% Example diagram structure (use this as a reference)
    %% Start([Start]) --> HttpListener[HTTP Listener /products]
    %% HttpListener --> GetProductsFlow[Get Products Flow]
    %% GetProductsFlow --> ResponseHeaders[Set Response Headers]
    %% ResponseHeaders --> End([End])

    %% %% Error Handling
    %% HttpListener -->|Error| ErrorHandler[(Global Error Handler)]
    %% ErrorHandler --> SetErrorResponse[Set Error Response]
    %% SetErrorResponse --> SetErrorHeaders[Set Error Headers]
    %% SetErrorHeaders --> ErrorEnd([Error End])

    %% %% Get Products Flow
    %% subgraph GetProductDetailsFlow["Get Product Details Flow"]
    %%     SubflowStart([Subflow Start]) --> ValidateProduct[Validate Product ID\nGroovy Script]
    %%     ValidateProduct --> ProductRouter{"Is Valid Product?"}
    %%     ProductRouter -->|Yes| LogValidRequest[Log Valid Request]
    %%     LogValidRequest --> BuildODataQuery[Build OData Query\n$filter and $select]
    %%     BuildODataQuery --> ODataRequest[OData Request to SAP HANA]
    %%     ODataRequest --> TransformResponse[Transform Response\nto JSON]
    %%
    %%     ProductRouter -->|No| LogInvalidRequest[Log Invalid Request]
    %%     LogInvalidRequest --> BuildErrorResponse[Build Error Response\nPRODUCT_NOT_FOUND]
    %% end

    %% YOUR ACTUAL DIAGRAM NODES AND CONNECTIONS GO HERE
    %% DO NOT INDENT THE FENCES — they must start at column 0

    ```

    %% Add styling
    class HttpListener,ODataRequest httpAdapter
    class ResponseHeaders,LogValidRequest,LogInvalidRequest,SetErrorResponse,SetErrorHeaders contentModifier
    class ProductRouter router
    class ValidateProduct,BuildODataQuery,TransformResponse,BuildErrorResponse mapping
    class ErrorHandler exception
    class GetProductsFlow processCall

    IMPORTANT MERMAID DIAGRAM RULES:
    1. Use TD (top-down) direction
    2. Include ALL style definitions exactly as shown above
    3. Group related flows with %% comments
    4. Use these exact node shapes:
       - ((name)) for Start/End events
       - [name] for regular components
       - {"name"} for routers (IMPORTANT: use quotes inside the curly braces)
       - [[name]] for process calls
       - [(name)] for error handlers
    5. Use these exact style classes:
       - :::httpAdapter for HTTP components
       - :::contentModifier for content modifiers
       - :::router for routers
       - :::mapping for transformations
       - :::exception for error handlers
       - :::processCall for process calls
    6. Use -->|label| for labeled connections
    7. Keep error handlers grouped together
    8. Maintain exact spacing and indentation as shown
    9. AVOID using special characters like curly braces in node labels or connection labels - replace path parameters like '{{accountId}}' with plain text (e.g., 'accountId')
    10. For API endpoints with path parameters, use simplified formats like '/InvestmentAccounts/accountId/Retrieve' instead of '/InvestmentAccounts/{{accountId}}/Retrieve'

    ### Configuration Details
    For each component in the visualization, provide detailed configuration:

    **Start Message Event:**
    - Trigger type (webhook, timer, file, etc.)
    - Protocol and authentication settings
    - Endpoint URLs and paths
    - Security configurations

    **Content Modifier (for Document Properties):**
    - Properties to set with their calculation logic
    - External API calls and their configurations
    - HTTP endpoints, methods, and authentication
    - Date calculation formulas and string concatenation logic
    - Error handling for external calls

    **Message Mapping (for Map components):**
    - Source and target message formats
    - Field mapping details with transformations
    - Function logic and calculations
    - Default values and constants
    - Conditional mapping rules

    **Request Reply or Adapters (for Connector Actions):**
    - Connection settings and authentication
    - Operation types and parameters
    - Request/response formats
    - Error handling and retry logic
    - Timeout and performance settings

    ## Environment Configuration
    Provide a comprehensive breakdown of all configuration details based on the Boomi process analysis:

    **External System Dependencies:**
    - List all HTTP/API endpoints called by Document Properties
    - Authentication requirements for each external system
    - API keys, certificates, and security configurations
    - Rate limiting and timeout considerations

    **Connector Configurations:**
    - Salesforce connection settings (URLs, API versions, authentication)
    - Database connection details (if applicable)
    - File system paths and permissions (if applicable)
    - Web service endpoints and WSDL locations

    **Process Properties and Variables:**
    - Document properties and their calculation logic
    - Environment-specific values and configurations
    - Dynamic property dependencies and relationships

    **Security Settings:**
    - TLS/SSL certificates for secure communication
    - API key storage and credential management
    - IP restrictions and firewall configurations

    **Deployment Considerations:**
    - High availability setup for production
    - Monitoring for integration health and external API calls
    - Alert configuration for failed integrations and API timeouts
    - Performance tuning for external API calls and data processing

    **Required Resources:**
    - Memory requirements based on data volume and external calls
    - CPU requirements for transformation and API processing
    - Network bandwidth for external API communications
    - Disk space for logging and temporary data storage

    ## API Reference
    Create a detailed API reference section that includes:
    - Complete list of all endpoints with their HTTP methods
    - Request and response schemas for each endpoint
    - Authentication requirements
    - Error codes and their meanings
    - Rate limiting information
    - Pagination details (if applicable)
    - Versioning information

    Make sure the final document has:
    1. A descriptive title that reflects the purpose of the API/integration
    2. A comprehensive table of contents with hyperlinks to all sections
    3. Clear headings and subheadings for all sections
    4. Properly labeled diagrams with descriptive headings
    5. Complete API details including endpoints, parameters, and examples
    6. Detailed environment configuration information"""

        # Try to enhance with selected service
        enhanced_content = None
        if self.selected_service == 'openai':
            enhanced_content = self.enhance_with_openai(prompt)
            if enhanced_content:
                logger.info("Enhancement with OpenAI was successful")
                enhancement_successful = True
            else:
                logger.warning("Enhancement with OpenAI failed")

        elif self.selected_service in ['anthropic', 'claude']:  # Accept both names for compatibility
            enhanced_content = self.enhance_with_anthropic(prompt)
            if enhanced_content:
                logger.info("Enhancement with Anthropic was successful")
                enhancement_successful = True
            else:
                logger.warning("Enhancement with Anthropic failed")

        # Return enhanced content or original if enhancement fails
        if not enhancement_successful:
            logger.warning("LLM enhancement was not successful. Returning original documentation.")
            return base_documentation

        return enhanced_content

    def enhance_with_openai(self, prompt: str) -> Optional[str]:
        """Enhance documentation using OpenAI.

        Args:
            prompt: Prompt for OpenAI

        Returns:
            Enhanced documentation or None if failed
        """
        if not self.openai_client:
            logger.warning("OpenAI client not available. Cannot enhance documentation.")
            return None

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",  # Can be configured based on needs
                messages=[
                    {"role": "system", "content": "You are an expert integration specialist helping convert MuleSoft applications to SAP Integration Suite."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,
                max_tokens=18000,
                timeout=600  # Set timeout to 600 seconds (increased from 300)
            )

            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error using OpenAI for enhancement: {str(e)}")
            return None

    def enhance_with_anthropic(self, prompt: str) -> Optional[str]:
        """Enhance documentation using Anthropic Claude.

        Args:
            prompt: Prompt for Claude

        Returns:
            Enhanced documentation or None if failed
        """
        if not self.anthropic_client:
            logger.error("Anthropic client not available. Cannot enhance documentation.")
            logger.error(f"API Key available: {bool(self.anthropic_api_key)}")
            logger.error(f"Anthropic module available: {bool(anthropic)}")
            return None

        try:
            # Log the API call attempt with prompt size
            logger.info(f"Starting Anthropic Claude API call with prompt size: {len(prompt)} characters")
            logger.info(f"Using model: claude-3-7-sonnet-20250219 with timeout: 600 seconds")
            logger.info(f"API Key (first 5 chars): {self.anthropic_api_key[:5]}...")

            import time
            start_time = time.time()

            try:
                # Use the Anthropic Messages API with the newer format
                response = self.anthropic_client.messages.create(
                    model="claude-3-7-sonnet-20250219",  # Using the latest model
                    max_tokens=20000,
                    temperature=0.2,
                    timeout=600,  # Set timeout to 600 seconds (increased from 300)
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": prompt
                                }
                            ]
                        }
                    ]
                )
            except Exception as api_error:
                logger.error(f"Anthropic API call failed with error: {str(api_error)}")
                logger.error(f"Error type: {type(api_error).__name__}")

                # Try with a different model as fallback
                logger.info("Trying fallback to claude-3-opus-20240229 model...")
                try:
                    response = self.anthropic_client.messages.create(
                        model="claude-3-opus-20240229",
                        max_tokens=20000,
                        temperature=0.2,
                        timeout=600,
                        messages=[
                            {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": prompt
                                    }
                                ]
                            }
                        ]
                    )
                    logger.info("Fallback to claude-3-opus-20240229 model succeeded")
                except Exception as fallback_error:
                    logger.error(f"Fallback API call also failed: {str(fallback_error)}")
                    raise fallback_error

            elapsed_time = time.time() - start_time
            logger.info(f"Anthropic API call completed in {elapsed_time:.2f} seconds")

            # Get the content from the response - extract text from the first content item
            if hasattr(response, 'content') and len(response.content) > 0:
                # Check if content is a list with text items
                for item in response.content:
                    if hasattr(item, 'text'):
                        logger.info(f"Successfully extracted content from response, length: {len(item.text)} characters")
                        return item.text

                # Fallback for other response structures
                logger.warning("Unexpected response structure. Attempting to extract text directly.")
                if hasattr(response.content[0], 'text'):
                    logger.info(f"Extracted text directly from content[0], length: {len(response.content[0].text)} characters")
                    return response.content[0].text

                # Try to extract content as a string if it's not an object with a text attribute
                logger.warning("Could not find text attribute. Trying to convert content to string.")
                try:
                    content_str = str(response.content[0])
                    logger.info(f"Converted content to string, length: {len(content_str)} characters")
                    return content_str
                except Exception as str_error:
                    logger.error(f"Error converting content to string: {str(str_error)}")

            # Log the full response structure for debugging
            logger.warning("Could not extract text from Anthropic response")
            logger.warning(f"Response type: {type(response)}")
            logger.warning(f"Response attributes: {dir(response)}")
            logger.warning(f"Response content type: {type(response.content) if hasattr(response, 'content') else 'No content attribute'}")

            return None

        except Exception as e:
            logger.error(f"Error using Anthropic Claude for enhancement: {str(e)}")
            logger.error(f"Error type: {type(e).__name__}")
            logger.error(f"Error traceback: {e.__traceback__}")
            return None