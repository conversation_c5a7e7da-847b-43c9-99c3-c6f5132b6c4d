{"process_name": "Create Salesforce Opportunities from Stripe Subscriptions", "description": "This integration creates Salesforce Opportunities based on Stripe Subscription data. It listens for Stripe webhook events, processes the subscription data, and creates corresponding opportunity records in Salesforce.", "endpoints": [{"method": "POST", "path": "/stripe-webhook", "purpose": "Receives subscription events from Stripe and creates Salesforce Opportunities", "components": [{"type": "enricher", "name": "Set_Dynamic_Properties", "id": "enricher_1", "config": {"content": "{\n  \"salesforceApiUrl\": \"${property.salesforce_api_url}\",\n  \"salesforceApiVersion\": \"${property.salesforce_api_version}\",\n  \"salesforceAuthToken\": \"${property.salesforce_auth_token}\"\n}"}}, {"type": "request_reply", "name": "Create_Salesforce_Opportunity", "id": "request_reply_1", "config": {"endpoint_path": "/services/data/${property.salesforce_api_version}/sobjects/Opportunity"}}, {"type": "groovy_script", "name": "Transform_Stripe_To_Salesforce", "id": "transform_1", "config": {"script": "TransformStripeToSalesforce.groovy"}}], "error_handling": {"exception_subprocess": [{"type": "enricher", "name": "Set_Error_Message", "id": "error_enricher_1", "trigger": "connection_error", "config": {"content": "{\n  \"errorMessage\": \"Failed to create Salesforce Opportunity\",\n  \"errorDetails\": ${exception.message},\n  \"timestamp\": ${date:now()}\n}"}}, {"type": "groovy_script", "name": "Log_Error_Details", "id": "error_script_1", "trigger": "any_error", "config": {"script": "LogErrorDetails.groovy"}}]}, "branching": {"type": "exclusive", "branches": [{"condition": "Default path", "components": ["enricher_1", "transform_1", "request_reply_1"], "sequence": ["enricher_1", "transform_1", "request_reply_1"]}]}, "sequence": ["enricher_1", "transform_1", "request_reply_1"], "transformations": [{"name": "TransformStripeToSalesforce.groovy", "type": "groovy", "script": "import groovy.json.*\n\ndef inputBody = message.getBody(String.class)\ndef stripeData = new JsonSlurper().parseText(inputBody)\n\n// Create Salesforce Opportunity object\ndef opportunity = [:]\n\n// Map Stripe subscription fields to Salesforce Opportunity fields\nopportunity.Name = \"Subscription: \" + stripeData.id\nopportunity.Amount = stripeData.items.data[0].price.unit_amount / 100 // Convert cents to dollars\nopportunity.CloseDate = new Date(stripeData.current_period_start * 1000).format(\"yyyy-MM-dd\")\n\n// Set Opportunity stage based on subscription status\nswitch(stripeData.status) {\n    case \"active\":\n        opportunity.StageName = \"Closed Won\"\n        break\n    case \"trialing\":\n        opportunity.StageName = \"Proposal/Price Quote\"\n        break\n    case \"incomplete\":\n    case \"incomplete_expired\":\n        opportunity.StageName = \"Qualification\"\n        break\n    case \"past_due\":\n    case \"unpaid\":\n        opportunity.StageName = \"Negotiation/Review\"\n        break\n    case \"canceled\":\n        opportunity.StageName = \"Closed Lost\"\n        break\n    default:\n        opportunity.StageName = \"Prospecting\"\n}\n\n// Add Stripe reference fields\nopportunity.Description = \"Created from Stripe subscription \" + stripeData.id\nopportunity.Stripe_Subscription_ID__c = stripeData.id\nopportunity.Stripe_Customer_ID__c = stripeData.customer\n\n// Set AccountId - In a real scenario, you would look up the Account ID based on the Stripe customer ID\n// For this example, we're assuming a property has been set with the Account ID\nopportunity.AccountId = property.get(\"salesforce_account_id\")\n\n// Convert to JSON and set as message body\nmessage.setBody(new JsonBuilder(opportunity).toString())\nreturn message"}, {"name": "LogErrorDetails.groovy", "type": "groovy", "script": "import groovy.json.*\n\ndef errorDetails = [:]\nerrorDetails.timestamp = new Date().format(\"yyyy-MM-dd'T'HH:mm:ss.SSSZ\")\nerrorDetails.errorMessage = property.get(\"errorMessage\")\nerrorDetails.errorDetails = property.get(\"errorDetails\")\nerrorDetails.processName = \"Create Salesforce Opportunities from Stripe Subscriptions\"\n\n// Log error details\nprintln \"ERROR: \" + new JsonBuilder(errorDetails).toString()\n\n// You could also send the error to a monitoring system or error queue\n// For this example, we're just returning the error details\nreturn message"}], "sequence_flows": [{"id": "SequenceFlow_Start", "source": "enricher_1", "target": "request_reply_1", "is_immediate": true, "xml_content": "<bpmn2:sequenceFlow id=\"SequenceFlow_Start\" sourceRef=\"enricher_1\" targetRef=\"request_reply_1\" isImmediate=\"true\"/>"}, {"id": "SequenceFlow_End", "source": "request_reply_1", "target": "transform_1", "is_immediate": true, "xml_content": "<bpmn2:sequenceFlow id=\"SequenceFlow_End\" sourceRef=\"request_reply_1\" targetRef=\"transform_1\" isImmediate=\"true\"/>"}]}]}