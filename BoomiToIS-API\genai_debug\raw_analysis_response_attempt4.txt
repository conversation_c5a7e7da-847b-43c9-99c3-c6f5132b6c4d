{"process_name": "SAP SuccessFactors to SFTP Integration", "description": "Integration solution that connects SAP SuccessFactors with an SFTP server for automated transfer of employee data with error handling", "endpoints": [{"method": "GET", "path": "/employee-data", "purpose": "Retrieve employee data from SuccessFactors and transfer to SFTP server", "components": [{"type": "request_reply", "name": "Get_SuccessFactors_Employee_Data", "id": "successfactors_request", "config": {"endpoint_path": "/odata/v2/Employee", "address": "${successFactors_api_url}"}}, {"type": "groovy_script", "name": "Validate_Employee_Data", "id": "data_validation", "config": {"script": "ValidateEmployeeData.groovy"}}, {"type": "groovy_script", "name": "Transform_Canonical_To_Kafka_Avro", "id": "data_transformation", "config": {"script": "CanonicalToKafkaAvro.groovy"}}, {"type": "enricher", "name": "Prepare_File_For_SFTP", "id": "prepare_sftp_file", "config": {"content": "${property.fileName}"}}, {"type": "request_reply", "name": "Upload_To_SFTP", "id": "sftp_upload", "config": {"endpoint_path": "${sftp_directory}/${property.fileName}"}}], "error_handling": {"exception_subprocess": [{"type": "enricher", "name": "Log_Error_Details", "id": "log_error", "trigger": "any_error", "config": {"content": "Error occurred during integration: ${exception.message}"}}, {"type": "request_reply", "name": "Send_Error_Notification", "id": "send_notification", "trigger": "any_error", "config": {"endpoint_path": "/mail"}}]}, "branching": {"type": "exclusive", "branches": [{"condition": "${body.valid == true}", "components": ["data_transformation", "prepare_sftp_file", "sftp_upload"], "sequence": ["data_transformation", "prepare_sftp_file", "sftp_upload"]}, {"condition": "${body.valid == false}", "components": ["log_error", "send_notification"], "sequence": ["log_error", "send_notification"]}]}, "sequence": ["successfactors_request", "data_validation", "data_transformation", "prepare_sftp_file", "sftp_upload"], "transformations": [{"name": "ValidateEmployeeData.groovy", "type": "groovy", "script": "import com.sap.gateway.ip.core.customdev.util.Message;\n\ndef Message processData(Message message) {\n    def body = message.getBody(java.lang.String);\n    def properties = message.getProperties();\n    \n    // Validate the employee data structure\n    def valid = true;\n    def validationErrors = [];\n    \n    // Add validation logic here\n    // Example: Check if required fields exist\n    if (!body.contains(\"username\")) {\n        valid = false;\n        validationErrors.add(\"Missing username field\");\n    }\n    \n    // Set validation results as properties\n    properties.put(\"valid\", valid);\n    properties.put(\"validationErrors\", validationErrors);\n    \n    return message;\n}"}, {"name": "CanonicalToKafkaAvro.groovy", "type": "groovy", "script": "import com.sap.gateway.ip.core.customdev.util.Message;\nimport groovy.json.*;\n\ndef Message processData(Message message) {\n    def body = message.getBody(java.lang.String);\n    def jsonParser = new JsonSlurper();\n    def jsonBuilder = new JsonBuilder();\n    \n    try {\n        def parsedData = jsonParser.parseText(body);\n        \n        // Transform data according to Canonical To Kafka Avro mapping\n        def transformedData = jsonBuilder {\n            Object {\n                batchProcessingDirectives {\n                    Object {\n                        accountID {\n                            Object {\n                                username parsedData.field9 // Username mapping\n                            }\n                        }\n                        batchProcessingOption {\n                            Array {\n                                ArrayElement1 {\n                                    Object {\n                                        name parsedData.field118 // Batch processing option name\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n                batchContactList {\n                    Array {\n                        ArrayElement1 {\n                            Object {\n                                contact {\n                                    Array {\n                                        ArrayElement1 {\n                                            Object {\n                                                contactID parsedData.field91 // Contact ID mapping\n                                                contactPointList {\n                                                    Array {\n                                                        ArrayElement1 {\n                                                            Object {\n                                                                contactPoint {\n                                                                    Array {\n                                                                        ArrayElement1 {\n                                                                            Object {\n                                                                                type parsedData.field111 // Contact point type\n                                                                            }\n                                                                        }\n                                                                    }\n                                                                }\n                                                            }\n                                                        }\n                                                    }\n                                                }\n                                            }\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        \n        // Generate a filename for SFTP upload\n        def timestamp = new Date().format(\"yyyyMMdd-HHmmss\");\n        def fileName = \"employee_data_\" + timestamp + \".json\";\n        message.setProperty(\"fileName\", fileName);\n        \n        // Set the transformed data as the message body\n        message.setBody(transformedData.toString());\n    } catch (Exception e) {\n        message.setProperty(\"error\", e.getMessage());\n        throw e;\n    }\n    \n    return message;\n}"}]}]}