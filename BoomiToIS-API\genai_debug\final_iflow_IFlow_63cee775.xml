<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:ifl="http:///com.sap.ifl.model/Ifl.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1">
    <bpmn2:collaboration id="Collaboration_1" name="Default Collaboration">
        <bpmn2:extensionElements>
            <ifl:property>
                <key>namespaceMapping</key>
                <value />
            </ifl:property>
            <ifl:property>
                <key>httpSessionHandling</key>
                <value>None</value>
            </ifl:property>
            <ifl:property>
                <key>returnExceptionToSender</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>log</key>
                <value>All events</value>
            </ifl:property>
            <ifl:property>
                <key>corsEnabled</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>componentVersion</key>
                <value>1.2</value>
            </ifl:property>
            <ifl:property>
                <key>ServerTrace</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>xsrfProtection</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>cmdVariantUri</key>
                <value>ctype::IFlowVariant/cname::IFlowConfiguration/version::1.2.4</value>
            </ifl:property>
        </bpmn2:extensionElements>
<bpmn2:participant id="Participant_1" ifl:type="EndpointSender" name="Sender">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>enableBasicAuthentication</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>ifl:type</key>
                    <value>EndpointSender</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:participant>
<bpmn2:participant id="Participant_Process_1" ifl:type="IntegrationProcess" name="Integration Process" processRef="Process_1">
            <bpmn2:extensionElements />
        </bpmn2:participant>
<bpmn2:participant id="Participant_OData_root" ifl:type="EndpointReceiver" name="InboundProduct_root">
                    <bpmn2:extensionElements>
                        <ifl:property>
                            <key>ifl:type</key>
                            <value>EndpointReceiver</value>
                        </ifl:property>
                    </bpmn2:extensionElements>
                </bpmn2:participant>
<bpmn2:messageFlow id="MessageFlow_10" name="HTTPS" sourceRef="Participant_1" targetRef="StartEvent_2">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>ComponentType</key>
                    <value>HTTPS</value>
                </ifl:property>
                <ifl:property>
                    <key>Description</key>
                    <value />
                </ifl:property>
                <ifl:property>
                    <key>maximumBodySize</key>
                    <value>40</value>
                </ifl:property>
                <ifl:property>
                    <key>ComponentNS</key>
                    <value>sap</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.4</value>
                </ifl:property>
                <ifl:property>
                    <key>urlPath</key>
                    <value>/test</value>
                </ifl:property>
                <ifl:property>
                    <key>TransportProtocolVersion</key>
                    <value>1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>xsrfProtection</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>TransportProtocol</key>
                    <value>HTTPS</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::AdapterVariant/cname::sap:HTTPS/tp::HTTPS/mp::None/direction::Sender/version::1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>userRole</key>
                    <value>ESBMessaging.send</value>
                </ifl:property>
                <ifl:property>
                    <key>senderAuthType</key>
                    <value>RoleBased</value>
                </ifl:property>
                <ifl:property>
                    <key>MessageProtocol</key>
                    <value>None</value>
                </ifl:property>
                <ifl:property>
                    <key>MessageProtocolVersion</key>
                    <value>1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>direction</key>
                    <value>Sender</value>
                </ifl:property>
                <ifl:property>
                    <key>clientCertificates</key>
                    <value />
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:messageFlow>
<bpmn2:messageFlow id="MessageFlow_OData_root" name="OData" sourceRef="ServiceTask_OData_root" targetRef="Participant_OData_root">
                        <bpmn2:extensionElements>
                            <ifl:property>
                                <key>Description</key>
                                <value />
                            </ifl:property>
                            <ifl:property>
                                <key>pagination</key>
                                <value>0</value>
                            </ifl:property>
                            <ifl:property>
                                <key>odataCertAuthPrivateKeyAlias</key>
                                <value />
                            </ifl:property>
                            <ifl:property>
                                <key>ComponentNS</key>
                                <value>sap</value>
                            </ifl:property>
                            <ifl:property>
                                <key>resourcePath</key>
                                <value>${property.ProductsResourcePath}</value>
                            </ifl:property>
                            <ifl:property>
                                <key>customQueryOptions</key>
                                <value />
                            </ifl:property>
                            <ifl:property>
                                <key>Name</key>
                                <value>OData</value>
                            </ifl:property>
                            <ifl:property>
                                <key>TransportProtocolVersion</key>
                                <value>1.8.0</value>
                            </ifl:property>
                            <ifl:property>
                                <key>ComponentSWCVName</key>
                                <value>external</value>
                            </ifl:property>
                            <ifl:property>
                                <key>proxyPort</key>
                                <value />
                            </ifl:property>
                            <ifl:property>
                                <key>receiveTimeOut</key>
                                <value>{{Timeout}}</value>
                            </ifl:property>
                            <ifl:property>
                                <key>alias</key>
                                <value>{{Destination Credential}}</value>
                            </ifl:property>
                            <ifl:property>
                                <key>contentType</key>
                                <value>application/json</value>
                            </ifl:property>
                            <ifl:property>
                                <key>MessageProtocol</key>
                                <value>OData V2</value>
                            </ifl:property>
                            <ifl:property>
                                <key>ComponentSWCVId</key>
                                <value>1.8.0</value>
                            </ifl:property>
                            <ifl:property>
                                <key>direction</key>
                                <value>Receiver</value>
                            </ifl:property>
                            <ifl:property>
                                <key>scc_location_id</key>
                                <value>{{Location ID}}</value>
                            </ifl:property>
                            <ifl:property>
                                <key>ComponentType</key>
                                <value>HCIOData</value>
                            </ifl:property>
                            <ifl:property>
                                <key>address</key>
                                <value>{{Destination Host}}/{{Products Service URL}}</value>
                            </ifl:property>
                            <ifl:property>
                                <key>queryOptions</key>
                                <value />
                            </ifl:property>
                            <ifl:property>
                                <key>proxyType</key>
                                <value>{{Destination Proxy Type}}</value>
                            </ifl:property>
                            <ifl:property>
                                <key>componentVersion</key>
                                <value>1.8</value>
                            </ifl:property>
                            <ifl:property>
                                <key>proxyHost</key>
                                <value />
                            </ifl:property>
                            <ifl:property>
                                <key>edmxFilePath</key>
                                <value>edmx/Products.edmx</value>
                            </ifl:property>
                            <ifl:property>
                                <key>odatapagesize</key>
                                <value>200</value>
                            </ifl:property>
                            <ifl:property>
                                <key>system</key>
                                <value>InboundProduct</value>
                            </ifl:property>
                            <ifl:property>
                                <key>authenticationMethod</key>
                                <value>{{Destination Authentication}}</value>
                            </ifl:property>
                            <ifl:property>
                                <key>enableBatchProcessing</key>
                                <value>1</value>
                            </ifl:property>
                            <ifl:property>
                                <key>TransportProtocol</key>
                                <value>HTTP</value>
                            </ifl:property>
                            <ifl:property>
                                <key>cmdVariantUri</key>
                                <value>ctype::AdapterVariant/cname::sap:HCIOData/tp::HTTP/mp::OData V2/direction::Receiver/version::1.8.0</value>
                            </ifl:property>
                            <ifl:property>
                                <key>fields</key>
                                <value>ID,Name,Description,Price</value>
                            </ifl:property>
                            <ifl:property>
                                <key>characterEncoding</key>
                                <value>none</value>
                            </ifl:property>
                            <ifl:property>
                                <key>operation</key>
                                <value>Create(POST)</value>
                            </ifl:property>
                            <ifl:property>
                                <key>MessageProtocolVersion</key>
                                <value>1.8.0</value>
                            </ifl:property>
                        </bpmn2:extensionElements>
                    </bpmn2:messageFlow>
    </bpmn2:collaboration>
    <bpmn2:process id="Process_1" name="Integration Process">
        <bpmn2:extensionElements>
            <ifl:property>
                <key>transactionTimeout</key>
                <value>30</value>
            </ifl:property>
            <ifl:property>
                <key>componentVersion</key>
                <value>1.2</value>
            </ifl:property>
            <ifl:property>
                <key>cmdVariantUri</key>
                <value>ctype::FlowElementVariant/cname::IntegrationProcess/version::1.2.1</value>
            </ifl:property>
            <ifl:property>
                <key>transactionalHandling</key>
                <value>Not Required</value>
            </ifl:property>
        </bpmn2:extensionElements>
        
            <bpmn2:startEvent id="StartEvent_2" name="Start">
                <bpmn2:extensionElements>
                    <ifl:property>
                        <key>componentVersion</key>
                        <value>1.0</value>
                    </ifl:property>
                    <ifl:property>
                        <key>cmdVariantUri</key>
                        <value>ctype::FlowstepVariant/cname::MessageStartEvent/version::1.0</value>
                    </ifl:property>
                </bpmn2:extensionElements>
                <bpmn2:outgoing>SequenceFlow_Start</bpmn2:outgoing>
                <bpmn2:messageEventDefinition id="MessageEventDefinition_StartEvent_2" />
            </bpmn2:startEvent>
            <bpmn2:callActivity id="JSONtoXMLConverter_root" name="JSONtoXMLConverter_root">
                        <bpmn2:extensionElements>
                            <ifl:property>
                                <key>additionalRootElementName</key>
                                <value>root</value>
                            </ifl:property>
                            <ifl:property>
                                <key>jsonNamespaceMapping</key>
                                <value />
                            </ifl:property>
                            <ifl:property>
                                <key>componentVersion</key>
                                <value>1.1</value>
                            </ifl:property>
                            <ifl:property>
                                <key>activityType</key>
                                <value>JsonToXmlConverter</value>
                            </ifl:property>
                            <ifl:property>
                                <key>cmdVariantUri</key>
                                <value>ctype::FlowstepVariant/cname::JsonToXmlConverter/version::1.1.2</value>
                            </ifl:property>
                            <ifl:property>
                                <key>useNamespaces</key>
                                <value>true</value>
                            </ifl:property>
                            <ifl:property>
                                <key>addXMLRootElement</key>
                                <value>true</value>
                            </ifl:property>
                            <ifl:property>
                                <key>additionalRootElementNamespace</key>
                                <value />
                            </ifl:property>
                            <ifl:property>
                                <key>jsonNamespaceSeparator</key>
                                <value>:</value>
                            </ifl:property>
                        </bpmn2:extensionElements>
                        <bpmn2:incoming>SequenceFlow_Start_root</bpmn2:incoming>
                        <bpmn2:outgoing>SequenceFlow_1_root</bpmn2:outgoing>
                    </bpmn2:callActivity>
            <bpmn2:callActivity id="ContentEnricher_root_1" name="PrepareRequest_root">
                        <bpmn2:extensionElements>
                            <ifl:property>
                                <key>bodyType</key>
                                <value>expression</value>
                            </ifl:property>
                            <ifl:property>
                                <key>propertyTable</key>
                                <value>&lt;row&gt;&lt;cell id='Action'&gt;Create&lt;/cell&gt;&lt;cell id='Type'&gt;constant&lt;/cell&gt;&lt;cell id='Value'&gt;GET&lt;/cell&gt;&lt;cell id='Default'&gt;&lt;/cell&gt;&lt;cell id='Name'&gt;HTTP_METHOD&lt;/cell&gt;&lt;cell id='Datatype'&gt;java.lang.String&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell id='Action'&gt;Create&lt;/cell&gt;&lt;cell id='Type'&gt;constant&lt;/cell&gt;&lt;cell id='Value'&gt;_&lt;/cell&gt;&lt;cell id='Default'&gt;&lt;/cell&gt;&lt;cell id='Name'&gt;HTTP_PATH&lt;/cell&gt;&lt;cell id='Datatype'&gt;java.lang.String&lt;/cell&gt;&lt;/row&gt;</value>
                            </ifl:property>
                            <ifl:property>
                                <key>headerTable</key>
                                <value>&lt;row&gt;&lt;cell id='Action'&gt;Create&lt;/cell&gt;&lt;cell id='Type'&gt;constant&lt;/cell&gt;&lt;cell id='Value'&gt;GET _&lt;/cell&gt;&lt;cell id='Default'&gt;&lt;/cell&gt;&lt;cell id='Name'&gt;RequestInfo&lt;/cell&gt;&lt;cell id='Datatype'&gt;java.lang.String&lt;/cell&gt;&lt;/row&gt;</value>
                            </ifl:property>
                            <ifl:property>
                                <key>wrapContent</key>
                                <value />
                            </ifl:property>
                            <ifl:property>
                                <key>componentVersion</key>
                                <value>1.4</value>
                            </ifl:property>
                            <ifl:property>
                                <key>activityType</key>
                                <value>Enricher</value>
                            </ifl:property>
                            <ifl:property>
                                <key>cmdVariantUri</key>
                                <value>ctype::FlowstepVariant/cname::Enricher/version::1.4.2</value>
                            </ifl:property>
                            <ifl:property>
                                <key>bodyContent</key>
                                <value>{ "request": "Preparing request" }</value>
                            </ifl:property>
                        </bpmn2:extensionElements>
                        <bpmn2:incoming>SequenceFlow_1_root</bpmn2:incoming>
                        <bpmn2:outgoing>SequenceFlow_2_root</bpmn2:outgoing>
                    </bpmn2:callActivity>
            <bpmn2:callActivity id="ContentEnricher_root_headers" name="SetRequestHeaders_root">
                        <bpmn2:extensionElements>
                            <ifl:property>
                                <key>bodyType</key>
                                <value>expression</value>
                            </ifl:property>
                            <ifl:property>
                                <key>propertyTable</key>
                                <value>&lt;row&gt;&lt;cell id='Action'&gt;Create&lt;/cell&gt;&lt;cell id='Type'&gt;constant&lt;/cell&gt;&lt;cell id='Value'&gt;GET&lt;/cell&gt;&lt;cell id='Default'&gt;&lt;/cell&gt;&lt;cell id='Name'&gt;HTTP_METHOD&lt;/cell&gt;&lt;cell id='Datatype'&gt;java.lang.String&lt;/cell&gt;&lt;/row&gt;</value>
                            </ifl:property>
                            <ifl:property>
                                <key>headerTable</key>
                                <value>&lt;row&gt;&lt;cell id='Action'&gt;Create&lt;/cell&gt;&lt;cell id='Type'&gt;constant&lt;/cell&gt;&lt;cell id='Value'&gt;GET /&lt;/cell&gt;&lt;cell id='Default'&gt;&lt;/cell&gt;&lt;cell id='Name'&gt;RequestInfo&lt;/cell&gt;&lt;cell id='Datatype'&gt;java.lang.String&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell id='Action'&gt;Create&lt;/cell&gt;&lt;cell id='Type'&gt;constant&lt;/cell&gt;&lt;cell id='Value'&gt;application/json&lt;/cell&gt;&lt;cell id='Default'&gt;&lt;/cell&gt;&lt;cell id='Name'&gt;Content-Type&lt;/cell&gt;&lt;cell id='Datatype'&gt;java.lang.String&lt;/cell&gt;&lt;/row&gt;</value>
                            </ifl:property>
                            <ifl:property>
                                <key>wrapContent</key>
                                <value />
                            </ifl:property>
                            <ifl:property>
                                <key>componentVersion</key>
                                <value>1.4</value>
                            </ifl:property>
                            <ifl:property>
                                <key>activityType</key>
                                <value>Enricher</value>
                            </ifl:property>
                            <ifl:property>
                                <key>cmdVariantUri</key>
                                <value>ctype::FlowstepVariant/cname::Enricher/version::1.4.2</value>
                            </ifl:property>
                            <ifl:property>
                                <key>bodyContent</key>
                                <value>${body}</value>
                            </ifl:property>
                        </bpmn2:extensionElements>
                        <bpmn2:incoming>SequenceFlow_2_root</bpmn2:incoming>
                        <bpmn2:outgoing>SequenceFlow_3_root</bpmn2:outgoing>
                    </bpmn2:callActivity>
            <bpmn2:serviceTask id="ServiceTask_OData_root" name="Send_root">
                    <bpmn2:extensionElements>
                        <ifl:property>
                            <key>componentVersion</key>
                            <value>1.1</value>
                        </ifl:property>
                        <ifl:property>
                            <key>activityType</key>
                            <value>ExternalCall</value>
                        </ifl:property>
                        <ifl:property>
                            <key>cmdVariantUri</key>
                            <value>ctype::FlowstepVariant/cname::ExternalCall/version::1.1.0</value>
                        </ifl:property>
                    </bpmn2:extensionElements>
                    <bpmn2:incoming>SequenceFlow_3_root</bpmn2:incoming>
                    <bpmn2:outgoing>SequenceFlow_4_root</bpmn2:outgoing>
                </bpmn2:serviceTask>
            <bpmn2:callActivity id="ErrorHandler_root" name="LogError_root">
                    <bpmn2:extensionElements>
                        <ifl:property>
                            <key>scriptFunction</key>
                            <value>processError</value>
                        </ifl:property>
                        <ifl:property>
                            <key>componentVersion</key>
                            <value>1.0</value>
                        </ifl:property>
                        <ifl:property>
                            <key>activityType</key>
                            <value>Script</value>
                        </ifl:property>
                        <ifl:property>
                            <key>cmdVariantUri</key>
                            <value>ctype::FlowstepVariant/cname::GroovyScript/version::1.0.1</value>
                        </ifl:property>
                        <ifl:property>
                            <key>subActivityType</key>
                            <value>GroovyScript</value>
                        </ifl:property>
                        <ifl:property>
                            <key>script</key>
                            <value>ErrorHandler_root.groovy</value>
                        </ifl:property>
                    </bpmn2:extensionElements>
                    <bpmn2:incoming>SequenceFlow_Error_root</bpmn2:incoming>
                    <bpmn2:outgoing>SequenceFlow_ErrorEnd_root</bpmn2:outgoing>
                </bpmn2:callActivity>
            <bpmn2:callActivity id="ContentModifier_root_response" name="SetResponseHeaders_root">
                    <bpmn2:extensionElements>
                        <ifl:property>
                            <key>bodyType</key>
                            <value>expression</value>
                        </ifl:property>
                        <ifl:property>
                            <key>propertyTable</key>
                            <value>&lt;row&gt;&lt;cell id='Action'&gt;Create&lt;/cell&gt;&lt;cell id='Type'&gt;constant&lt;/cell&gt;&lt;cell id='Value'&gt;200&lt;/cell&gt;&lt;cell id='Default'&gt;&lt;/cell&gt;&lt;cell id='Name'&gt;HTTP_STATUS_CODE&lt;/cell&gt;&lt;cell id='Datatype'&gt;java.lang.String&lt;/cell&gt;&lt;/row&gt;</value>
                        </ifl:property>
                        <ifl:property>
                            <key>headerTable</key>
                            <value>&lt;row&gt;&lt;cell id='Action'&gt;Create&lt;/cell&gt;&lt;cell id='Type'&gt;constant&lt;/cell&gt;&lt;cell id='Value'&gt;GET /&lt;/cell&gt;&lt;cell id='Default'&gt;&lt;/cell&gt;&lt;cell id='Name'&gt;ResponseInfo&lt;/cell&gt;&lt;cell id='Datatype'&gt;java.lang.String&lt;/cell&gt;&lt;/row&gt;&lt;row&gt;&lt;cell id='Action'&gt;Create&lt;/cell&gt;&lt;cell id='Type'&gt;constant&lt;/cell&gt;&lt;cell id='Value'&gt;application/json&lt;/cell&gt;&lt;cell id='Default'&gt;&lt;/cell&gt;&lt;cell id='Name'&gt;Content-Type&lt;/cell&gt;&lt;cell id='Datatype'&gt;java.lang.String&lt;/cell&gt;&lt;/row&gt;</value>
                        </ifl:property>
                        <ifl:property>
                            <key>wrapContent</key>
                            <value />
                        </ifl:property>
                        <ifl:property>
                            <key>componentVersion</key>
                            <value>1.4</value>
                        </ifl:property>
                        <ifl:property>
                            <key>activityType</key>
                            <value>Enricher</value>
                        </ifl:property>
                        <ifl:property>
                            <key>cmdVariantUri</key>
                            <value>ctype::FlowstepVariant/cname::Enricher/version::1.4.2</value>
                        </ifl:property>
                        <ifl:property>
                            <key>bodyContent</key>
                            <value>${body}</value>
                        </ifl:property>
                    </bpmn2:extensionElements>
                    <bpmn2:incoming>SequenceFlow_3_root</bpmn2:incoming>
                    <bpmn2:outgoing>SequenceFlow_4_root</bpmn2:outgoing>
                </bpmn2:callActivity>
            <bpmn2:callActivity id="ContentModifier_root_2" name="TransformResponse_root">
                    <bpmn2:extensionElements>
                        <ifl:property>
                            <key>bodyType</key>
                            <value>expression</value>
                        </ifl:property>
                        <ifl:property>
                            <key>propertyTable</key>
                            <value>&lt;row&gt;&lt;cell id='Action'&gt;Create&lt;/cell&gt;&lt;cell id='Type'&gt;constant&lt;/cell&gt;&lt;cell id='Value'&gt;completed&lt;/cell&gt;&lt;cell id='Default'&gt;&lt;/cell&gt;&lt;cell id='Name'&gt;PROCESS_STATUS&lt;/cell&gt;&lt;cell id='Datatype'&gt;java.lang.String&lt;/cell&gt;&lt;/row&gt;</value>
                        </ifl:property>
                        <ifl:property>
                            <key>headerTable</key>
                            <value>&lt;row&gt;&lt;cell id='Action'&gt;Create&lt;/cell&gt;&lt;cell id='Type'&gt;constant&lt;/cell&gt;&lt;cell id='Value'&gt;success&lt;/cell&gt;&lt;cell id='Default'&gt;&lt;/cell&gt;&lt;cell id='Name'&gt;ProcessStatus&lt;/cell&gt;&lt;cell id='Datatype'&gt;java.lang.String&lt;/cell&gt;&lt;/row&gt;</value>
                        </ifl:property>
                        <ifl:property>
                            <key>wrapContent</key>
                            <value />
                        </ifl:property>
                        <ifl:property>
                            <key>componentVersion</key>
                            <value>1.4</value>
                        </ifl:property>
                        <ifl:property>
                            <key>activityType</key>
                            <value>Enricher</value>
                        </ifl:property>
                        <ifl:property>
                            <key>cmdVariantUri</key>
                            <value>ctype::FlowstepVariant/cname::Enricher/version::1.4.2</value>
                        </ifl:property>
                        <ifl:property>
                            <key>bodyContent</key>
                            <value>${body}</value>
                        </ifl:property>
                    </bpmn2:extensionElements>
                    <bpmn2:incoming>SequenceFlow_4_root</bpmn2:incoming>
                    <bpmn2:outgoing>SequenceFlow_End_root</bpmn2:outgoing>
                </bpmn2:callActivity>
            <bpmn2:endEvent id="EndEvent_2" name="End">
                <bpmn2:extensionElements>
                    <ifl:property>
                        <key>componentVersion</key>
                        <value>1.1</value>
                    </ifl:property>
                    <ifl:property>
                        <key>cmdVariantUri</key>
                        <value>ctype::FlowstepVariant/cname::MessageEndEvent/version::1.1.0</value>
                    </ifl:property>
                </bpmn2:extensionElements>
                <bpmn2:incoming>SequenceFlow_End</bpmn2:incoming>
                <bpmn2:messageEventDefinition id="MessageEventDefinition_EndEvent_2" />
            </bpmn2:endEvent>
            <bpmn2:serviceTask id="RequestReply_root" name="RequestReply_root">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.0</value>
        </ifl:property>
        <ifl:property>
            <key>activityType</key>
            <value>ExternalCall</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::ExternalCall/version::1.0.4</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:incoming>SequenceFlow_3_root</bpmn2:incoming>
    <bpmn2:outgoing>SequenceFlow_4_root</bpmn2:outgoing>
</bpmn2:serviceTask>
            <bpmn2:sequenceFlow id="SequenceFlow_Start_root" sourceRef="StartEvent_2" targetRef="JSONtoXMLConverter_root" isImmediate="true" />
            <bpmn2:sequenceFlow id="SequenceFlow_1_root" sourceRef="JSONtoXMLConverter_root" targetRef="ContentModifier_root_1" isImmediate="true" />
            <bpmn2:sequenceFlow id="SequenceFlow_2_root" sourceRef="ContentModifier_root_1" targetRef="ContentModifier_root_headers" isImmediate="true" />
            <bpmn2:sequenceFlow id="SequenceFlow_3_root" sourceRef="ContentModifier_root_headers" targetRef="RequestReply_root" isImmediate="true" />
            <bpmn2:sequenceFlow id="SequenceFlow_4_root" sourceRef="RequestReply_root" targetRef="ContentModifier_root_response" isImmediate="true" />
            <bpmn2:sequenceFlow id="SequenceFlow_5_root" sourceRef="ContentModifier_root_response" targetRef="ContentModifier_root_2" isImmediate="true" />
            <bpmn2:sequenceFlow id="SequenceFlow_End_root" sourceRef="ContentModifier_root_2" targetRef="EndEvent_2" isImmediate="true" />
            <bpmn2:sequenceFlow id="SequenceFlow_End" sourceRef="EndEvent_2" targetRef="EndEvent_2" isImmediate="true" />
            </bpmn2:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1" name="Default Collaboration Diagram">
        
                <bpmndi:BPMNPlane bpmnElement="Collaboration_1" id="BPMNPlane_1"><bpmndi:BPMNShape bpmnElement="Participant_1" id="BPMNShape_Participant_1">
    <dc:Bounds height="140.0" width="100.0" x="100" y="100" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="Participant_Process_1" id="BPMNShape_Participant_Process_1">
    <dc:Bounds height="294" width="957" x="220" y="150" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="Participant_OData_root" id="BPMNShape_Participant_OData_root">
    <dc:Bounds height="140.0" width="100.0" x="850" y="150" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="StartEvent_2" id="BPMNShape_StartEvent_2">
    <dc:Bounds height="32.0" width="32.0" x="263" y="126" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="JSONtoXMLConverter_root" id="BPMNShape_JSONtoXMLConverter_root">
    <dc:Bounds height="60.0" width="100.0" x="362" y="110" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="ContentEnricher_root_1" id="BPMNShape_ContentEnricher_root_1">
    <dc:Bounds height="60.0" width="100.0" x="300" y="140" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="ContentEnricher_root_headers" id="BPMNShape_ContentEnricher_root_headers">
    <dc:Bounds height="60.0" width="100.0" x="420" y="140" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="ServiceTask_OData_root" id="BPMNShape_ServiceTask_OData_root">
    <dc:Bounds height="60.0" width="100.0" x="540" y="140" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="ErrorHandler_root" id="BPMNShape_ErrorHandler_root">
    <dc:Bounds height="60" width="100" x="660" y="140" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="ContentModifier_root_response" id="BPMNShape_ContentModifier_root_response">
    <dc:Bounds height="60.0" width="100.0" x="509" y="110" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="ContentModifier_root_2" id="BPMNShape_ContentModifier_root_2">
    <dc:Bounds height="60.0" width="100.0" x="509" y="110" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="EndEvent_2" id="BPMNShape_EndEvent_2">
    <dc:Bounds height="32.0" width="32.0" x="950" y="112" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="RequestReply_root" id="BPMNShape_RequestReply_root">
    <dc:Bounds height="60.0" width="100.0" x="803" y="110" />
</bpmndi:BPMNShape><bpmndi:BPMNEdge bpmnElement="MessageFlow_10" id="BPMNEdge_MessageFlow_10" sourceElement="BPMNShape_Participant_1" targetElement="BPMNShape_StartEvent_2">
                        <di:waypoint x="150" xsi:type="dc:Point" y="170" />
                        <di:waypoint x="250" xsi:type="dc:Point" y="170" />
                    </bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="MessageFlow_OData_root" id="BPMNEdge_MessageFlow_OData_root" sourceElement="BPMNShape_ServiceTask_OData_root" targetElement="BPMNShape_Participant_OData_root">
    <di:waypoint x="757" xsi:type="dc:Point" y="140" />
    <di:waypoint x="850" xsi:type="dc:Point" y="170" />
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_Start_root" id="BPMNEdge_SequenceFlow_Start_root" sourceElement="BPMNShape_StartEvent_2" targetElement="BPMNShape_JSONtoXMLConverter_root">
    <di:waypoint x="295" xsi:type="dc:Point" y="142.0" />
    <di:waypoint x="362" xsi:type="dc:Point" y="140.0" />
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_4_root" id="BPMNEdge_SequenceFlow_4_root" sourceElement="BPMNShape_RequestReply_root" targetElement="BPMNShape_ContentModifier_root_response">
    <di:waypoint x="903" xsi:type="dc:Point" y="140.0" />
    <di:waypoint x="509" xsi:type="dc:Point" y="140.0" />
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_5_root" id="BPMNEdge_SequenceFlow_5_root" sourceElement="BPMNShape_ContentModifier_root_response" targetElement="BPMNShape_ContentModifier_root_2">
    <di:waypoint x="609" xsi:type="dc:Point" y="140.0" />
    <di:waypoint x="509" xsi:type="dc:Point" y="140.0" />
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_End_root" id="BPMNEdge_SequenceFlow_End_root" sourceElement="BPMNShape_ContentModifier_root_2" targetElement="BPMNShape_EndEvent_2">
    <di:waypoint x="609" xsi:type="dc:Point" y="140.0" />
    <di:waypoint x="950" xsi:type="dc:Point" y="128.0" />
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_End" id="BPMNEdge_SequenceFlow_End" sourceElement="BPMNShape_EndEvent_2" targetElement="BPMNShape_EndEvent_2">
    <di:waypoint x="982" xsi:type="dc:Point" y="128.0" />
    <di:waypoint x="950" xsi:type="dc:Point" y="128.0" />
</bpmndi:BPMNEdge>
                </bpmndi:BPMNPlane>
            
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>