<?xml version="1.0" encoding="UTF-8"?><Component xmlns:bns="http://api.platform.boomi.com/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" branchId="Qjo1MDM0OTU" branchName="main" componentId="06230328-b879-4666-afc1-57aca076a8f2" copiedFromComponentId="92cb3cf6-e1cc-462d-a3b5-24d48c4fd2ef" copiedFromComponentVersion="3" createdBy="<EMAIL>" createdDate="2025-06-17T12:54:56Z" currentVersion="true" deleted="false" folderFullPath="ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08" folderId="Rjo3NzM2MDUz" folderName="Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08" modifiedBy="<EMAIL>" modifiedDate="2025-06-17T12:54:56Z" name="SuccessFactors Connection - With Entity Limits" subType="successfactorsmaster-Q2Q93V-SFSF-priv_prod" type="connector-settings" version="1">
  <bns:encryptedValues/>
  <bns:description>We limit the entities returned via the OData URL suffix.</bns:description>
  <bns:object>
    <GenericConnectionConfig>
      <field id="url" type="string" value="other"/>
      <field id="alternateUrl" type="string" value="https://endpoint.successfactors.com"/>
      <field id="sfapiUrlSuffix" type="string" value="/sfapi/v1/soap"/>
      <field id="enableOData" type="boolean" value="true"/>
      <field id="odataUrlSuffix" type="string" value="/odata/v2/EmpEmployment,EmpJob,PerPersonal,PerPerson,EmpCompensation,User,PerPhone,FOLocation,FOPaygrade,PicklistOption,FOEventReason,PerEmail,Position,FOBusinessUnit,FOCostCenter,EmpPayCompRecurring,FOPayComponent,PerAddressDEFLT,PerNationalId/"/>
      <field id="companyId" type="string" value="[Company ID]"/>
      <field id="username" type="string" value="[Enter ID Here]"/>
      <field id="password" type="password" value=""/>
      <field id="clientId" type="string" value=""/>
      <field id="privateKey" type="password"/>
      <field id="samlAssertionField" type="string" value=""/>
      <field id="samlAssertionValue" type="password"/>
      <field id="batchSize" type="integer" value="200"/>
      <field id="pageSize" type="integer" value="200"/>
      <field id="timeout" type="integer" value="5"/>
      <field id="sleep" type="integer" value="15"/>
      <field id="objectTypeIDList" type="string" value=""/>
    </GenericConnectionConfig>
  </bns:object>
</Component>