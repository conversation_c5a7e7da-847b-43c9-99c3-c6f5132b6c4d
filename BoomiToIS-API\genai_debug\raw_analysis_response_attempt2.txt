{"process_name": "SAP SuccessFactors to SFTP Integration", "description": "This integration facilitates the secure transfer of employee data from SAP SuccessFactors to an SFTP server with comprehensive error handling capabilities.", "endpoints": [{"method": "GET", "path": "/SuccessFactors/Employee", "purpose": "Retrieves employee profile data from SuccessFactors", "components": [{"type": "request_reply", "name": "SAP SuccessFactors OData Request", "id": "odata_successfactors_request", "config": {"operation": "Query(GET)", "resourcePath": "User", "address": "${property.SF_API_BASE_URL}/odata/v2", "authenticationMethod": "OAuth2ClientCredentials", "contentType": "application/json", "MessageProtocol": "OData V2"}}, {"type": "enricher", "name": "Validate Response", "id": "validate_response", "config": {"condition": "${body} != null"}}, {"type": "groovy_script", "name": "Transform to Canonical Format", "id": "transform_to_canonical", "config": {"script": "// Transform SuccessFactors response to canonical format"}}, {"type": "groovy_script", "name": "Canonical To Kafka Avro", "id": "canonical_to_kafka_avro", "config": {"script": "// Map username from Field 9 to Root/Object/batchProcessingDirectives/Object/accountID/Object/username\n// Map contact ID from Field 91 to Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID\n// Map contact point type from Field 111 to Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type\n// Map batch processing option name from Field 118 to Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name"}}, {"type": "request_reply", "name": "SFTP Write", "id": "sftp_write", "config": {"address": "${property.SFTP_HOST}:${property.SFTP_PORT}", "username": "${property.SFTP_USERNAME}", "password": "${property.SFTP_PASSWORD}", "directory": "${property.SFTP_TARGET_DIR}", "operation": "Write"}}, {"type": "groovy_script", "name": "<PERSON><PERSON><PERSON>", "id": "error_handler", "config": {"script": "// Log error details and prepare notification"}}, {"type": "request_reply", "name": "Send Email Notification", "id": "send_email", "config": {"address": "${property.SMTP_HOST}:${property.SMTP_PORT}", "username": "${property.SMTP_USERNAME}", "password": "${property.SMTP_PASSWORD}", "recipients": "${property.NOTIFICATION_RECIPIENTS}", "subject": "SAP SuccessFactors to SFTP Integration Error"}}], "sequence": ["odata_successfactors_request", "validate_response", "transform_to_canonical", "canonical_to_kafka_avro", "sftp_write"], "transformations": [{"source": "Field 9", "target": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username", "type": "profile"}, {"source": "Field 91", "target": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID", "type": "profile"}, {"source": "Field 111", "target": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type", "type": "profile"}, {"source": "Field 118", "target": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name", "type": "profile"}]}]}