{"process_name": "Stripe to Salesforce Opportunity Integration", "description": "This integration creates Salesforce Opportunities automatically when Stripe Subscriptions are completed. The integration listens for Stripe webhook events, specifically for subscription completion events, transforms the subscription data into Salesforce Opportunity format, and creates new Opportunity records in Salesforce.", "endpoints": [{"method": "POST", "path": "/webhook/stripe/subscription", "purpose": "Receives webhook notifications from Stripe when subscription events occur", "components": [{"type": "enricher", "name": "Set Dynamic Properties", "id": "content_modifier_1", "config": {"action": "Set Properties", "property_names": "Based on Stripe subscription data", "property_values": "Extracted from webhook payload"}}, {"type": "json_to_xml_converter", "name": "Transform Subscription to Opportunity", "id": "mapping_1", "config": {"source_format": "JSON", "target_format": "XML", "mapping_details": {"source_path_3_to_description": "Opportunity/Description", "source_path_3_to_name": "Opportunity/Name", "source_path_3_to_closedate": "Opportunity/CloseDate"}}}, {"type": "request_reply", "name": "Create Salesforce Opportunity", "id": "salesforce_adapter_1", "config": {"operation": "Create", "object": "Opportunity", "authentication": "OAuth", "request_format": "XML", "endpoint": "/services/data/v[VERSION]/sobjects/Opportunity", "method": "POST"}}], "sequence": ["content_modifier_1", "mapping_1", "salesforce_adapter_1"], "transformations": [{"source": "Stripe Subscription JSON", "target": "Salesforce Opportunity XML", "mapping": [{"from": "subscription.id", "to": "Opportunity/Description"}, {"from": "subscription.plan.name", "to": "Opportunity/Name"}, {"from": "subscription.current_period_start", "to": "Opportunity/CloseDate"}]}]}]}