{"process_name": "SAP SuccessFactors to SFTP Integration", "description": "This integration connects SAP SuccessFactors with an SFTP server to transfer employee data while implementing comprehensive error handling. The solution extracts data from SuccessFactors, transforms it into the required format, and securely transfers it to an SFTP destination.", "endpoints": [{"method": "GET", "path": "/SuccessFactors/Employee", "purpose": "Retrieves employee data from SuccessFactors", "components": [{"type": "request_reply", "name": "SuccessFactors OData Request", "id": "odata_successfactors_employee", "config": {"adapter_type": "OData", "operation": "Query(GET)", "resourcePath": "Employee", "authentication": "OAuth", "address": "${property.SF_API_URL}", "contentType": "application/json"}}, {"type": "groovy_script", "name": "Validate Response", "id": "script_validate_response", "config": {"script": "def message = messageLog.getVariable('body');\nif (message == null || message.isEmpty()) {\n    messageLog.setVariable('valid_response', 'false');\n} else {\n    messageLog.setVariable('valid_response', 'true');\n}\nreturn message;"}}, {"type": "enricher", "name": "Transform Data (Canonical To Kafka Avro)", "id": "transform_canonical_to_avro", "config": {"mappings": [{"source": "profile.field9", "target": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username"}, {"source": "profile.field91", "target": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID"}, {"source": "profile.field111", "target": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type"}, {"source": "profile.field118", "target": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name"}]}}, {"type": "enricher", "name": "Prepare SFTP Content", "id": "prepare_sftp_content", "config": {"properties": {"filename": "${property.file_naming_pattern}", "encoding": "UTF-8"}}}, {"type": "request_reply", "name": "SFTP Upload", "id": "sftp_upload", "config": {"adapter_type": "SFTP", "operation": "PUT", "address": "${property.SFTP_HOST}", "port": "${property.SFTP_PORT}", "authentication": "Basic", "username": "${property.SFTP_USER}", "password": "${property.SFTP_PASSWORD}", "remote_directory": "${property.SFTP_REMOTE_DIR}"}}, {"type": "groovy_script", "name": "Log Error <PERSON>", "id": "script_log_error", "config": {"script": "def errorMessage = messageLog.getVariable('error_details');\nif (errorMessage != null) {\n    messageLog.setErrorMessage(errorMessage);\n    messageLog.addAttachmentAsString('error_log', errorMessage, 'text/plain');\n}\nreturn message;"}}, {"type": "request_reply", "name": "Send Error Notification", "id": "send_error_notification", "config": {"adapter_type": "Mail", "operation": "Send", "to": "${property.ERROR_EMAIL}", "subject": "Error in SAP SuccessFactors to SFTP Integration", "body": "An error occurred during the integration process: ${property.error_details}"}}], "sequence": ["odata_successfactors_employee", "script_validate_response", "transform_canonical_to_avro", "prepare_sftp_content", "sftp_upload"], "transformations": [{"source_field": "profile.field9", "target_field": "Root/Object/batchProcessingDirectives/Object/accountID/Object/username", "transformation_type": "direct_mapping"}, {"source_field": "profile.field91", "target_field": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID", "transformation_type": "direct_mapping"}, {"source_field": "profile.field111", "target_field": "Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type", "transformation_type": "direct_mapping"}, {"source_field": "profile.field118", "target_field": "Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name", "transformation_type": "direct_mapping"}]}, {"method": "PUT", "path": "/sftp/path/to/destination", "purpose": "Uploads processed employee data to SFTP server", "components": [{"type": "request_reply", "name": "SFTP Upload", "id": "sftp_direct_upload", "config": {"adapter_type": "SFTP", "operation": "PUT", "address": "${property.SFTP_HOST}", "port": "${property.SFTP_PORT}", "authentication": "Basic", "username": "${property.SFTP_USER}", "password": "${property.SFTP_PASSWORD}", "remote_directory": "${property.SFTP_REMOTE_DIR}", "content_type": "application/octet-stream"}}], "sequence": ["sftp_direct_upload"], "transformations": []}]}