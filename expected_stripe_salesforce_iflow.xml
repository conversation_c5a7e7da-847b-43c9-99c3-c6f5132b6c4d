<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:ifl="http:///com.sap.ifl.model/Ifl.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1">
  <bpmn2:collaboration id="Collaboration_1" name="Collaboration">
    <bpmn2:documentation id="Documentation_1671742909132" textFormat="text/plain">Stripe to Salesforce Opportunity Integration</bpmn2:documentation>
    <bpmn2:extensionElements>
      <ifl:property>
        <key>namespaceMapping</key>
        <value></value>
      </ifl:property>
      <ifl:property>
        <key>allowedHeaderList</key>
        <value>*</value>
      </ifl:property>
      <ifl:property>
        <key>httpSessionHandling</key>
        <value>None</value>
      </ifl:property>
      <ifl:property>
        <key>ServerTrace</key>
        <value>false</value>
      </ifl:property>
      <ifl:property>
        <key>returnExceptionToSender</key>
        <value>false</value>
      </ifl:property>
      <ifl:property>
        <key>log</key>
        <value>All events</value>
      </ifl:property>
      <ifl:property>
        <key>componentVersion</key>
        <value>1.1</value>
      </ifl:property>
      <ifl:property>
        <key>cmdVariantUri</key>
        <value>ctype::IFlowVariant/cname::IFlowConfiguration/version::1.1.16</value>
      </ifl:property>
    </bpmn2:extensionElements>
    
    <!-- HTTP Sender Participant (Stripe Webhook) -->
    <bpmn2:participant id="Participant_1" ifl:type="EndpointSender" name="Stripe Webhook">
      <bpmn2:extensionElements>
        <ifl:property>
          <key>enableBasicAuthentication</key>
          <value>false</value>
        </ifl:property>
        <ifl:property>
          <key>ifl:type</key>
          <value>EndpointSender</value>
        </ifl:property>
      </bpmn2:extensionElements>
    </bpmn2:participant>
    
    <!-- Integration Process Participant -->
    <bpmn2:participant id="Participant_Process" ifl:type="IntegrationProcess" name="Integration Process" processRef="Process_1">
      <bpmn2:extensionElements/>
    </bpmn2:participant>
    
    <!-- Salesforce Receiver Participant -->
    <bpmn2:participant id="Participant_Salesforce" ifl:type="EndpointRecevier" name="Salesforce">
      <bpmn2:extensionElements>
        <ifl:property>
          <key>ifl:type</key>
          <value>EndpointRecevier</value>
        </ifl:property>
      </bpmn2:extensionElements>
    </bpmn2:participant>
    
    <!-- HTTP Message Flow from Stripe -->
    <bpmn2:messageFlow id="MessageFlow_Stripe" name="HTTPS" sourceRef="Participant_1" targetRef="StartEvent_2">
      <bpmn2:extensionElements>
        <ifl:property>
          <key>ComponentType</key>
          <value>HTTPS</value>
        </ifl:property>
        <ifl:property>
          <key>urlPath</key>
          <value>/webhook/stripe/subscription</value>
        </ifl:property>
        <ifl:property>
          <key>ComponentNS</key>
          <value>sap</value>
        </ifl:property>
        <ifl:property>
          <key>componentVersion</key>
          <value>1.5</value>
        </ifl:property>
        <ifl:property>
          <key>senderAuthType</key>
          <value>RoleBased</value>
        </ifl:property>
        <ifl:property>
          <key>userRole</key>
          <value>ESBMessaging.send</value>
        </ifl:property>
      </bpmn2:extensionElements>
    </bpmn2:messageFlow>
    
    <!-- Salesforce Message Flow -->
    <bpmn2:messageFlow id="MessageFlow_Salesforce" name="Salesforce" sourceRef="salesforce_adapter_1" targetRef="Participant_Salesforce">
      <bpmn2:extensionElements>
        <ifl:property>
          <key>ComponentType</key>
          <value>Salesforce</value>
        </ifl:property>
        <ifl:property>
          <key>operation</key>
          <value>Create</value>
        </ifl:property>
        <ifl:property>
          <key>object</key>
          <value>Opportunity</value>
        </ifl:property>
        <ifl:property>
          <key>authentication</key>
          <value>OAuth</value>
        </ifl:property>
      </bpmn2:extensionElements>
    </bpmn2:messageFlow>
  </bpmn2:collaboration>
  
  <!-- Integration Process -->
  <bpmn2:process id="Process_1" name="Integration Process" isExecutable="true">
    
    <!-- Start Event -->
    <bpmn2:startEvent id="StartEvent_2" name="Start">
      <bpmn2:extensionElements>
        <ifl:property>
          <key>componentVersion</key>
          <value>1.0</value>
        </ifl:property>
        <ifl:property>
          <key>cmdVariantUri</key>
          <value>ctype::FlowstepVariant/cname::MessageStartEvent/version::1.0</value>
        </ifl:property>
      </bpmn2:extensionElements>
      <bpmn2:outgoing>SequenceFlow_Start_To_Enricher</bpmn2:outgoing>
      <bpmn2:messageEventDefinition/>
    </bpmn2:startEvent>
    
    <!-- Content Enricher (Set Dynamic Properties) -->
    <bpmn2:callActivity id="content_modifier_1" name="Set Dynamic Properties">
      <bpmn2:extensionElements>
        <ifl:property>
          <key>bodyType</key>
          <value>constant</value>
        </ifl:property>
        <ifl:property>
          <key>headerTable</key>
          <value>Based on Stripe subscription data</value>
        </ifl:property>
        <ifl:property>
          <key>componentVersion</key>
          <value>1.6</value>
        </ifl:property>
        <ifl:property>
          <key>activityType</key>
          <value>Enricher</value>
        </ifl:property>
        <ifl:property>
          <key>cmdVariantUri</key>
          <value>ctype::FlowstepVariant/cname::Enricher/version::1.6.0</value>
        </ifl:property>
      </bpmn2:extensionElements>
      <bpmn2:incoming>SequenceFlow_Start_To_Enricher</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_Enricher_To_Mapping</bpmn2:outgoing>
    </bpmn2:callActivity>
    
    <!-- JSON to XML Converter (Transform Subscription to Opportunity) -->
    <bpmn2:callActivity id="mapping_1" name="Transform Subscription to Opportunity">
      <bpmn2:extensionElements>
        <ifl:property>
          <key>additionalRootElementName</key>
          <value>root</value>
        </ifl:property>
        <ifl:property>
          <key>componentVersion</key>
          <value>1.1</value>
        </ifl:property>
        <ifl:property>
          <key>activityType</key>
          <value>JsonToXmlConverter</value>
        </ifl:property>
        <ifl:property>
          <key>cmdVariantUri</key>
          <value>ctype::FlowstepVariant/cname::JsonToXmlConverter/version::1.1.2</value>
        </ifl:property>
      </bpmn2:extensionElements>
      <bpmn2:incoming>SequenceFlow_Enricher_To_Mapping</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_Mapping_To_Salesforce</bpmn2:outgoing>
    </bpmn2:callActivity>
    
    <!-- Service Task for Salesforce (Request Reply) -->
    <bpmn2:serviceTask id="salesforce_adapter_1" name="Create Salesforce Opportunity">
      <bpmn2:extensionElements>
        <ifl:property>
          <key>componentVersion</key>
          <value>1.0</value>
        </ifl:property>
        <ifl:property>
          <key>activityType</key>
          <value>ExternalCall</value>
        </ifl:property>
        <ifl:property>
          <key>cmdVariantUri</key>
          <value>ctype::FlowstepVariant/cname::ExternalCall/version::1.0.4</value>
        </ifl:property>
      </bpmn2:extensionElements>
      <bpmn2:incoming>SequenceFlow_Mapping_To_Salesforce</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_Salesforce_To_End</bpmn2:outgoing>
    </bpmn2:serviceTask>
    
    <!-- End Event -->
    <bpmn2:endEvent id="EndEvent_2" name="End">
      <bpmn2:extensionElements>
        <ifl:property>
          <key>componentVersion</key>
          <value>1.1</value>
        </ifl:property>
        <ifl:property>
          <key>cmdVariantUri</key>
          <value>ctype::FlowstepVariant/cname::MessageEndEvent/version::1.1.0</value>
        </ifl:property>
      </bpmn2:extensionElements>
      <bpmn2:incoming>SequenceFlow_Salesforce_To_End</bpmn2:incoming>
      <bpmn2:messageEventDefinition/>
    </bpmn2:endEvent>
    
    <!-- Sequence Flows -->
    <bpmn2:sequenceFlow id="SequenceFlow_Start_To_Enricher" sourceRef="StartEvent_2" targetRef="content_modifier_1"/>
    <bpmn2:sequenceFlow id="SequenceFlow_Enricher_To_Mapping" sourceRef="content_modifier_1" targetRef="mapping_1"/>
    <bpmn2:sequenceFlow id="SequenceFlow_Mapping_To_Salesforce" sourceRef="mapping_1" targetRef="salesforce_adapter_1"/>
    <bpmn2:sequenceFlow id="SequenceFlow_Salesforce_To_End" sourceRef="salesforce_adapter_1" targetRef="EndEvent_2"/>
  </bpmn2:process>
  
  <!-- BPMN Diagram Layout -->
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane bpmnElement="Collaboration_1" id="BPMNPlane_1">
      
      <!-- Participant Shapes -->
      <bpmndi:BPMNShape bpmnElement="Participant_1" id="BPMNShape_Participant_1">
        <dc:Bounds height="140.0" width="100.0" x="66.0" y="92.0"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape bpmnElement="Participant_Process" id="BPMNShape_Participant_Process">
        <dc:Bounds height="220.0" width="1060.0" x="240.0" y="80.0"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape bpmnElement="Participant_Salesforce" id="BPMNShape_Participant_Salesforce">
        <dc:Bounds height="140.0" width="100.0" x="750.0" y="341.0"/>
      </bpmndi:BPMNShape>
      
      <!-- Process Component Shapes with Dynamic Positioning -->
      <bpmndi:BPMNShape bpmnElement="StartEvent_2" id="BPMNShape_StartEvent_2">
        <dc:Bounds height="32.0" width="32.0" x="300.0" y="154.0"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape bpmnElement="content_modifier_1" id="BPMNShape_content_modifier_1">
        <dc:Bounds height="60.0" width="100.0" x="450.0" y="140.0"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape bpmnElement="mapping_1" id="BPMNShape_mapping_1">
        <dc:Bounds height="60.0" width="100.0" x="600.0" y="140.0"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape bpmnElement="salesforce_adapter_1" id="BPMNShape_salesforce_adapter_1">
        <dc:Bounds height="60.0" width="100.0" x="750.0" y="140.0"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape bpmnElement="EndEvent_2" id="BPMNShape_EndEvent_2">
        <dc:Bounds height="32.0" width="32.0" x="950.0" y="154.0"/>
      </bpmndi:BPMNShape>
      
      <!-- Message Flow Edges -->
      <bpmndi:BPMNEdge bpmnElement="MessageFlow_Stripe" id="BPMNEdge_MessageFlow_Stripe" sourceElement="BPMNShape_Participant_1" targetElement="BPMNShape_StartEvent_2">
        <di:waypoint x="166.0" xsi:type="dc:Point" y="162.0"/>
        <di:waypoint x="300.0" xsi:type="dc:Point" y="170.0"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge bpmnElement="MessageFlow_Salesforce" id="BPMNEdge_MessageFlow_Salesforce" sourceElement="BPMNShape_salesforce_adapter_1" targetElement="BPMNShape_Participant_Salesforce">
        <di:waypoint x="800.0" xsi:type="dc:Point" y="200.0"/>
        <di:waypoint x="800.0" xsi:type="dc:Point" y="341.0"/>
      </bpmndi:BPMNEdge>
      
      <!-- Sequence Flow Edges with Proper Waypoints -->
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_Start_To_Enricher" id="BPMNEdge_SequenceFlow_Start_To_Enricher" sourceElement="BPMNShape_StartEvent_2" targetElement="BPMNShape_content_modifier_1">
        <di:waypoint x="332.0" xsi:type="dc:Point" y="170.0"/>
        <di:waypoint x="450.0" xsi:type="dc:Point" y="170.0"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_Enricher_To_Mapping" id="BPMNEdge_SequenceFlow_Enricher_To_Mapping" sourceElement="BPMNShape_content_modifier_1" targetElement="BPMNShape_mapping_1">
        <di:waypoint x="550.0" xsi:type="dc:Point" y="170.0"/>
        <di:waypoint x="600.0" xsi:type="dc:Point" y="170.0"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_Mapping_To_Salesforce" id="BPMNEdge_SequenceFlow_Mapping_To_Salesforce" sourceElement="BPMNShape_mapping_1" targetElement="BPMNShape_salesforce_adapter_1">
        <di:waypoint x="700.0" xsi:type="dc:Point" y="170.0"/>
        <di:waypoint x="750.0" xsi:type="dc:Point" y="170.0"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_Salesforce_To_End" id="BPMNEdge_SequenceFlow_Salesforce_To_End" sourceElement="BPMNShape_salesforce_adapter_1" targetElement="BPMNShape_EndEvent_2">
        <di:waypoint x="850.0" xsi:type="dc:Point" y="170.0"/>
        <di:waypoint x="950.0" xsi:type="dc:Point" y="170.0"/>
      </bpmndi:BPMNEdge>
      
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn2:definitions>
