{"total_matches": 26, "top_matches": [{"name": "SAP Ariba Integration with SAP Ariba APIs", "description": "This integration flow allows you to connect an Ariba system to the SAP Ariba APIs.", "score": 22.153596901790436, "url": "https://github.com/SAP/apibusinesshub-integration-recipes/tree/master/for/saparibaintegrationwithsaparibaapis"}, {"name": "Build custom Azure Blob Storage integration adapter", "description": "Azure Blob Storage is a massively scalable and secure object storage for cloud-native workloads, archives, data lakes, high-performance computing and machine learning. The camel-Azure Blob Storage component stores and retrieves blobs from [Azure Storage Blob Service](https://azure.microsoft.com/services/storage/blobs/) using Azure APIs v12. This integration adapter enables an integration flow to connect to Azure Blob Storage collection.", "score": 21.454486873431687, "url": "https://github.com/SAP/apibusinesshub-integration-recipes/tree/master/for/azure-integration-adapter/readme.md"}, {"name": "Supplier Integration between SAP Integrated Business Planning and SAP Business Networks", "description": "Replicate supplier related data between SAP Integrated Business Planning (SAP IBP) and SAP Business Networks. These iFlows help to exchange data between SAP Integrated Business Planning and SAP Business Networks from a Supplier prespective. The flows can either be triggered via an REST API or via Timer-based Iflow.", "score": 15.505024093518733, "url": "https://github.com/SAP/apibusinesshub-integration-recipes/tree/master/for/aribasupplierintegration"}, {"name": "SAP SuccessFactors Employee Central Integration with Fidelity", "description": "This integration package provides integration flow which allows you to read and send employee data from SAP SuccessFactors Employee Central to the Fidelity sever.", "score": 15.504584352130024, "url": "https://github.com/SAP/apibusinesshub-integration-recipes/tree/master/for/sapsuccessfactorsemployeecentralintegrationwithfidelity"}, {"name": "SAP SuccessFactors Employee Central Integration with SAP Commissions", "description": "This integration package provides integration flows to replicate employee data from SAP SuccessFactors Employee Central to SAP Commissions.", "score": 15.503876926580782, "url": "https://github.com/SAP/apibusinesshub-integration-recipes/tree/master/successfactorsecintegrationwithcommission"}], "search_terms": {"primary": ["creates salesforce", "salesforce opportunities", "creates", "salesforce", "opportunities", "opportunity", "this integration", "integration creates", "integration", "base url", "stripe webhook", "webhook authentication", "authentication using", "using webhook", "webhook signatures"], "secondary": ["creates", "salesforce", "opportunities", "integration", "stripe", "webhook", "authentication", "using", "signatures", "automatically", "**base", "url/endpoint", "pattern**:", "webhook", "endpoint", "receives", "subscription", "events", "**rate", "limiting"], "tertiary": ["process", "process opportunities", "process opportunity", "process integration", "process creates", "process url", "process signatures", "process automatically", "process events", "process subject", "process subscriptions", "process profile", "process mechanisms**:", "process period)", "process authentication", "process webhook", "process webhook", "process general", "process using", "process salesforce", "process customer", "process this", "process api", "process stripe", "process rate"]}}