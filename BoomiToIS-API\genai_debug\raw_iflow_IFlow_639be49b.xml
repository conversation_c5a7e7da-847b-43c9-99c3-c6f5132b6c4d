<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
                   xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                   xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                   xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                   xmlns:ifl="http:///com.sap.ifl.model/Ifl.xsd"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1">
    <bpmn2:collaboration id="Collaboration_1" name="Default Collaboration">
        <bpmn2:extensionElements>
            <ifl:property>
                <key>namespaceMapping</key>
                <value></value>
            </ifl:property>
            <ifl:property>
                <key>httpSessionHandling</key>
                <value>None</value>
            </ifl:property>
            <ifl:property>
                <key>returnExceptionToSender</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>log</key>
                <value>All events</value>
            </ifl:property>
            <ifl:property>
                <key>corsEnabled</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>componentVersion</key>
                <value>1.2</value>
            </ifl:property>
            <ifl:property>
                <key>ServerTrace</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>xsrfProtection</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>cmdVariantUri</key>
                <value>ctype::IFlowVariant/cname::IFlowConfiguration/version::1.2.4</value>
            </ifl:property>
        </bpmn2:extensionElements>
<bpmn2:participant id="Participant_1" ifl:type="EndpointSender" name="Sender">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>enableBasicAuthentication</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>ifl:type</key>
                    <value>EndpointSender</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:participant>
<bpmn2:participant id="Participant_Process_1" ifl:type="IntegrationProcess" name="Integration Process" processRef="Process_1">
            <bpmn2:extensionElements/>
        </bpmn2:participant>
<bpmn2:participant id="Participant_salesforce_adapter_1" ifl:type="EndpointReceiver" name="Create Salesforce Opportunity_Receiver">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ifl:type</key>
            <value>EndpointReceiver</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:participant>
<bpmn2:messageFlow id="MessageFlow_10" name="HTTPS" sourceRef="Participant_1" targetRef="StartEvent_2">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>ComponentType</key>
                    <value>HTTPS</value>
                </ifl:property>
                <ifl:property>
                    <key>Description</key>
                    <value/>
                </ifl:property>
                <ifl:property>
                    <key>maximumBodySize</key>
                    <value>40</value>
                </ifl:property>
                <ifl:property>
                    <key>ComponentNS</key>
                    <value>sap</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.4</value>
                </ifl:property>
                <ifl:property>
                    <key>urlPath</key>
                    <value>/test</value>
                </ifl:property>
                <ifl:property>
                    <key>TransportProtocolVersion</key>
                    <value>1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>xsrfProtection</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>TransportProtocol</key>
                    <value>HTTPS</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::AdapterVariant/cname::sap:HTTPS/tp::HTTPS/mp::None/direction::Sender/version::1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>userRole</key>
                    <value>ESBMessaging.send</value>
                </ifl:property>
                <ifl:property>
                    <key>senderAuthType</key>
                    <value>RoleBased</value>
                </ifl:property>
                <ifl:property>
                    <key>MessageProtocol</key>
                    <value>None</value>
                </ifl:property>
                <ifl:property>
                    <key>MessageProtocolVersion</key>
                    <value>1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>direction</key>
                    <value>Sender</value>
                </ifl:property>
                <ifl:property>
                    <key>clientCertificates</key>
                    <value></value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:messageFlow>
<bpmn2:messageFlow id="MessageFlow_salesforce_adapter_1" name="HTTP" sourceRef="salesforce_adapter_1" targetRef="Participant_salesforce_adapter_1">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ComponentType</key>
            <value>HTTP</value>
        </ifl:property>
        <ifl:property>
            <key>address</key>
            <value>https://example.com/api</value>
        </ifl:property>
        <ifl:property>
            <key>method</key>
            <value>POST</value>
        </ifl:property>
        <ifl:property>
            <key>TransportProtocol</key>
            <value>HTTPS</value>
        </ifl:property>
        <ifl:property>
            <key>direction</key>
            <value>Receiver</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:messageFlow>
    </bpmn2:collaboration>
    <bpmn2:process id="Process_1" name="Integration Process">
        <bpmn2:extensionElements>
            <ifl:property>
                <key>transactionTimeout</key>
                <value>30</value>
            </ifl:property>
            <ifl:property>
                <key>componentVersion</key>
                <value>1.2</value>
            </ifl:property>
            <ifl:property>
                <key>cmdVariantUri</key>
                <value>ctype::FlowElementVariant/cname::IntegrationProcess/version::1.2.1</value>
            </ifl:property>
            <ifl:property>
                <key>transactionalHandling</key>
                <value>Not Required</value>
            </ifl:property>
        </bpmn2:extensionElements>
        
            <bpmn2:startEvent id="StartEvent_2" name="Start">
                <bpmn2:extensionElements>
                    <ifl:property>
                        <key>componentVersion</key>
                        <value>1.0</value>
                    </ifl:property>
                    <ifl:property>
                        <key>cmdVariantUri</key>
                        <value>ctype::FlowstepVariant/cname::MessageStartEvent/version::1.0</value>
                    </ifl:property>
                </bpmn2:extensionElements>
                <bpmn2:outgoing>SequenceFlow_Start</bpmn2:outgoing>
                <bpmn2:messageEventDefinition id="MessageEventDefinition_StartEvent_2"/>
            </bpmn2:startEvent>
            <bpmn2:callActivity id="content_modifier_1" name="Set Dynamic Properties">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>bodyType</key>
                    <value>expression</value>
                </ifl:property>
                <ifl:property>
                    <key>propertyTable</key>
                    <value></value>
                </ifl:property>
                <ifl:property>
                    <key>headerTable</key>
                    <value></value>
                </ifl:property>
                <ifl:property>
                    <key>wrapContent</key>
                    <value></value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.4</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Enricher</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Enricher/version::1.4.2</value>
                </ifl:property>
                <ifl:property>
                    <key>bodyContent</key>
                    <value>${body}</value>
                </ifl:property>
            </bpmn2:extensionElements>
            <bpmn2:incoming>SequenceFlow_1</bpmn2:incoming>
            <bpmn2:outgoing>SequenceFlow_2</bpmn2:outgoing>
        </bpmn2:callActivity>
            <bpmn2:serviceTask id="salesforce_adapter_1" name="Create Salesforce Opportunity">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.0</value>
        </ifl:property>
        <ifl:property>
            <key>activityType</key>
            <value>ExternalCall</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::ExternalCall/version::1.0.4</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:incoming>SequenceFlow_salesforce_adapter_1_in</bpmn2:incoming>
    <bpmn2:outgoing>SequenceFlow_salesforce_adapter_1_out</bpmn2:outgoing>
</bpmn2:serviceTask>
            <bpmn2:endEvent id="EndEvent_2" name="End">
                <bpmn2:extensionElements>
                    <ifl:property>
                        <key>componentVersion</key>
                        <value>1.1</value>
                    </ifl:property>
                    <ifl:property>
                        <key>cmdVariantUri</key>
                        <value>ctype::FlowstepVariant/cname::MessageEndEvent/version::1.1.0</value>
                    </ifl:property>
                </bpmn2:extensionElements>
                <bpmn2:incoming>SequenceFlow_End</bpmn2:incoming>
                <bpmn2:messageEventDefinition id="MessageEventDefinition_EndEvent_2"/>
            </bpmn2:endEvent>
            <bpmn2:sequenceFlow id="SequenceFlow_Start" sourceRef="content_modifier_1" targetRef="salesforce_adapter_1" isImmediate="true"/>
            <bpmn2:sequenceFlow id="SequenceFlow_End" sourceRef="salesforce_adapter_1" targetRef="mapping_1" isImmediate="true"/>
            <bpmn2:sequenceFlow id="SequenceFlow_End" sourceRef="salesforce_adapter_1" targetRef="EndEvent_2" isImmediate="true"/>
            <bpmn2:sequenceFlow id="SequenceFlow_Start" sourceRef="StartEvent_2" targetRef="content_modifier_1" isImmediate="true"/>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1" name="Default Collaboration Diagram">
        
                <bpmndi:BPMNPlane bpmnElement="Collaboration_1" id="BPMNPlane_1"><bpmndi:BPMNShape bpmnElement="Participant_1" id="BPMNShape_Participant_1">
    <dc:Bounds height="140.0" width="100.0" x="100" y="100"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="Participant_Process_1" id="BPMNShape_Participant_Process_1">
    <dc:Bounds height="294" width="957" x="220" y="150"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="Participant_salesforce_adapter_1" id="BPMNShape_Participant_salesforce_adapter_1">
    <dc:Bounds height="140.0" width="100.0" x="340" y="400"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="StartEvent_2" id="BPMNShape_StartEvent_2">
    <dc:Bounds height="32.0" width="32.0" x="263" y="126"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="content_modifier_1" id="BPMNShape_content_modifier_1">
    <dc:Bounds height="60" width="100" x="300" y="140"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="salesforce_adapter_1" id="BPMNShape_salesforce_adapter_1">
    <dc:Bounds height="60" width="100" x="420" y="140"/>
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="EndEvent_2" id="BPMNShape_EndEvent_2">
    <dc:Bounds height="32.0" width="32.0" x="950" y="112"/>
</bpmndi:BPMNShape>
                    <bpmndi:BPMNEdge bpmnElement="MessageFlow_10" id="BPMNEdge_MessageFlow_10" sourceElement="BPMNShape_Participant_1" targetElement="BPMNShape_StartEvent_2">
                        <di:waypoint x="150" xsi:type="dc:Point" y="170"/>
                        <di:waypoint x="250" xsi:type="dc:Point" y="170"/>
                    </bpmndi:BPMNEdge>
                    <bpmndi:BPMNEdge bpmnElement="MessageFlow_salesforce_adapter_1" id="BPMNEdge_MessageFlow_salesforce_adapter_1" sourceElement="BPMNShape_salesforce_adapter_1" targetElement="BPMNShape_Participant_salesforce_adapter_1">
                        <di:waypoint x="540" xsi:type="dc:Point" y="142"/>
                        <di:waypoint x="540" xsi:type="dc:Point" y="300"/>
                    </bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_Start" id="BPMNEdge_SequenceFlow_Start" sourceElement="BPMNShape_StartEvent_2" targetElement="BPMNShape_content_modifier_1">
    <di:waypoint x="295" xsi:type="dc:Point" y="142.0"/>
    <di:waypoint x="300" xsi:type="dc:Point" y="170.0"/>
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_End" id="BPMNEdge_SequenceFlow_End" sourceElement="BPMNShape_salesforce_adapter_1" targetElement="BPMNShape_EndEvent_2">
    <di:waypoint x="520" xsi:type="dc:Point" y="170.0"/>
    <di:waypoint x="950" xsi:type="dc:Point" y="128.0"/>
</bpmndi:BPMNEdge>
                </bpmndi:BPMNPlane>
            
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>