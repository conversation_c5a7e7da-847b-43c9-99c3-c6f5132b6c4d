{"process_name": "SAP SuccessFactors to SFTP Integration", "description": "Integration solution that connects SAP SuccessFactors with an SFTP server for automated transfer of employee data with error handling", "endpoints": [{"method": "GET", "path": "/employee-data", "purpose": "Retrieve employee data from SuccessFactors and transfer to SFTP server", "components": [{"type": "request_reply", "name": "Get_SuccessFactors_Employee_Data", "id": "successfactors_request", "config": {"endpoint_path": "/odata/v2/Employee", "address": "${successFactors_api_url}"}}, {"type": "groovy_script", "name": "Validate_Employee_Data", "id": "data_validation", "config": {"script": "ValidateEmployeeData.groovy"}}, {"type": "groovy_script", "name": "Transform_Canonical_To_Kafka_Avro", "id": "data_transformation", "config": {"script": "CanonicalToKafkaAvro.groovy"}}, {"type": "enricher", "name": "Prepare_File_For_SFTP", "id": "prepare_sftp_file", "config": {"content": "${property.fileName}"}}, {"type": "request_reply", "name": "Upload_To_SFTP", "id": "sftp_upload", "config": {"endpoint_path": "${sftp_directory}/${property.fileName}"}}], "error_handling": {"exception_subprocess": [{"type": "enricher", "name": "Log_Error_Details", "id": "log_error", "trigger": "any_error", "config": {"content": "Error occurred during integration: ${exception.message}"}}, {"type": "request_reply", "name": "Send_Error_Notification", "id": "send_notification", "trigger": "any_error", "config": {"endpoint_path": "/mail"}}]}, "branching": {"type": "exclusive", "branches": [{"condition": "${body.valid == true}", "components": ["data_transformation", "prepare_sftp_file", "sftp_upload"], "sequence": ["data_transformation", "prepare_sftp_file", "sftp_upload"]}, {"condition": "${body.valid == false}", "components": ["log_error", "send_notification"], "sequence": ["log_error", "send_notification"]}]}, "sequence": ["successfactors_request", "data_validation", "data_transformation", "prepare_sftp_file", "sftp_upload"], "transformations": [{"name": "ValidateEmployeeData.groovy", "type": "groovy", "script": "import com.sap.gateway.ip.core.customdev.util.Message;\n\ndef Message processData(Message message) {\n    def body = message.getBody(java.lang.String);\n    def properties = message.getProperties();\n    \n    // Validate the structure and content of the retrieved data\n    def isValid = true;\n    def validationErrors = [];\n    \n    // Add validation logic here\n    // Example: Check if required fields exist\n    def jsonData = new groovy.json.JsonSlurper().parseText(body);\n    \n    if (!jsonData.d || !jsonData.d.results) {\n        isValid = false;\n        validationErrors.add(\"Missing expected data structure\");\n    }\n    \n    // Set validation result as property\n    properties.put(\"valid\", isValid);\n    properties.put(\"validationErrors\", validationErrors.join(\", \"));\n    \n    return message;\n}"}, {"name": "CanonicalToKafkaAvro.groovy", "type": "groovy", "script": "import com.sap.gateway.ip.core.customdev.util.Message;\nimport groovy.json.*;\n\ndef Message processData(Message message) {\n    def body = message.getBody(java.lang.String);\n    def properties = message.getProperties();\n    \n    // Parse input JSON\n    def jsonData = new JsonSlurper().parseText(body);\n    def results = jsonData.d.results;\n    \n    // Create output structure\n    def output = [:];\n    output.Object = [:];\n    \n    // Batch processing directives\n    output.Object.batchProcessingDirectives = [:];\n    output.Object.batchProcessingDirectives.Object = [:];\n    output.Object.batchProcessingDirectives.Object.accountID = [:];\n    output.Object.batchProcessingDirectives.Object.accountID.Object = [:];\n    output.Object.batchProcessingDirectives.Object.accountID.Object.username = results[0].username;\n    \n    // Batch processing option\n    output.Object.batchProcessingDirectives.Object.batchProcessingOption = [:];\n    output.Object.batchProcessingDirectives.Object.batchProcessingOption.Array = [:];\n    output.Object.batchProcessingDirectives.Object.batchProcessingOption.Array.ArrayElement1 = [:];\n    output.Object.batchProcessingDirectives.Object.batchProcessingOption.Array.ArrayElement1.Object = [:];\n    output.Object.batchProcessingDirectives.Object.batchProcessingOption.Array.ArrayElement1.Object.name = \"ProcessAll\";\n    \n    // Batch contact list\n    output.Object.batchContactList = [:];\n    output.Object.batchContactList.Array = [:];\n    output.Object.batchContactList.Array.ArrayElement1 = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1 = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1.Object = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1.Object.contactID = results[0].contactID;\n    \n    // Contact point list\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1.Object.contactPointList = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1.Object.contactPointList.Array = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1.Object.contactPointList.Array.ArrayElement1 = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1.Object.contactPointList.Array.ArrayElement1.Object = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1.Object.contactPointList.Array.ArrayElement1.Object.contactPoint = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1.Object.contactPointList.Array.ArrayElement1.Object.contactPoint.Array = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1.Object.contactPointList.Array.ArrayElement1.Object.contactPoint.Array.ArrayElement1 = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1.Object.contactPointList.Array.ArrayElement1.Object.contactPoint.Array.ArrayElement1.Object = [:];\n    output.Object.batchContactList.Array.ArrayElement1.Object.contact.Array.ArrayElement1.Object.contactPointList.Array.ArrayElement1.Object.contactPoint.Array.ArrayElement1.Object.type = \"Email\";\n    \n    // Generate filename for SFTP\n    def timestamp = new Date().format(\"yyyyMMdd_HHmmss\");\n    properties.put(\"fileName\", \"employee_data_\" + timestamp + \".json\");\n    \n    // Convert output to JSON string\n    message.setBody(JsonOutput.toJson(output));\n    \n    return message;\n}"}]}]}