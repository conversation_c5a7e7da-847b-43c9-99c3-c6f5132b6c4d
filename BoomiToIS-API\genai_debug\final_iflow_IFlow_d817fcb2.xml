<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:ifl="http:///com.sap.ifl.model/Ifl.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1">
    <bpmn2:collaboration id="Collaboration_1" name="Default Collaboration">
        <bpmn2:extensionElements>
            <ifl:property>
                <key>namespaceMapping</key>
                <value />
            </ifl:property>
            <ifl:property>
                <key>httpSessionHandling</key>
                <value>None</value>
            </ifl:property>
            <ifl:property>
                <key>returnExceptionToSender</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>log</key>
                <value>All events</value>
            </ifl:property>
            <ifl:property>
                <key>corsEnabled</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>componentVersion</key>
                <value>1.2</value>
            </ifl:property>
            <ifl:property>
                <key>ServerTrace</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>xsrfProtection</key>
                <value>false</value>
            </ifl:property>
            <ifl:property>
                <key>cmdVariantUri</key>
                <value>ctype::IFlowVariant/cname::IFlowConfiguration/version::1.2.4</value>
            </ifl:property>
        </bpmn2:extensionElements>
<bpmn2:participant id="Participant_1" ifl:type="EndpointSender" name="Sender">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>enableBasicAuthentication</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>ifl:type</key>
                    <value>EndpointSender</value>
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:participant>
<bpmn2:participant id="Participant_Process_1" ifl:type="IntegrationProcess" name="Integration Process" processRef="Process_1">
            <bpmn2:extensionElements />
        </bpmn2:participant>
<bpmn2:participant id="Participant_OData_successfactors_1" ifl:type="EndpointReceiver" name="OData_Get_SuccessFactors_ContactData">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ifl:type</key>
            <value>EndpointReceiver</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:participant>
<bpmn2:participant id="Participant_sftp_transfer_1" ifl:type="EndpointRecevier" name="SFTP_Transfer_SFTP">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ifl:type</key>
            <value>EndpointRecevier</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:participant>
<bpmn2:messageFlow id="MessageFlow_10" name="HTTPS" sourceRef="Participant_1" targetRef="StartEvent_2">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>ComponentType</key>
                    <value>HTTPS</value>
                </ifl:property>
                <ifl:property>
                    <key>Description</key>
                    <value />
                </ifl:property>
                <ifl:property>
                    <key>maximumBodySize</key>
                    <value>40</value>
                </ifl:property>
                <ifl:property>
                    <key>ComponentNS</key>
                    <value>sap</value>
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.4</value>
                </ifl:property>
                <ifl:property>
                    <key>urlPath</key>
                    <value>/test</value>
                </ifl:property>
                <ifl:property>
                    <key>TransportProtocolVersion</key>
                    <value>1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>xsrfProtection</key>
                    <value>false</value>
                </ifl:property>
                <ifl:property>
                    <key>TransportProtocol</key>
                    <value>HTTPS</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::AdapterVariant/cname::sap:HTTPS/tp::HTTPS/mp::None/direction::Sender/version::1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>userRole</key>
                    <value>ESBMessaging.send</value>
                </ifl:property>
                <ifl:property>
                    <key>senderAuthType</key>
                    <value>RoleBased</value>
                </ifl:property>
                <ifl:property>
                    <key>MessageProtocol</key>
                    <value>None</value>
                </ifl:property>
                <ifl:property>
                    <key>MessageProtocolVersion</key>
                    <value>1.4.1</value>
                </ifl:property>
                <ifl:property>
                    <key>direction</key>
                    <value>Sender</value>
                </ifl:property>
                <ifl:property>
                    <key>clientCertificates</key>
                    <value />
                </ifl:property>
            </bpmn2:extensionElements>
        </bpmn2:messageFlow>
<bpmn2:messageFlow id="MessageFlow_OData_successfactors_1" name="OData" sourceRef="ODataCall_successfactors_1" targetRef="Participant_OData_successfactors_1">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>Description</key>
            <value>OData Connection to ContactData</value>
        </ifl:property>
        <ifl:property>
            <key>pagination</key>
            <value>0</value>
        </ifl:property>
        <ifl:property>
            <key>ComponentNS</key>
            <value>sap</value>
        </ifl:property>
        <ifl:property>
            <key>resourcePath</key>
            <value>ContactData</value>
        </ifl:property>
        <ifl:property>
            <key>TransportProtocolVersion</key>
            <value>1.25.0</value>
        </ifl:property>
        <ifl:property>
            <key>ComponentSWCVName</key>
            <value>external</value>
        </ifl:property>
        <ifl:property>
            <key>enableMPLAttachments</key>
            <value>true</value>
        </ifl:property>
        <ifl:property>
            <key>contentType</key>
            <value>application/atom+xml</value>
        </ifl:property>
        <ifl:property>
            <key>ComponentSWCVId</key>
            <value>1.25.0</value>
        </ifl:property>
        <ifl:property>
            <key>MessageProtocol</key>
            <value>OData V2</value>
        </ifl:property>
        <ifl:property>
            <key>direction</key>
            <value>Receiver</value>
        </ifl:property>
        <ifl:property>
            <key>ComponentType</key>
            <value>HCIOData</value>
        </ifl:property>
        <ifl:property>
            <key>address</key>
            <value>https://${property.SF_API_BASE_URL}/odata/v2</value>
        </ifl:property>
        <ifl:property>
            <key>queryOptions</key>
            <value />
        </ifl:property>
        <ifl:property>
            <key>proxyType</key>
            <value>default</value>
        </ifl:property>
        <ifl:property>
            <key>isCSRFEnabled</key>
            <value>true</value>
        </ifl:property>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.25</value>
        </ifl:property>
        <ifl:property>
            <key>operation</key>
            <value>Query(GET)</value>
        </ifl:property>
        <ifl:property>
            <key>MessageProtocolVersion</key>
            <value>1.25.0</value>
        </ifl:property>
        <ifl:property>
            <key>TransportProtocol</key>
            <value>HTTP</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::AdapterVariant/cname::sap:HCIOData/tp::HTTP/mp::OData V2/direction::Receiver/version::1.25.0</value>
        </ifl:property>
        <ifl:property>
            <key>authenticationMethod</key>
            <value>None</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:messageFlow>
<bpmn2:messageFlow id="MessageFlow_sftp_transfer_1" name="SFTP" sourceRef="sftp_transfer_1" targetRef="Participant_sftp_transfer_1">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>ComponentType</key>
            <value>SFTP</value>
        </ifl:property>
        <ifl:property>
            <key>Description</key>
            <value>SFTP Connection for file upload</value>
        </ifl:property>
        <ifl:property>
            <key>ComponentNS</key>
            <value>sap</value>
        </ifl:property>
        <ifl:property>
            <key>host</key>
            <value />
        </ifl:property>
        <ifl:property>
            <key>port</key>
            <value>22</value>
        </ifl:property>
        <ifl:property>
            <key>path</key>
            <value />
        </ifl:property>
        <ifl:property>
            <key>authentication</key>
            <value>password</value>
        </ifl:property>
        <ifl:property>
            <key>username</key>
            <value />
        </ifl:property>
        <ifl:property>
            <key>operation</key>
            <value>GET</value>
        </ifl:property>
        <ifl:property>
            <key>TransportProtocolVersion</key>
            <value>1.11.2</value>
        </ifl:property>
        <ifl:property>
            <key>MessageProtocol</key>
            <value>File</value>
        </ifl:property>
        <ifl:property>
            <key>direction</key>
            <value>Receiver</value>
        </ifl:property>
        <ifl:property>
            <key>TransportProtocol</key>
            <value>SFTP</value>
        </ifl:property>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.11</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::AdapterVariant/cname::sap:SFTP/tp::SFTP/mp::File/direction::Receiver/version::1.11.2</value>
        </ifl:property>
        <ifl:property>
            <key>fileExist</key>
            <value>Override</value>
        </ifl:property>
        <ifl:property>
            <key>autoCreate</key>
            <value>1</value>
        </ifl:property>
        <ifl:property>
            <key>connectTimeout</key>
            <value>10000</value>
        </ifl:property>
    </bpmn2:extensionElements>
</bpmn2:messageFlow>
    </bpmn2:collaboration>
    <bpmn2:process id="Process_1" name="Integration Process">
        <bpmn2:extensionElements>
            <ifl:property>
                <key>transactionTimeout</key>
                <value>30</value>
            </ifl:property>
            <ifl:property>
                <key>componentVersion</key>
                <value>1.2</value>
            </ifl:property>
            <ifl:property>
                <key>cmdVariantUri</key>
                <value>ctype::FlowElementVariant/cname::IntegrationProcess/version::1.2.1</value>
            </ifl:property>
            <ifl:property>
                <key>transactionalHandling</key>
                <value>Not Required</value>
            </ifl:property>
        </bpmn2:extensionElements>
        
            <bpmn2:startEvent id="StartEvent_2" name="Start">
                <bpmn2:extensionElements>
                    <ifl:property>
                        <key>componentVersion</key>
                        <value>1.0</value>
                    </ifl:property>
                    <ifl:property>
                        <key>cmdVariantUri</key>
                        <value>ctype::FlowstepVariant/cname::MessageStartEvent/version::1.0</value>
                    </ifl:property>
                </bpmn2:extensionElements>
                <bpmn2:outgoing>SequenceFlow_Start</bpmn2:outgoing>
                <bpmn2:messageEventDefinition id="MessageEventDefinition_StartEvent_2" />
            </bpmn2:startEvent>
            <bpmn2:serviceTask id="ODataCall_successfactors_1" name="Get_SuccessFactors_ContactData">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.0</value>
        </ifl:property>
        <ifl:property>
            <key>activityType</key>
            <value>ExternalCall</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::ExternalCall/version::1.0.4</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:incoming>SequenceFlow_Start</bpmn2:incoming>
    <bpmn2:outgoing>SequenceFlow_odata_successfactors_1_out</bpmn2:outgoing>
</bpmn2:serviceTask>
            <bpmn2:callActivity id="log_success_1" name="Log_Success_Message">
            <bpmn2:extensionElements>
                <ifl:property>
                    <key>bodyType</key>
                    <value>expression</value>
                </ifl:property>
                <ifl:property>
                    <key>propertyTable</key>
                    <value />
                </ifl:property>
                <ifl:property>
                    <key>headerTable</key>
                    <value />
                </ifl:property>
                <ifl:property>
                    <key>wrapContent</key>
                    <value />
                </ifl:property>
                <ifl:property>
                    <key>componentVersion</key>
                    <value>1.4</value>
                </ifl:property>
                <ifl:property>
                    <key>activityType</key>
                    <value>Enricher</value>
                </ifl:property>
                <ifl:property>
                    <key>cmdVariantUri</key>
                    <value>ctype::FlowstepVariant/cname::Enricher/version::1.4.2</value>
                </ifl:property>
                <ifl:property>
                    <key>bodyContent</key>
                    <value>{ "status": "success", "timestamp": "${date:now:yyyy-MM-dd'T'HH:mm:ss.SSS}", "message": "Successfully transferred data to SFTP" }</value>
                </ifl:property>
            </bpmn2:extensionElements>
            <bpmn2:incoming>SequenceFlow_1</bpmn2:incoming>
            <bpmn2:outgoing>SequenceFlow_2</bpmn2:outgoing>
        </bpmn2:callActivity>
            <bpmn2:serviceTask id="sftp_transfer_1" name="SFTP_Transfer">
    <bpmn2:extensionElements>
        <ifl:property>
            <key>componentVersion</key>
            <value>1.0</value>
        </ifl:property>
        <ifl:property>
            <key>activityType</key>
            <value>ExternalCall</value>
        </ifl:property>
        <ifl:property>
            <key>cmdVariantUri</key>
            <value>ctype::FlowstepVariant/cname::ExternalCall/version::1.0.4</value>
        </ifl:property>
    </bpmn2:extensionElements>
    <bpmn2:incoming>SequenceFlow_1</bpmn2:incoming>
    <bpmn2:outgoing>SequenceFlow_2</bpmn2:outgoing>
</bpmn2:serviceTask>
            <bpmn2:callActivity id="validate_sf_data_1" name="Validate_SF_Response">
                        <bpmn2:extensionElements>
                            <ifl:property>
                                <key>scriptFunction</key>
                                <value>processMessage</value>
                            </ifl:property>
                            <ifl:property>
                                <key>componentVersion</key>
                                <value>1.0</value>
                            </ifl:property>
                            <ifl:property>
                                <key>activityType</key>
                                <value>Script</value>
                            </ifl:property>
                            <ifl:property>
                                <key>cmdVariantUri</key>
                                <value>ctype::FlowstepVariant/cname::GroovyScript/version::1.0.1</value>
                            </ifl:property>
                            <ifl:property>
                                <key>subActivityType</key>
                                <value>GroovyScript</value>
                            </ifl:property>
                            <ifl:property>
                                <key>script</key>
                                <value>Validate_SF_Response.groovy</value>
                            </ifl:property>
                        </bpmn2:extensionElements>
                    </bpmn2:callActivity>
            <bpmn2:callActivity id="transform_data_1" name="Transform_Canonical_To_Kafka_Avro">
                        <bpmn2:extensionElements>
                            <ifl:property>
                                <key>scriptFunction</key>
                                <value>processMessage</value>
                            </ifl:property>
                            <ifl:property>
                                <key>componentVersion</key>
                                <value>1.0</value>
                            </ifl:property>
                            <ifl:property>
                                <key>activityType</key>
                                <value>Script</value>
                            </ifl:property>
                            <ifl:property>
                                <key>cmdVariantUri</key>
                                <value>ctype::FlowstepVariant/cname::GroovyScript/version::1.0.1</value>
                            </ifl:property>
                            <ifl:property>
                                <key>subActivityType</key>
                                <value>GroovyScript</value>
                            </ifl:property>
                            <ifl:property>
                                <key>script</key>
                                <value>Transform_Canonical_To_Kafka_Avro.groovy</value>
                            </ifl:property>
                        </bpmn2:extensionElements>
                    </bpmn2:callActivity>
            <bpmn2:endEvent id="EndEvent_2" name="End">
                <bpmn2:extensionElements>
                    <ifl:property>
                        <key>componentVersion</key>
                        <value>1.1</value>
                    </ifl:property>
                    <ifl:property>
                        <key>cmdVariantUri</key>
                        <value>ctype::FlowstepVariant/cname::MessageEndEvent/version::1.1.0</value>
                    </ifl:property>
                </bpmn2:extensionElements>
                <bpmn2:incoming>SequenceFlow_End</bpmn2:incoming>
                <bpmn2:messageEventDefinition id="MessageEventDefinition_EndEvent_2" />
            </bpmn2:endEvent>
            <bpmn2:sequenceFlow id="flow_odata_successfactors_1_to_log_success_1_1" sourceRef="odata_successfactors_1" targetRef="log_success_1" isImmediate="true" />
            <bpmn2:sequenceFlow id="SequenceFlow_1" sourceRef="log_success_1" targetRef="sftp_transfer_1" isImmediate="true" />
            <bpmn2:sequenceFlow id="SequenceFlow_2" sourceRef="sftp_transfer_1" targetRef="validate_sf_data_1" isImmediate="true" />
            <bpmn2:sequenceFlow id="flow_validate_sf_data_1_to_transform_data_1_2" sourceRef="validate_sf_data_1" targetRef="transform_data_1" isImmediate="true" />
            <bpmn2:sequenceFlow id="flow_transform_data_1_to_EndEvent_2_3" sourceRef="transform_data_1" targetRef="EndEvent_2" isImmediate="true" />
            <bpmn2:sequenceFlow id="flow_StartEvent_2_to_ODataCall_successfactors_1_4" sourceRef="StartEvent_2" targetRef="ODataCall_successfactors_1" isImmediate="true" />
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1" name="Default Collaboration Diagram">
        
                <bpmndi:BPMNPlane bpmnElement="Collaboration_1" id="BPMNPlane_1"><bpmndi:BPMNShape bpmnElement="Participant_1" id="BPMNShape_Participant_1">
    <dc:Bounds height="140.0" width="100.0" x="100" y="100" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="Participant_Process_1" id="BPMNShape_Participant_Process_1">
    <dc:Bounds height="294" width="957" x="220" y="150" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="Participant_OData_successfactors_1" id="BPMNShape_Participant_OData_successfactors_1">
    <dc:Bounds height="140.0" width="100.0" x="850" y="150" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="Participant_sftp_transfer_1" id="BPMNShape_Participant_sftp_transfer_1">
    <dc:Bounds height="140.0" width="100.0" x="460" y="100" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="StartEvent_2" id="BPMNShape_StartEvent_2">
    <dc:Bounds height="32.0" width="32.0" x="263" y="126" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="ODataCall_successfactors_1" id="BPMNShape_ODataCall_successfactors_1">
    <dc:Bounds height="60" width="100" x="300" y="140" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="log_success_1" id="BPMNShape_log_success_1">
    <dc:Bounds height="60" width="100" x="420" y="140" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="sftp_transfer_1" id="BPMNShape_sftp_transfer_1">
    <dc:Bounds height="60" width="100" x="540" y="140" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="validate_sf_data_1" id="BPMNShape_validate_sf_data_1">
    <dc:Bounds height="60" width="100" x="660" y="140" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="transform_data_1" id="BPMNShape_transform_data_1">
    <dc:Bounds height="60" width="100" x="780" y="140" />
</bpmndi:BPMNShape><bpmndi:BPMNShape bpmnElement="EndEvent_2" id="BPMNShape_EndEvent_2">
    <dc:Bounds height="32.0" width="32.0" x="950" y="112" />
</bpmndi:BPMNShape><bpmndi:BPMNEdge bpmnElement="MessageFlow_10" id="BPMNEdge_MessageFlow_10" sourceElement="BPMNShape_Participant_1" targetElement="BPMNShape_StartEvent_2">
                        <di:waypoint x="150" xsi:type="dc:Point" y="170" />
                        <di:waypoint x="250" xsi:type="dc:Point" y="170" />
                    </bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="MessageFlow_OData_successfactors_1" id="BPMNEdge_MessageFlow_OData_successfactors_1" sourceElement="BPMNShape_ODataCall_successfactors_1" targetElement="BPMNShape_Participant_OData_successfactors_1">
    <di:waypoint x="757" xsi:type="dc:Point" y="140" />
    <di:waypoint x="850" xsi:type="dc:Point" y="170" />
</bpmndi:BPMNEdge>
                    <bpmndi:BPMNEdge bpmnElement="MessageFlow_sftp_transfer_1" id="BPMNEdge_MessageFlow_sftp_transfer_1" sourceElement="BPMNShape_sftp_transfer_1" targetElement="BPMNShape_Participant_sftp_transfer_1">
                        <di:waypoint x="660" xsi:type="dc:Point" y="142" />
                        <di:waypoint x="660" xsi:type="dc:Point" y="300" />
                    </bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="flow_odata_successfactors_1_to_log_success_1_1" id="BPMNEdge_flow_odata_successfactors_1_to_log_success_1_1" sourceElement="BPMNShape_StartEvent_2" targetElement="BPMNShape_ODataCall_successfactors_1">
    <di:waypoint x="295" xsi:type="dc:Point" y="142.0" />
    <di:waypoint x="300" xsi:type="dc:Point" y="170.0" />
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_1" id="BPMNEdge_SequenceFlow_1" sourceElement="BPMNShape_log_success_1" targetElement="BPMNShape_sftp_transfer_1">
    <di:waypoint x="520" xsi:type="dc:Point" y="170.0" />
    <di:waypoint x="540" xsi:type="dc:Point" y="170.0" />
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="SequenceFlow_2" id="BPMNEdge_SequenceFlow_2" sourceElement="BPMNShape_sftp_transfer_1" targetElement="BPMNShape_validate_sf_data_1">
    <di:waypoint x="640" xsi:type="dc:Point" y="170.0" />
    <di:waypoint x="660" xsi:type="dc:Point" y="170.0" />
</bpmndi:BPMNEdge><bpmndi:BPMNEdge bpmnElement="flow_validate_sf_data_1_to_transform_data_1_2" id="BPMNEdge_flow_validate_sf_data_1_to_transform_data_1_2" sourceElement="BPMNShape_transform_data_1" targetElement="BPMNShape_EndEvent_2">
    <di:waypoint x="880" xsi:type="dc:Point" y="170.0" />
    <di:waypoint x="950" xsi:type="dc:Point" y="128.0" />
</bpmndi:BPMNEdge>
                </bpmndi:BPMNPlane>
            
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>