
<?xml version="1.0" encoding="UTF-8"?>
<boomi-mapping-component>
    <metadata>
        <title>SFSF EmpEmployment to Canonical</title>
        <component-id>37d05338-4d4f-4b50-8575-b65934caeea8</component-id>
        <created-by><PERSON><PERSON><PERSON> (<EMAIL>)</created-by>
        <created-date>2025-06-17 06:24:55 PM</created-date>
        <revision>1</revision>
        <modified-by><PERSON><PERSON><PERSON> (<EMAIL>)</modified-by>
        <modified-date>2025-06-17 06:24:55 PM</modified-date>
    </metadata>

    <component-xml>
<?xml version="1.0" encoding="UTF-8"?><Component xmlns:bns="http://api.platform.boomi.com/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" branchId="Qjo1MDM0OTU" branchName="main" componentId="37d05338-4d4f-4b50-8575-b65934caeea8" copiedFromComponentId="40b5cbf4-6188-4c97-8061-55846c281e87" copiedFromComponentVersion="1" createdBy="<EMAIL>" createdDate="2025-06-17T12:54:55Z" currentVersion="true" deleted="false" folderFullPath="ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08" folderId="Rjo3NzM2MDUz" folderName="Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08" modifiedBy="<EMAIL>" modifiedDate="2025-06-17T12:54:55Z" name="SFSF EmpEmployment to Canonical" type="transform.map" version="1">
  <bns:encryptedValues/>
  <bns:description/>
  <bns:object>
    <Map fromProfile="d3661895-bcc9-4511-8bb0-4ff379df2d6c" toProfile="a0733742-31f8-4c23-a8a4-08382ffb75ce">
      <Mappings>
        <Mapping fromKey="580" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='580']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/eventReason" fromType="profile" toKey="78" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='78']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/change-reason" toType="profile"/>
        <Mapping fromKey="426" fromKeyPath="*[@key='1']/*[@key='366']/*[@key='426']" fromNamePath="SFOData.EmpEmployment/compInfoNav/payGroup" fromType="profile" toKey="111" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='111']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/pay-group" toType="profile"/>
        <Mapping fromKey="848" fromKeyPath="*[@key='1']/*[@key='366']/*[@key='831']/*[@key='848']" fromNamePath="SFOData.EmpEmployment/compInfoNav/empPayCompRecurringNav/paycompvalue" fromType="profile" toKey="118" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='116']/*[@key='118']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/Pay_Component_Recurring/paycompvalue" toType="profile"/>
        <Mapping fromKey="848" fromKeyPath="*[@key='1']/*[@key='366']/*[@key='831']/*[@key='848']" fromNamePath="SFOData.EmpEmployment/compInfoNav/empPayCompRecurringNav/paycompvalue" fromType="profile" toKey="117" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='116']/*[@key='117']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/Pay_Component_Recurring/pay-component" toType="profile"/>
        <Mapping fromKey="874" fromKeyPath="*[@key='1']/*[@key='366']/*[@key='831']/*[@key='862']/*[@key='874']" fromNamePath="SFOData.EmpEmployment/compInfoNav/empPayCompRecurringNav/payComponentNav/frequencyCode" fromType="profile" toKey="119" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='116']/*[@key='119']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/Pay_Component_Recurring/frequency" toType="profile"/>
        <Mapping fromKey="610" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='610']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/isFulltimeEmployee" fromType="profile" toKey="71" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='71']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/is-fulltime-employee" toType="profile"/>
        <Mapping fromKey="596" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='596']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/fte" fromType="profile" toKey="72" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='72']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/FTE" toType="profile"/>
        <Mapping fromKey="596" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='596']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/fte" fromType="profile" toKey="73" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='73']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/FTEHYP" toType="profile"/>
        <Mapping fromKey="496" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='496']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/businessUnit" fromType="profile" toKey="83" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='83']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/businessUnitName" toType="profile"/>
        <Mapping fromKey="630" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='630']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/location" fromType="profile" toKey="91" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='91']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/locationId" toType="profile"/>
        <Mapping fromKey="694" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='694']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/timezone" fromType="profile" toKey="99" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='99']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/timezone" toType="profile"/>
        <Mapping fromKey="692" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='692']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/timeTypeProfileCode" fromType="profile" toKey="101" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='101']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/holiday-calendar-code" toType="profile"/>
        <Mapping fromKey="1218" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='1178']/*[@key='1218']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/businessUnitNav/name_defaultValue" fromType="profile" toKey="84" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='84']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/typeOfBusinessUnit2" toType="profile"/>
        <Mapping fromKey="1809" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='1765']/*[@key='1809']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/costCenterNav/name_defaultValue" fromType="profile" toKey="85" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='85']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/Area" toType="profile"/>
        <Mapping fromKey="2140" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2138']/*[@key='2140']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/emplStatusNav/externalCode" fromType="profile" toKey="42" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='42']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/Status" toType="profile"/>
        <Mapping fromKey="2141" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2138']/*[@key='2141']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/emplStatusNav/localeLabel" fromType="profile" toKey="43" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='43']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/DetailedStatus" toType="profile"/>
        <Mapping fromKey="2590" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2558']/*[@key='2590']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/locationNav/name" fromType="profile" toKey="92" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='92']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/locationName" toType="profile"/>
        <Mapping fromKey="2880" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2857']/*[@key='2880']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/positionNav/externalName_defaultValue" fromType="profile" toKey="61" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='61']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/job-title" toType="profile"/>
        <Mapping fromKey="2921" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2857']/*[@key='2921']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/positionNav/regularTemporary" fromType="profile" toKey="69" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='69']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/regular-temp" toType="profile"/>
        <Mapping fromKey="2922" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2857']/*[@key='2922']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/positionNav/standardHours" fromType="profile" toKey="70" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='70']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/standard-hours" toType="profile"/>
        <Mapping fromKey="2863" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2857']/*[@key='2863']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/positionNav/company" fromType="profile" toKey="81" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='81']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/company" toType="profile"/>
        <Mapping fromKey="2863" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2857']/*[@key='2863']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/positionNav/company" fromType="profile" toKey="82" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='82']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/company-US" toType="profile"/>
        <Mapping fromKey="2873" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2857']/*[@key='2873']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/positionNav/division" fromType="profile" toKey="86" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='86']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/division" toType="profile"/>
        <Mapping fromKey="2873" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2857']/*[@key='2873']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/positionNav/division" fromType="profile" toKey="87" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='87']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/divisionName" toType="profile"/>
        <Mapping fromKey="2871" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2857']/*[@key='2871']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/positionNav/department" fromType="profile" toKey="89" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='89']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/department" toType="profile"/>
        <Mapping fromKey="2864" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2857']/*[@key='2864']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/positionNav/costCenter" fromType="profile" toKey="100" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='100']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/cost-center" toType="profile"/>
        <Mapping fromKey="3353" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3347']/*[@key='3353']" fromNamePath="SFOData.EmpEmployment/personNav/emailNav/emailAddress" fromType="profile" toKey="34" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='33']/*[@key='34']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/email/email-address" toType="profile"/>
        <Mapping fromKey="3410" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3401']/*[@key='3410']" fromNamePath="SFOData.EmpEmployment/personNav/homeAddressNavDEFLT/address2" fromType="profile" toKey="23" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='21']/*[@key='23']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/address/extendedLines" toType="profile"/>
        <Mapping fromKey="3419" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3401']/*[@key='3419']" fromNamePath="SFOData.EmpEmployment/personNav/homeAddressNavDEFLT/city" fromType="profile" toKey="26" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='21']/*[@key='26']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/address/city" toType="profile"/>
        <Mapping fromKey="3421" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3401']/*[@key='3421']" fromNamePath="SFOData.EmpEmployment/personNav/homeAddressNavDEFLT/county" fromType="profile" toKey="27" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='21']/*[@key='27']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/address/county" toType="profile"/>
        <Mapping fromKey="3405" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3401']/*[@key='3405']" fromNamePath="SFOData.EmpEmployment/personNav/homeAddressNavDEFLT/address1" fromType="profile" toKey="93" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='93']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/line" toType="profile"/>
        <Mapping fromKey="3420" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3401']/*[@key='3420']" fromNamePath="SFOData.EmpEmployment/personNav/homeAddressNavDEFLT/country" fromType="profile" toKey="94" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='94']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/countryCode" toType="profile"/>
        <Mapping fromKey="3419" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3401']/*[@key='3419']" fromNamePath="SFOData.EmpEmployment/personNav/homeAddressNavDEFLT/city" fromType="profile" toKey="96" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='96']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/city" toType="profile"/>
        <Mapping fromKey="3421" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3401']/*[@key='3421']" fromNamePath="SFOData.EmpEmployment/personNav/homeAddressNavDEFLT/county" fromType="profile" toKey="97" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='97']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/county" toType="profile"/>
        <Mapping fromKey="3458" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3447']/*[@key='3458']" fromNamePath="SFOData.EmpEmployment/personNav/nationalIdNav/nationalId" fromType="profile" toKey="40" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='40']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/national-id" toType="profile"/>
        <Mapping fromKey="3598" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3583']/*[@key='3598']" fromNamePath="SFOData.EmpEmployment/personNav/phoneNav/phoneNumber" fromType="profile" toKey="31" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='29']/*[@key='31']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/phone/phone-number" toType="profile"/>
        <Mapping fromKey="362" fromKeyPath="*[@key='1']/*[@key='362']" fromNamePath="SFOData.EmpEmployment/startDate" fromType="profile" toKey="49" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='49']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/Grp-SeniorityDt" toType="profile"/>
        <Mapping fromKey="362" fromKeyPath="*[@key='1']/*[@key='362']" fromNamePath="SFOData.EmpEmployment/startDate" fromType="profile" toKey="51" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='51']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/orighireDate" toType="profile"/>
        <Mapping fromKey="362" fromKeyPath="*[@key='1']/*[@key='362']" fromNamePath="SFOData.EmpEmployment/startDate" fromType="profile" toKey="52" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='52']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/GrpSeniorDt" toType="profile"/>
        <Mapping fromKey="360" fromKeyPath="*[@key='1']/*[@key='360']" fromNamePath="SFOData.EmpEmployment/serviceDate" fromType="profile" toKey="53" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='53']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/latest-effdt-change" toType="profile"/>
        <Mapping fromKey="362" fromKeyPath="*[@key='1']/*[@key='362']" fromNamePath="SFOData.EmpEmployment/startDate" fromType="profile" toKey="54" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='54']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/EffectiveStart" toType="profile"/>
        <Mapping fromKey="362" fromKeyPath="*[@key='1']/*[@key='362']" fromNamePath="SFOData.EmpEmployment/startDate" fromType="profile" toKey="55" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='55']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/hireDate" toType="profile"/>
        <Mapping fromKey="362" fromKeyPath="*[@key='1']/*[@key='362']" fromNamePath="SFOData.EmpEmployment/startDate" fromType="profile" toKey="56" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='56']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/StartDate" toType="profile"/>
        <Mapping fromKey="358" fromKeyPath="*[@key='1']/*[@key='358']" fromNamePath="SFOData.EmpEmployment/seniorityDate" fromType="profile" toKey="57" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='57']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/seniorityDate" toType="profile"/>
        <Mapping fromKey="362" fromKeyPath="*[@key='1']/*[@key='362']" fromNamePath="SFOData.EmpEmployment/startDate" fromType="profile" toKey="120" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='116']/*[@key='120']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/Pay_Component_Recurring/startDate-PayComp" toType="profile"/>
        <Mapping fromKey="516" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='516']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/countryOfCompany" fromType="profile" toKey="24" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='21']/*[@key='24']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/address/countryCode" toType="profile"/>
        <Mapping fromKey="424" fromKeyPath="*[@key='1']/*[@key='366']/*[@key='424']" fromNamePath="SFOData.EmpEmployment/compInfoNav/payGrade" fromType="profile" toKey="67" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='67']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/pay-grade-US" toType="profile"/>
        <Mapping fromKey="424" fromKeyPath="*[@key='1']/*[@key='366']/*[@key='424']" fromNamePath="SFOData.EmpEmployment/compInfoNav/payGrade" fromType="profile" toKey="68" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='68']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/pay-grade" toType="profile"/>
        <Mapping fromKey="2577" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='2558']/*[@key='2577']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/locationNav/addressZipCode" fromType="profile" toKey="28" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='21']/*[@key='28']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/address/postalCode" toType="profile"/>
        <Mapping fromKey="428" fromKeyPath="*[@key='1']/*[@key='366']/*[@key='428']" fromNamePath="SFOData.EmpEmployment/compInfoNav/payrollSystemId" fromType="profile" toKey="10" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='10']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/value" toType="profile"/>
        <Mapping fromKey="3434" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3401']/*[@key='3434']" fromNamePath="SFOData.EmpEmployment/personNav/homeAddressNavDEFLT/state" fromType="profile" toKey="25" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='21']/*[@key='25']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/address/countrySubDivisions" toType="profile"/>
        <Mapping fromKey="3434" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3401']/*[@key='3434']" fromNamePath="SFOData.EmpEmployment/personNav/homeAddressNavDEFLT/state" fromType="profile" toKey="95" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='95']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/countrySubDivisions" toType="profile"/>
        <Mapping fromCacheJoinKey="0" fromKey="578" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='578']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/event" fromType="profile" toKey="79" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='79']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/change-reason-external" toType="profile"/>
        <Mapping fromKey="542" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='542']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/department" fromType="profile" toKey="90" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='90']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/departmentName" toType="profile"/>
        <Mapping fromKey="614" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='614']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/jobCode" fromType="profile" toKey="58" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='58']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/job-code" toType="profile"/>
        <Mapping fromKey="614" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='614']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/jobCode" fromType="profile" toKey="62" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='62']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/local-job-title" toType="profile"/>
        <Mapping fromKey="36293" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='36154']/*[@key='36293']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/managerUserNav/username" fromType="profile" toKey="105" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='105']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/rel-user-id" toType="profile"/>
        <Mapping fromKey="3405" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3401']/*[@key='3405']" fromNamePath="SFOData.EmpEmployment/personNav/homeAddressNavDEFLT/address1" fromType="profile" toKey="22" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='21']/*[@key='22']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/address/line" toType="profile"/>
        <Mapping fromKey="3479" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3462']/*[@key='3479']" fromNamePath="SFOData.EmpEmployment/personNav/personalInfoNav/maritalStatus" fromType="profile" toKey="39" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='39']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/marital-status" toType="profile"/>
        <Mapping fromKey="3483" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3462']/*[@key='3483']" fromNamePath="SFOData.EmpEmployment/personNav/personalInfoNav/nativePreferredLang" fromType="profile" toKey="8" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='8']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/languageCode" toType="profile"/>
        <Mapping fromKey="36613" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36613']" fromNamePath="SFOData.EmpEmployment/userNav/username" fromType="profile" toKey="9" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='9']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/username" toType="profile"/>
        <Mapping fromKey="36565" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36565']" fromNamePath="SFOData.EmpEmployment/userNav/nickname" fromType="profile" toKey="12" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='12']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/preferred-name" toType="profile"/>
        <Mapping fromKey="36561" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36561']" fromNamePath="SFOData.EmpEmployment/userNav/mi" fromType="profile" toKey="14" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='14']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/middle-name" toType="profile"/>
        <Mapping fromKey="36548" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36548']" fromNamePath="SFOData.EmpEmployment/userNav/lastName" fromType="profile" toKey="15" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='15']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/last-name" toType="profile"/>
        <Mapping fromKey="36605" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36605']" fromNamePath="SFOData.EmpEmployment/userNav/suffix" fromType="profile" toKey="18" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='18']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/title" toType="profile"/>
        <Mapping fromKey="36532" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36532']" fromNamePath="SFOData.EmpEmployment/userNav/gender" fromType="profile" toKey="20" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='20']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/gender" toType="profile"/>
        <Mapping fromKey="36514" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36514']" fromNamePath="SFOData.EmpEmployment/userNav/dateOfBirth" fromType="profile" toKey="41" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='41']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/date-of-birth" toType="profile"/>
        <Mapping fromKey="36605" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36605']" fromNamePath="SFOData.EmpEmployment/userNav/suffix" fromType="profile" toKey="19" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='19']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/suffix" toType="profile"/>
        <Mapping fromKey="36595" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36595']" fromNamePath="SFOData.EmpEmployment/userNav/serviceDate" fromType="profile" toKey="50" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='50']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/serviceDate" toType="profile"/>
        <Mapping fromKey="36612" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36612']" fromNamePath="SFOData.EmpEmployment/userNav/totalTeamSize" fromType="profile" toKey="141" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='141']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/TotalTeamSize" toType="profile"/>
        <Mapping fromKey="36893" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36848']/*[@key='36893']" fromNamePath="SFOData.EmpEmployment/userNav/incumbentOfPositionNav/jobTitle" fromType="profile" toKey="60" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='60']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/US-JobTitle" toType="profile"/>
        <Mapping fromKey="632" fromKeyPath="*[@key='1']/*[@key='372']/*[@key='632']" fromNamePath="SFOData.EmpEmployment/jobInfoNav/managerId" fromType="profile" toKey="145" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='145']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/ManagerID" toType="profile"/>
        <Mapping fromKey="36530" fromKeyPath="*[@key='1']/*[@key='36474']/*[@key='36530']" fromNamePath="SFOData.EmpEmployment/userNav/firstName" fromType="profile" toKey="13" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='13']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/first-name" toType="profile"/>
        <Mapping fromKey="3435" fromKeyPath="*[@key='1']/*[@key='376']/*[@key='3401']/*[@key='3435']" fromNamePath="SFOData.EmpEmployment/personNav/homeAddressNavDEFLT/zipCode" fromType="profile" toKey="98" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='98']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/postalCode" toType="profile"/>
        <Mapping fromKey="292" fromKeyPath="*[@key='1']/*[@key='292']" fromNamePath="SFOData.EmpEmployment/userId" fromType="profile" toKey="114" toKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='114']" toNamePath="output/GBOEmployeeList/GBOEmployee/Employee/payroll-id" toType="profile"/>
      </Mappings>
      <Functions optimizeExecutionOrder="true"/>
      <Defaults>
        <Default toKey="22" value="TBD"/>
        <Default toKey="30" value="+33"/>
        <Default toKey="32" value="work"/>
        <Default toKey="35" value="work"/>
        <Default toKey="48" value="123"/>
        <Default toKey="74" value="2"/>
        <Default toKey="75" value="N"/>
        <Default toKey="108" value="1"/>
        <Default toKey="109" value="1"/>
        <Default toKey="110" value="1"/>
        <Default toKey="112" value="1"/>
        <Default toKey="113" value="1"/>
        <Default toKey="121" value="0"/>
        <Default toKey="143" value="1"/>
        <Default toKey="144" value="1"/>
        <Default toKey="42" value="A"/>
        <Default toKey="118" value="3450"/>
      </Defaults>
      <DocumentCacheJoins/>
    </Map>
  </bns:object>
</Component>
    </component-xml>
</boomi-mapping-component>