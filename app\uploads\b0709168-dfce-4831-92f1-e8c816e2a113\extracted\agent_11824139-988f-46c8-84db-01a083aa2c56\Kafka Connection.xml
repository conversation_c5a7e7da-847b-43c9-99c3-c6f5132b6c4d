<?xml version="1.0" encoding="UTF-8"?><Component xmlns:bns="http://api.platform.boomi.com/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" branchId="Qjo1MDM0OTU" branchName="main" componentId="3a314f53-92c3-4a55-8dcf-6c3ad766acb4" copiedFromComponentId="6b360245-714b-462f-ab00-e9352a98feda" copiedFromComponentVersion="1" createdBy="<EMAIL>" createdDate="2025-06-17T12:54:55Z" currentVersion="true" deleted="false" folderFullPath="ITresonance/Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08" folderId="Rjo3NzM2MDUz" folderName="Connect SAP SuccessFactors to SFTP with Error Handling_2025-06-17-12:55:08" modifiedBy="<EMAIL>" modifiedDate="2025-06-17T12:54:55Z" name="Canonical To Kafka Avro" type="transform.map" version="1">
  <bns:encryptedValues/>
  <bns:description/>
  <bns:object>
    <Map fromProfile="a0733742-31f8-4c23-a8a4-08382ffb75ce" toProfile="d93e0b39-c94d-4947-9b49-5d9939473947">
      <Mappings>
        <Mapping fromKey="9" fromKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='9']" fromNamePath="output/GBOEmployeeList/GBOEmployee/Employee/username" fromType="profile" toKey="144" toKeyPath="*[@key='1']/*[@key='2']/*[@key='140']/*[@key='141']/*[@key='142']/*[@key='143']/*[@key='144']" toNamePath="Root/Object/batchProcessingDirectives/Object/accountID/Object/username" toType="profile"/>
        <Mapping fromKey="91" fromKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='91']" fromNamePath="output/GBOEmployeeList/GBOEmployee/Employee/locationId" fromType="profile" toKey="160" toKeyPath="*[@key='1']/*[@key='2']/*[@key='151']/*[@key='152']/*[@key='153']/*[@key='154']/*[@key='155']/*[@key='156']/*[@key='157']/*[@key='158']/*[@key='160']" toNamePath="Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactID" toType="profile"/>
        <Mapping fromKey="111" fromKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='111']" fromNamePath="output/GBOEmployeeList/GBOEmployee/Employee/pay-group" fromType="profile" toKey="174" toKeyPath="*[@key='1']/*[@key='2']/*[@key='151']/*[@key='152']/*[@key='153']/*[@key='154']/*[@key='155']/*[@key='156']/*[@key='157']/*[@key='158']/*[@key='166']/*[@key='167']/*[@key='168']/*[@key='169']/*[@key='170']/*[@key='171']/*[@key='172']/*[@key='173']/*[@key='174']" toNamePath="Root/Object/batchContactList/Array/ArrayElement1/Object/contact/Array/ArrayElement1/Object/contactPointList/Array/ArrayElement1/Object/contactPoint/Array/ArrayElement1/Object/type" toType="profile"/>
        <Mapping fromKey="118" fromKeyPath="*[@key='2']/*[@key='3']/*[@key='4']/*[@key='5']/*[@key='116']/*[@key='118']" fromNamePath="output/GBOEmployeeList/GBOEmployee/Employee/Pay_Component_Recurring/paycompvalue" fromType="profile" toKey="150" toKeyPath="*[@key='1']/*[@key='2']/*[@key='140']/*[@key='141']/*[@key='145']/*[@key='146']/*[@key='147']/*[@key='148']/*[@key='150']" toNamePath="Root/Object/batchProcessingDirectives/Object/batchProcessingOption/Array/ArrayElement1/Object/name" toType="profile"/>
      </Mappings>
      <Functions optimizeExecutionOrder="true"/>
      <Defaults>
        <Default toKey="149" value="true"/>
        <Default toKey="159" value="value"/>
        <Default toKey="179" value="value"/>
        <Default toKey="165" value="value"/>
      </Defaults>
      <DocumentCacheJoins/>
    </Map>
  </bns:object>
</Component>